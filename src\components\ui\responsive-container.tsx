import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  mobileLayout?: 'stack' | 'grid' | 'flex';
  desktopLayout?: 'grid' | 'flex' | 'sidebar';
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className,
  mobileLayout = 'stack',
  desktopLayout = 'grid',
  maxWidth = 'full',
  padding = 'md'
}) => {
  const mobileClasses = {
    stack: 'flex flex-col space-y-4',
    grid: 'grid grid-cols-1 gap-4',
    flex: 'flex flex-col space-y-4'
  };

  const desktopClasses = {
    grid: 'lg:grid lg:grid-cols-2 xl:grid-cols-3 lg:gap-6',
    flex: 'lg:flex lg:flex-row lg:space-x-6 lg:space-y-0',
    sidebar: 'lg:grid lg:grid-cols-4 lg:gap-6'
  };

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    '2xl': 'max-w-7xl',
    full: 'max-w-full'
  };

  const paddingClasses = {
    none: '',
    sm: 'p-2 sm:p-4',
    md: 'p-4 sm:p-6 lg:p-8',
    lg: 'p-6 sm:p-8 lg:p-12'
  };

  return (
    <div className={cn(
      'w-full mx-auto',
      mobileClasses[mobileLayout],
      desktopClasses[desktopLayout],
      maxWidthClasses[maxWidth],
      paddingClasses[padding],
      className
    )}>
      {children}
    </div>
  );
};

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    mobile: 1 | 2;
    tablet: 2 | 3 | 4;
    desktop: 2 | 3 | 4 | 5 | 6;
  };
  gap?: 'sm' | 'md' | 'lg';
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className,
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md'
}) => {
  const gapClasses = {
    sm: 'gap-2 sm:gap-3',
    md: 'gap-3 sm:gap-4 lg:gap-6',
    lg: 'gap-4 sm:gap-6 lg:gap-8'
  };

  const gridCols = `grid-cols-${cols.mobile} sm:grid-cols-${cols.tablet} lg:grid-cols-${cols.desktop}`;

  return (
    <div className={cn(
      'grid',
      gridCols,
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
};

interface ResponsiveCardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'glass' | 'bordered';
}

export const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  children,
  className,
  padding = 'md',
  variant = 'default'
}) => {
  const paddingClasses = {
    sm: 'p-3 sm:p-4',
    md: 'p-4 sm:p-6',
    lg: 'p-6 sm:p-8'
  };

  const variantClasses = {
    default: 'bg-white dark:bg-gray-800 rounded-lg shadow-sm',
    glass: 'bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20',
    bordered: 'bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700'
  };

  return (
    <div className={cn(
      variantClasses[variant],
      paddingClasses[padding],
      'w-full',
      className
    )}>
      {children}
    </div>
  );
};

interface ResponsiveButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical' | 'responsive';
  spacing?: 'sm' | 'md' | 'lg';
}

export const ResponsiveButtonGroup: React.FC<ResponsiveButtonGroupProps> = ({
  children,
  className,
  orientation = 'responsive',
  spacing = 'md'
}) => {
  const orientationClasses = {
    horizontal: 'flex flex-row',
    vertical: 'flex flex-col',
    responsive: 'flex flex-col sm:flex-row'
  };

  const spacingClasses = {
    sm: orientation === 'responsive' ? 'space-y-2 sm:space-y-0 sm:space-x-2' : 
        orientation === 'horizontal' ? 'space-x-2' : 'space-y-2',
    md: orientation === 'responsive' ? 'space-y-3 sm:space-y-0 sm:space-x-3' : 
        orientation === 'horizontal' ? 'space-x-3' : 'space-y-3',
    lg: orientation === 'responsive' ? 'space-y-4 sm:space-y-0 sm:space-x-4' : 
        orientation === 'horizontal' ? 'space-x-4' : 'space-y-4'
  };

  return (
    <div className={cn(
      orientationClasses[orientation],
      spacingClasses[spacing],
      'w-full',
      className
    )}>
      {children}
    </div>
  );
};
