
import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Heart, Clock, Volume2, VolumeX, Play, Pause, RotateCcw,
  Smartphone, Droplets, Utensils, Moon, Bell, Zap,
  Brain, Lightbulb, Activity, TrendingUp, Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { GlassmorphCard } from '@/components/ui/glassmorphcard';

const WellnessTools = () => {
  const [activeTab, setActiveTab] = useState('breathing');
  const [breathingPattern, setBreathingPattern] = useState('4-7-8');
  const [breathingActive, setBreathingActive] = useState(false);
  const [breathingPhase, setBreathingPhase] = useState('inhale');
  const [breathingCount, setBreathingCount] = useState(4);
  const [meditationTime, setMeditationTime] = useState(300);
  const [meditationActive, setMeditationActive] = useState(false);
  const [meditationRemaining, setMeditationRemaining] = useState(300);
  const [activeSoundscape, setActiveSoundscape] = useState(null);
  const [detoxTime, setDetoxTime] = useState(0);
  const [detoxActive, setDetoxActive] = useState(false);
  const [waterIntake, setWaterIntake] = useState(0);
  const [moodEntries, setMoodEntries] = useState([]);
  const [sleepLog, setSleepLog] = useState([]);
  const [cbtThoughts, setCbtThoughts] = useState({
    situation: '',
    thoughts: '',
    emotions: '',
    evidence: '',
    balanced: ''
  });

  const breathingTimer = useRef(null);
  const meditationTimer = useRef(null);
  const detoxTimer = useRef(null);

  const breathingPatterns = {
    '4-7-8': { inhale: 4, hold: 7, exhale: 8 },
    'box': { inhale: 4, hold: 4, exhale: 4, hold2: 4 },
    'custom': { inhale: 4, hold: 4, exhale: 6 }
  };

  const soundscapes = [
    { id: 'rain', name: 'Rain', icon: '🌧️', description: 'Gentle rainfall' },
    { id: 'cafe', name: 'Café', icon: '☕', description: 'Coffee shop ambiance' },
    { id: 'ocean', name: 'Ocean', icon: '🌊', description: 'Ocean waves' },
    { id: 'forest', name: 'Forest', icon: '🌲', description: 'Birds and nature' },
    { id: 'binaural', name: 'Focus', icon: '🧠', description: 'Binaural beats' }
  ];

  const moodOptions = [
    { value: 'great', emoji: '😄', label: 'Great' },
    { value: 'good', emoji: '😊', label: 'Good' },
    { value: 'okay', emoji: '😐', label: 'Okay' },
    { value: 'low', emoji: '😔', label: 'Low' },
    { value: 'anxious', emoji: '😰', label: 'Anxious' }
  ];

  useEffect(() => {
    return () => {
      if (breathingTimer.current) clearInterval(breathingTimer.current);
      if (meditationTimer.current) clearInterval(meditationTimer.current);
      if (detoxTimer.current) clearInterval(detoxTimer.current);
    };
  }, []);

  const startBreathing = () => {
    setBreathingActive(true);
    const pattern = breathingPatterns[breathingPattern];
    let phase = 'inhale';
    let count = pattern.inhale;
    
    setBreathingPhase(phase);
    setBreathingCount(count);

    breathingTimer.current = setInterval(() => {
      setBreathingCount(prev => {
        if (prev <= 1) {
          if (phase === 'inhale') {
            phase = 'hold';
            return pattern.hold || pattern.exhale;
          } else if (phase === 'hold') {
            phase = 'exhale';
            return pattern.exhale;
          } else if (phase === 'exhale') {
            if (pattern.hold2) {
              phase = 'hold2';
              return pattern.hold2;
            } else {
              phase = 'inhale';
              return pattern.inhale;
            }
          } else {
            phase = 'inhale';
            return pattern.inhale;
          }
        }
        return prev - 1;
      });
      setBreathingPhase(phase);
    }, 1000);
  };

  const stopBreathing = () => {
    setBreathingActive(false);
    if (breathingTimer.current) clearInterval(breathingTimer.current);
  };

  const startMeditation = () => {
    setMeditationActive(true);
    meditationTimer.current = setInterval(() => {
      setMeditationRemaining(prev => {
        if (prev <= 1) {
          setMeditationActive(false);
          return meditationTime;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const startDetox = (minutes) => {
    setDetoxTime(minutes * 60);
    setDetoxActive(true);
    detoxTimer.current = setInterval(() => {
      setDetoxTime(prev => {
        if (prev <= 1) {
          setDetoxActive(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const addWater = (amount) => {
    setWaterIntake(prev => prev + amount);
  };

  const addMoodEntry = (mood) => {
    const entry = {
      id: Date.now(),
      mood,
      timestamp: new Date(),
      date: new Date().toDateString()
    };
    setMoodEntries(prev => [entry, ...prev]);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const tabs = [
    { id: 'breathing', label: 'Breathing', icon: Heart },
    { id: 'meditation', label: 'Meditation', icon: Clock },
    { id: 'soundscape', label: 'Sounds', icon: Volume2 },
    { id: 'detox', label: 'Detox', icon: Smartphone },
    { id: 'wellness', label: 'Wellness', icon: Activity },
    { id: 'cbt', label: 'CBT Journal', icon: Lightbulb },
    { id: 'insights', label: 'Insights', icon: Brain }
  ];

  return (
    <div className="min-h-screen p-4 pb-24 bg-gradient-to-br from-green-50 via-blue-50 to-purple-50">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <h1 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2">
          Wellness Tools 🌱
        </h1>
        <p className="text-gray-600">Take care of your mind and body</p>
      </motion.div>

      {/* Tab Navigation */}
      <div className="flex overflow-x-auto mb-6 space-x-2 pb-2">
        {tabs.map((tab) => (
          <Button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            variant={activeTab === tab.id ? 'default' : 'outline'}
            className={`flex items-center space-x-2 whitespace-nowrap ${
              activeTab === tab.id 
                ? 'bg-gradient-to-r from-green-500 to-blue-500 text-white' 
                : 'bg-white/80'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span>{tab.label}</span>
          </Button>
        ))}
      </div>

      <AnimatePresence mode="wait">
        {activeTab === 'breathing' && (
          <motion.div
            key="breathing"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <GlassmorphCard className="p-6">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <Heart className="w-5 h-5 mr-2 text-red-500" />
                Breathing Coach
              </h2>
              
              <div className="grid grid-cols-3 gap-4 mb-6">
                {Object.keys(breathingPatterns).map(pattern => (
                  <Button
                    key={pattern}
                    onClick={() => setBreathingPattern(pattern)}
                    variant={breathingPattern === pattern ? 'default' : 'outline'}
                    className="text-sm"
                  >
                    {pattern.toUpperCase()}
                  </Button>
                ))}
              </div>

              <div className="text-center">
                <motion.div
                  className="w-32 h-32 mx-auto mb-6 rounded-full border-4 border-blue-500 flex items-center justify-center"
                  animate={breathingActive ? {
                    scale: breathingPhase === 'inhale' ? 1.2 : breathingPhase === 'exhale' ? 0.8 : 1
                  } : {}}
                  transition={{ duration: 1, ease: "easeInOut" }}
                >
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{breathingCount}</div>
                    <div className="text-sm text-gray-600 capitalize">{breathingPhase}</div>
                  </div>
                </motion.div>

                <div className="flex justify-center space-x-4">
                  {!breathingActive ? (
                    <Button onClick={startBreathing} className="bg-green-500 hover:bg-green-600">
                      <Play className="w-4 h-4 mr-2" />
                      Start
                    </Button>
                  ) : (
                    <Button onClick={stopBreathing} variant="outline">
                      <Pause className="w-4 h-4 mr-2" />
                      Stop
                    </Button>
                  )}
                </div>
              </div>
            </GlassmorphCard>
          </motion.div>
        )}

        {activeTab === 'meditation' && (
          <motion.div
            key="meditation"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <GlassmorphCard className="p-6">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <Clock className="w-5 h-5 mr-2 text-purple-500" />
                Meditation Timer
              </h2>

              <div className="grid grid-cols-4 gap-4 mb-6">
                {[5, 10, 15, 20].map(minutes => (
                  <Button
                    key={minutes}
                    onClick={() => setMeditationTime(minutes * 60)}
                    variant={meditationTime === minutes * 60 ? 'default' : 'outline'}
                    className="text-sm"
                  >
                    {minutes}m
                  </Button>
                ))}
              </div>

              <div className="text-center">
                <div className="text-4xl font-bold text-purple-600 mb-4">
                  {formatTime(meditationRemaining)}
                </div>
                <Progress 
                  value={((meditationTime - meditationRemaining) / meditationTime) * 100} 
                  className="mb-6"
                />
                
                <div className="flex justify-center space-x-4">
                  {!meditationActive ? (
                    <Button onClick={startMeditation} className="bg-purple-500 hover:bg-purple-600">
                      <Play className="w-4 h-4 mr-2" />
                      Start Meditation
                    </Button>
                  ) : (
                    <Button 
                      onClick={() => {
                        setMeditationActive(false);
                        if (meditationTimer.current) clearInterval(meditationTimer.current);
                        setMeditationRemaining(meditationTime);
                      }}
                      variant="outline"
                    >
                      <Pause className="w-4 h-4 mr-2" />
                      Stop
                    </Button>
                  )}
                </div>
              </div>
            </GlassmorphCard>
          </motion.div>
        )}

        {activeTab === 'soundscape' && (
          <motion.div
            key="soundscape"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <GlassmorphCard className="p-6">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <Volume2 className="w-5 h-5 mr-2 text-blue-500" />
                Ambient Soundscapes
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {soundscapes.map(sound => (
                  <Card 
                    key={sound.id}
                    className={`cursor-pointer transition-all hover:shadow-lg ${
                      activeSoundscape === sound.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                    }`}
                    onClick={() => setActiveSoundscape(activeSoundscape === sound.id ? null : sound.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="text-2xl">{sound.icon}</div>
                        <div>
                          <h3 className="font-semibold">{sound.name}</h3>
                          <p className="text-sm text-gray-600">{sound.description}</p>
                        </div>
                        <div className="ml-auto">
                          {activeSoundscape === sound.id ? (
                            <VolumeX className="w-5 h-5 text-blue-500" />
                          ) : (
                            <Volume2 className="w-5 h-5 text-gray-400" />
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </GlassmorphCard>
          </motion.div>
        )}

        {activeTab === 'detox' && (
          <motion.div
            key="detox"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <GlassmorphCard className="p-6">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <Smartphone className="w-5 h-5 mr-2 text-orange-500" />
                Digital Detox Timer
              </h2>

              <div className="text-center">
                {!detoxActive ? (
                  <div>
                    <p className="text-gray-600 mb-6">Take a break from screens</p>
                    <div className="flex justify-center space-x-4">
                      {[15, 30, 60, 120].map(minutes => (
                        <Button
                          key={minutes}
                          onClick={() => startDetox(minutes)}
                          className="bg-orange-500 hover:bg-orange-600"
                        >
                          {minutes}m
                        </Button>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="text-4xl font-bold text-orange-600 mb-4">
                      {formatTime(detoxTime)}
                    </div>
                    <p className="text-gray-600 mb-4">Detox in progress...</p>
                    <Button 
                      onClick={() => {
                        setDetoxActive(false);
                        if (detoxTimer.current) clearInterval(detoxTimer.current);
                        setDetoxTime(0);
                      }}
                      variant="outline"
                    >
                      End Detox
                    </Button>
                  </div>
                )}
              </div>
            </GlassmorphCard>
          </motion.div>
        )}

        {activeTab === 'wellness' && (
          <motion.div
            key="wellness"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Water Tracker */}
            <GlassmorphCard className="p-6">
              <h3 className="text-lg font-bold mb-4 flex items-center">
                <Droplets className="w-5 h-5 mr-2 text-blue-500" />
                Water Tracker
              </h3>
              <div className="flex items-center justify-between mb-4">
                <span className="text-2xl font-bold text-blue-600">{waterIntake}ml</span>
                <span className="text-sm text-gray-600">Daily Goal: 2000ml</span>
              </div>
              <Progress value={(waterIntake / 2000) * 100} className="mb-4" />
              <div className="flex space-x-2">
                {[250, 500, 750].map(amount => (
                  <Button
                    key={amount}
                    onClick={() => addWater(amount)}
                    size="sm"
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    +{amount}ml
                  </Button>
                ))}
              </div>
            </GlassmorphCard>

            {/* Mood Check-in */}
            <GlassmorphCard className="p-6">
              <h3 className="text-lg font-bold mb-4 flex items-center">
                <Heart className="w-5 h-5 mr-2 text-pink-500" />
                Mood Check-in
              </h3>
              <div className="grid grid-cols-5 gap-2 mb-4">
                {moodOptions.map(mood => (
                  <Button
                    key={mood.value}
                    onClick={() => addMoodEntry(mood.value)}
                    variant="outline"
                    className="flex flex-col items-center p-3 h-auto"
                  >
                    <span className="text-2xl mb-1">{mood.emoji}</span>
                    <span className="text-xs">{mood.label}</span>
                  </Button>
                ))}
              </div>
              {moodEntries.length > 0 && (
                <div className="text-sm text-gray-600">
                  Last entry: {moodEntries[0].mood} at {moodEntries[0].timestamp.toLocaleTimeString()}
                </div>
              )}
            </GlassmorphCard>

            {/* Sleep Log */}
            <GlassmorphCard className="p-6">
              <h3 className="text-lg font-bold mb-4 flex items-center">
                <Moon className="w-5 h-5 mr-2 text-indigo-500" />
                Sleep Log
              </h3>
              <div className="text-center">
                <Button className="bg-indigo-500 hover:bg-indigo-600">
                  Log Sleep Hours
                </Button>
              </div>
            </GlassmorphCard>
          </motion.div>
        )}

        {activeTab === 'cbt' && (
          <motion.div
            key="cbt"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <GlassmorphCard className="p-6">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <Lightbulb className="w-5 h-5 mr-2 text-yellow-500" />
                CBT Thought Journal
              </h2>
              <p className="text-gray-600 mb-6">
                Challenge negative thoughts with cognitive behavioral therapy techniques
              </p>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Situation</label>
                  <textarea
                    className="w-full p-3 border rounded-lg resize-none"
                    rows={2}
                    placeholder="What happened? Describe the situation..."
                    value={cbtThoughts.situation || ''}
                    onChange={(e) => setCbtThoughts(prev => ({...prev, situation: e.target.value}))}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Automatic Thoughts</label>
                  <textarea
                    className="w-full p-3 border rounded-lg resize-none"
                    rows={2}
                    placeholder="What thoughts went through your mind?"
                    value={cbtThoughts.thoughts || ''}
                    onChange={(e) => setCbtThoughts(prev => ({...prev, thoughts: e.target.value}))}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Emotions</label>
                  <textarea
                    className="w-full p-3 border rounded-lg resize-none"
                    rows={2}
                    placeholder="How did you feel? Rate intensity 1-10"
                    value={cbtThoughts.emotions || ''}
                    onChange={(e) => setCbtThoughts(prev => ({...prev, emotions: e.target.value}))}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Evidence For/Against</label>
                  <textarea
                    className="w-full p-3 border rounded-lg resize-none"
                    rows={3}
                    placeholder="What evidence supports or contradicts this thought?"
                    value={cbtThoughts.evidence || ''}
                    onChange={(e) => setCbtThoughts(prev => ({...prev, evidence: e.target.value}))}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Balanced Thought</label>
                  <textarea
                    className="w-full p-3 border rounded-lg resize-none"
                    rows={2}
                    placeholder="What's a more balanced, realistic thought?"
                    value={cbtThoughts.balanced || ''}
                    onChange={(e) => setCbtThoughts(prev => ({...prev, balanced: e.target.value}))}
                  />
                </div>

                <Button
                  onClick={() => {
                    // Save CBT entry logic here
                    console.log('CBT entry saved:', cbtThoughts);
                    setCbtThoughts({});
                  }}
                  className="w-full bg-yellow-500 hover:bg-yellow-600"
                >
                  Save Thought Record
                </Button>
              </div>
            </GlassmorphCard>

            {/* CBT Techniques */}
            <GlassmorphCard className="p-6">
              <h3 className="text-lg font-bold mb-4">CBT Techniques</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4">
                  <h4 className="font-semibold mb-2">🔍 Thought Challenging</h4>
                  <p className="text-sm text-gray-600">
                    Question the accuracy and helpfulness of negative thoughts
                  </p>
                </Card>
                <Card className="p-4">
                  <h4 className="font-semibold mb-2">⚖️ Evidence Examination</h4>
                  <p className="text-sm text-gray-600">
                    Look for evidence that supports or contradicts your thoughts
                  </p>
                </Card>
                <Card className="p-4">
                  <h4 className="font-semibold mb-2">🎯 Behavioral Experiments</h4>
                  <p className="text-sm text-gray-600">
                    Test your predictions through real-world actions
                  </p>
                </Card>
                <Card className="p-4">
                  <h4 className="font-semibold mb-2">🧘 Mindful Awareness</h4>
                  <p className="text-sm text-gray-600">
                    Observe thoughts without judgment or immediate reaction
                  </p>
                </Card>
              </div>
            </GlassmorphCard>
          </motion.div>
        )}

        {activeTab === 'insights' && (
          <motion.div
            key="insights"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <GlassmorphCard className="p-6">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <Brain className="w-5 h-5 mr-2 text-purple-500" />
                Wellness Insights
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4">
                  <h3 className="font-semibold mb-2 flex items-center">
                    <TrendingUp className="w-4 h-4 mr-2 text-green-500" />
                    Mood Trends
                  </h3>
                  <p className="text-sm text-gray-600">
                    {moodEntries.length > 0 
                      ? `${moodEntries.length} mood entries logged`
                      : 'No mood data yet'
                    }
                  </p>
                </Card>

                <Card className="p-4">
                  <h3 className="font-semibold mb-2 flex items-center">
                    <Droplets className="w-4 h-4 mr-2 text-blue-500" />
                    Hydration
                  </h3>
                  <p className="text-sm text-gray-600">
                    {((waterIntake / 2000) * 100).toFixed(0)}% of daily goal
                  </p>
                </Card>
              </div>
            </GlassmorphCard>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default WellnessTools;
