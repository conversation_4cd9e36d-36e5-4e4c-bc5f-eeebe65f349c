@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile-first responsive utilities */
@layer utilities {
  /* Safe area handling for mobile devices */
  .safe-area-pb {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-pt {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-pl {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-pr {
    padding-right: env(safe-area-inset-right);
  }

  /* Custom scrollbar for webkit browsers */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(168 85 247) rgb(243 244 246);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(243 244 246);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(168 85 247);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(147 51 234);
  }

  /* Text truncation utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Touch-friendly button sizing */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Responsive text sizing */
  .text-responsive-xs {
    font-size: clamp(0.75rem, 2vw, 0.875rem);
  }

  .text-responsive-sm {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
  }

  .text-responsive-base {
    font-size: clamp(1rem, 3vw, 1.125rem);
  }

  .text-responsive-lg {
    font-size: clamp(1.125rem, 3.5vw, 1.25rem);
  }

  .text-responsive-xl {
    font-size: clamp(1.25rem, 4vw, 1.5rem);
  }

  .text-responsive-2xl {
    font-size: clamp(1.5rem, 5vw, 2rem);
  }

  .text-responsive-3xl {
    font-size: clamp(1.875rem, 6vw, 2.5rem);
  }
}

/* Eye Protective Mode */
.eye-protective {
  --bg-white: #f8f9fa;
  --bg-gray-50: #f1f3f4;
  --bg-gray-100: #e8eaed;
  --text-white: #f8f9fa;
}

.eye-protective .bg-white {
  background-color: var(--bg-white) !important;
}

.eye-protective .bg-gray-50 {
  background-color: var(--bg-gray-50) !important;
}

.eye-protective .bg-white\/80 {
  background-color: rgba(248, 249, 250, 0.8) !important;
}

.eye-protective .bg-white\/90 {
  background-color: rgba(248, 249, 250, 0.9) !important;
}

.eye-protective .text-white {
  color: var(--text-white) !important;
}

/* Mobile viewport fixes */
@supports (-webkit-touch-callout: none) {
  .min-h-screen {
    min-height: -webkit-fill-available;
  }
}

/* Prevent zoom on input focus on iOS */
@media screen and (max-width: 768px) {
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  textarea,
  select {
    font-size: 16px !important;
  }
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}