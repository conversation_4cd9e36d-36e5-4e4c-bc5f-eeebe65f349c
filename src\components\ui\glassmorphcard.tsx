
import * as React from 'react';
import { cn } from '@/lib/utils';

interface GlassmorphCardProps extends React.HTMLAttributes<HTMLDivElement> {}

const GlassmorphCard = React.forwardRef<HTMLDivElement, GlassmorphCardProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "backdrop-blur-md bg-white/10 border border-white/20 rounded-2xl shadow-xl",
        className
      )}
      {...props}
    />
  )
);

GlassmorphCard.displayName = 'GlassmorphCard';

export { GlassmorphCard };
