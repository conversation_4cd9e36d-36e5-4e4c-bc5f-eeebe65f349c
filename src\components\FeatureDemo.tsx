import { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, CheckCircle, Target, Coffee, Heart, Timer, Plus, Settings, Zap, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const FeatureDemo = ({ onBack }: { onBack: () => void }) => {
  const [activeFeature, setActiveFeature] = useState<string | null>(null);

  const features = [
    {
      id: 'tasks',
      icon: CheckCircle,
      title: 'Interactive Tasks',
      description: 'Tap to complete tasks, add new ones, track progress in real-time',
      color: 'from-blue-500 to-cyan-500',
      demo: [
        '✅ Tap any task to mark complete/incomplete',
        '✅ Progress bars update instantly',
        '✅ Add new tasks via FAB',
        '✅ Task completion increases total count',
        '✅ First task of day increases streak'
      ]
    },
    {
      id: 'habits',
      icon: Coffee,
      title: 'Habit Tracking',
      description: 'Build streaks, track daily habits, see real progress',
      color: 'from-green-500 to-emerald-500',
      demo: [
        '✅ Tap habits to mark complete',
        '✅ Streaks increase/decrease in real-time',
        '✅ Progress percentage updates live',
        '✅ Add new habits via FAB',
        '✅ Visual feedback with animations'
      ]
    },
    {
      id: 'pomodoro',
      icon: Timer,
      title: 'Pomodoro Timer',
      description: 'Functional 25-minute timer with focus time tracking',
      color: 'from-orange-500 to-red-500',
      demo: [
        '✅ Real countdown timer (25 minutes)',
        '✅ Play/pause functionality',
        '✅ Progress bar shows completion',
        '✅ Adds 0.42h to focus time when complete',
        '✅ Browser notifications on completion',
        '✅ Reset and change task options'
      ]
    },
    {
      id: 'mood',
      icon: Heart,
      title: 'Mood Tracking',
      description: 'Interactive mood updates with energy levels',
      color: 'from-pink-500 to-rose-500',
      demo: [
        '✅ Tap mood card to cycle through moods',
        '✅ Energy levels change with mood',
        '✅ Emoji animations on updates',
        '✅ Persistent mood state',
        '✅ Update via FAB or direct tap'
      ]
    },
    {
      id: 'goals',
      icon: Target,
      title: 'Goal Progress',
      description: 'Interactive goal tracking with visual progress rings',
      color: 'from-purple-500 to-indigo-500',
      demo: [
        '✅ Tap goals to update progress (0-100%)',
        '✅ Animated SVG progress rings',
        '✅ Overall progress calculation',
        '✅ Add new goals via FAB',
        '✅ Real-time percentage updates'
      ]
    },
    {
      id: 'stats',
      icon: Zap,
      title: 'Live Statistics',
      description: 'Real-time calculations and progress tracking',
      color: 'from-yellow-500 to-orange-500',
      demo: [
        '✅ Task completion percentage',
        '✅ Habit completion percentage',
        '✅ Focus time accumulation',
        '✅ Daily streak tracking',
        '✅ Total tasks completed counter',
        '✅ All stats update instantly'
      ]
    },
    {
      id: 'persistence',
      icon: Settings,
      title: 'Data Persistence',
      description: 'Everything saves automatically to localStorage',
      color: 'from-gray-500 to-slate-500',
      demo: [
        '✅ All data persists across sessions',
        '✅ Refresh page - data remains',
        '✅ Close/reopen app - data intact',
        '✅ Real-time localStorage updates',
        '✅ Reset option to start fresh'
      ]
    },
    {
      id: 'responsive',
      icon: Star,
      title: 'Mobile Responsive',
      description: 'Perfect on all devices with touch optimization',
      color: 'from-indigo-500 to-purple-500',
      demo: [
        '✅ Mobile-first design',
        '✅ Touch-friendly interactions',
        '✅ Responsive breakpoints',
        '✅ Safe area support (iOS)',
        '✅ Optimized spacing and typography'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <Button
          onClick={onBack}
          variant="outline"
          className="rounded-2xl bg-white/90 backdrop-blur-sm border border-white/30"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to App
        </Button>
        <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
          Interactive Features Demo
        </h1>
        <div className="w-24" />
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 max-w-7xl mx-auto">
        {features.map((feature, index) => (
          <motion.div
            key={feature.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
            onClick={() => setActiveFeature(activeFeature === feature.id ? null : feature.id)}
            className="cursor-pointer"
          >
            <Card className={`h-full border-0 shadow-lg transition-all duration-300 ${
              activeFeature === feature.id 
                ? 'bg-white shadow-2xl scale-105' 
                : 'bg-white/80 backdrop-blur-sm hover:shadow-xl'
            }`}>
              <CardHeader className="pb-3">
                <div className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-3 shadow-lg`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-lg font-bold text-gray-800">
                  {feature.title}
                </CardTitle>
                <p className="text-sm text-gray-600">
                  {feature.description}
                </p>
              </CardHeader>
              
              {activeFeature === feature.id && (
                <CardContent className="pt-0">
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-2"
                  >
                    {feature.demo.map((item, idx) => (
                      <motion.div
                        key={idx}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: idx * 0.1 }}
                        className="text-sm text-gray-700 flex items-start space-x-2"
                      >
                        <span className="text-green-500 font-bold">✓</span>
                        <span>{item.replace('✅ ', '')}</span>
                      </motion.div>
                    ))}
                  </motion.div>
                </CardContent>
              )}
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Instructions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="max-w-4xl mx-auto mt-8"
      >
        <Card className="border-0 shadow-xl bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 backdrop-blur-xl">
          <CardContent className="p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4 text-center">
              🎯 How to Test All Features
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-800 mb-3">📱 Interactive Elements</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• <strong>Tasks:</strong> Tap checkboxes to complete/uncomplete</li>
                  <li>• <strong>Habits:</strong> Tap habit cards to build streaks</li>
                  <li>• <strong>Mood:</strong> Tap mood card to cycle through emotions</li>
                  <li>• <strong>Goals:</strong> Tap goal rings to update progress</li>
                  <li>• <strong>Timer:</strong> Use play/pause/reset buttons</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800 mb-3">⚡ Quick Actions (FAB)</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• <strong>Purple FAB:</strong> Tap to expand action menu</li>
                  <li>• <strong>Quick Task:</strong> Add new tasks instantly</li>
                  <li>• <strong>New Goal:</strong> Create custom goals</li>
                  <li>• <strong>New Habit:</strong> Add personal habits</li>
                  <li>• <strong>Settings Icon:</strong> Reset all data to start fresh</li>
                </ul>
              </div>
            </div>
            <div className="mt-6 p-4 bg-white/50 rounded-2xl">
              <p className="text-center text-sm text-gray-700">
                <strong>💡 Pro Tip:</strong> Everything starts from zero and increases with your interactions. 
                Complete a Pomodoro session to see focus time increase, complete tasks to build streaks, 
                and mark habits to see real progress tracking!
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default FeatureDemo;
