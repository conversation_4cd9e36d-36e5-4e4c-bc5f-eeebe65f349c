import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Target, Plus, Calendar, Clock, TrendingUp, Link,
  Star, CheckCircle, Circle, Edit3, Trash2, Eye,
  BarChart3, Archive, Zap, Hash, Flag
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { GlassmorphCard } from '@/components/ui/glassmorphcard';

const GoalProjectManagement = () => {
  const [activeTab, setActiveTab] = useState('goals');
  const [goals, setGoals] = useState([]);
  const [projects, setProjects] = useState([]);
  const [showGoalForm, setShowGoalForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState(null);
  const [selectedGoal, setSelectedGoal] = useState(null);

  useEffect(() => {
    const savedGoals = JSON.parse(localStorage.getItem('goals') || '[]');
    const savedProjects = JSON.parse(localStorage.getItem('projects') || '[]');
    setGoals(savedGoals);
    setProjects(savedProjects);
  }, []);

  const saveGoals = (updatedGoals) => {
    setGoals(updatedGoals);
    localStorage.setItem('goals', JSON.stringify(updatedGoals));
  };

  const goalCategories = [
    { id: 'health', name: 'Health & Fitness', color: 'bg-green-500', icon: '💪' },
    { id: 'career', name: 'Career', color: 'bg-blue-500', icon: '🎯' },
    { id: 'personal', name: 'Personal Growth', color: 'bg-purple-500', icon: '🌱' },
    { id: 'financial', name: 'Financial', color: 'bg-yellow-500', icon: '💰' },
    { id: 'relationships', name: 'Relationships', color: 'bg-pink-500', icon: '❤️' },
    { id: 'creative', name: 'Creative', color: 'bg-orange-500', icon: '🎨' }
  ];

  const goalTypes = ['Outcome', 'Process', 'Learning', 'Habit'];
  const priorityLevels = ['Low', 'Medium', 'High', 'Critical'];

  const createSMARTGoal = (formData) => {
    const newGoal = {
      id: Date.now(),
      title: formData.title,
      description: formData.description,
      category: formData.category,
      type: formData.type,
      priority: formData.priority,
      targetDate: formData.targetDate,
      progress: 0,
      milestones: formData.milestones || [],
      linkedHabits: formData.linkedHabits || [],
      linkedProjects: formData.linkedProjects || [],
      timeSpent: 0,
      timeBudgeted: Number(formData.timeBudgeted) || 0,
      isPublic: formData.isPublic || false,
      recurring: formData.recurring || false,
      visionStatement: formData.visionStatement || '',
      createdAt: new Date(),
      status: 'active'
    };

    const updatedGoals = [...goals, newGoal];
    saveGoals(updatedGoals);
    setShowGoalForm(false);
  };

  const updateGoalProgress = (goalId, progress) => {
    const updatedGoals = goals.map(goal => 
      goal.id === goalId ? { ...goal, progress: Math.min(100, Math.max(0, progress)) } : goal
    );
    saveGoals(updatedGoals);
  };

  const toggleMilestone = (goalId, milestoneIndex) => {
    const updatedGoals = goals.map(goal => {
      if (goal.id === goalId) {
        const updatedMilestones = goal.milestones.map((milestone, index) =>
          index === milestoneIndex ? { ...milestone, completed: !milestone.completed } : milestone
        );
        const completedCount = updatedMilestones.filter(m => m.completed).length;
        const newProgress = (completedCount / updatedMilestones.length) * 100;
        
        return { 
          ...goal, 
          milestones: updatedMilestones,
          progress: newProgress
        };
      }
      return goal;
    });
    saveGoals(updatedGoals);
  };

  const archiveGoal = (goalId) => {
    const updatedGoals = goals.map(goal => 
      goal.id === goalId ? { ...goal, status: 'archived', archivedAt: new Date() } : goal
    );
    saveGoals(updatedGoals);
  };

  const deleteGoal = (goalId) => {
    const updatedGoals = goals.filter(goal => goal.id !== goalId);
    saveGoals(updatedGoals);
  };

  const getGoalsByCategory = () => {
    const categorized = {};
    goalCategories.forEach(category => {
      categorized[category.id] = goals.filter(goal => 
        goal.category === category.id && goal.status === 'active'
      );
    });
    return categorized;
  };

  const getProgressStats = () => {
    const activeGoals = goals.filter(goal => goal.status === 'active');
    const completedGoals = activeGoals.filter(goal => goal.progress === 100);
    const avgProgress = activeGoals.length > 0 
      ? activeGoals.reduce((sum, goal) => sum + goal.progress, 0) / activeGoals.length 
      : 0;

    return {
      total: activeGoals.length,
      completed: completedGoals.length,
      inProgress: activeGoals.length - completedGoals.length,
      avgProgress: Math.round(avgProgress)
    };
  };

  const tabs = [
    { id: 'goals', label: 'Goals', icon: Target },
    { id: 'create', label: 'Create', icon: Plus },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'archive', label: 'Archive', icon: Archive }
  ];

  const stats = getProgressStats();
  const categorizedGoals = getGoalsByCategory();

  return (
    <div className="min-h-screen p-4 pb-24 bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2">
          Goal & Project Management 🎯
        </h1>
        <p className="text-gray-600">Turn your dreams into achievable milestones</p>
      </motion.div>

      {/* Quick Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6"
      >
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{stats.total}</div>
            <div className="text-sm text-gray-600">Active Goals</div>
          </CardContent>
        </Card>
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
            <div className="text-sm text-gray-600">Completed</div>
          </CardContent>
        </Card>
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.inProgress}</div>
            <div className="text-sm text-gray-600">In Progress</div>
          </CardContent>
        </Card>
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardContent className="p-4 text-center">	
            <div className="text-2xl font-bold text-orange-600">{stats.avgProgress}%</div>
            <div className="text-sm text-gray-600">Avg Progress</div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Tab Navigation */}
      <div className="flex overflow-x-auto mb-6 space-x-2 pb-2">
        {tabs.map((tab) => (
          <Button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            variant={activeTab === tab.id ? 'default' : 'outline'}
            className={`flex items-center space-x-2 whitespace-nowrap ${
              activeTab === tab.id 
                ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white' 
                : 'bg-white/80'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span>{tab.label}</span>
          </Button>
        ))}
      </div>

      <AnimatePresence mode="wait">
        {activeTab === 'goals' && (
          <motion.div
            key="goals"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {goalCategories.map(category => {
              const categoryGoals = categorizedGoals[category.id];
              if (categoryGoals.length === 0) return null;

              return (
                <GlassmorphCard key={category.id} className="p-6">
                  <h2 className="text-xl font-bold mb-4 flex items-center">
                    <div className={`w-8 h-8 ${category.color} rounded-lg flex items-center justify-center text-white mr-3`}>
                      <span>{category.icon}</span>
                    </div>
                    {category.name}
                  </h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {categoryGoals.map(goal => (
                      <Card 
                        key={goal.id} 
                        className="hover:shadow-lg transition-all cursor-pointer"
                        onClick={() => setSelectedGoal(goal)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex justify-between items-start">
                            <CardTitle className="text-lg">{goal.title}</CardTitle>
                            <div className="flex items-center space-x-2">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                goal.priority === 'Critical' ? 'bg-red-100 text-red-600' :
                                goal.priority === 'High' ? 'bg-orange-100 text-orange-600' :
                                goal.priority === 'Medium' ? 'bg-yellow-100 text-yellow-600' :
                                'bg-gray-100 text-gray-600'
                              }`}>
                                {goal.priority}
                              </span>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            <Progress value={goal.progress} className="h-2" />
                            <div className="flex justify-between text-sm text-gray-600">
                              <span>{goal.progress}% Complete</span>
                              <span>{goal.targetDate ? new Date(goal.targetDate).toLocaleDateString() : 'No deadline'}</span>
                            </div>
                            
                            {goal.milestones && goal.milestones.length > 0 && (
                              <div className="space-y-1">
                                <div className="text-sm font-medium text-gray-700">Milestones:</div>
                                {goal.milestones.slice(0, 3).map((milestone, index) => (
                                  <div 
                                    key={index} 
                                    className="flex items-center space-x-2 text-sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toggleMilestone(goal.id, index);
                                    }}
                                  >
                                    {milestone.completed ? (
                                      <CheckCircle className="w-4 h-4 text-green-500" />
                                    ) : (
                                      <Circle className="w-4 h-4 text-gray-400" />
                                    )}
                                    <span className={milestone.completed ? 'line-through text-gray-500' : ''}>
                                      {milestone.title}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            )}

                            <div className="flex justify-between items-center pt-2">
                              <div className="flex space-x-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setEditingGoal(goal);
                                  }}
                                >
                                  <Edit3 className="w-3 h-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    archiveGoal(goal.id);
                                  }}
                                >
                                  <Archive className="w-3 h-3" />
                                </Button>
                              </div>
                              <span className="text-xs text-gray-500">{goal.type}</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </GlassmorphCard>
              );
            })}

            {goals.filter(g => g.status === 'active').length === 0 && (
              <GlassmorphCard className="p-12 text-center">
                <Target className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">No Active Goals</h3>
                <p className="text-gray-500 mb-6">Start by creating your first SMART goal</p>
                <Button 
                  onClick={() => setActiveTab('create')}
                  className="bg-gradient-to-r from-purple-500 to-blue-500"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Your First Goal
                </Button>
              </GlassmorphCard>
            )}
          </motion.div>
        )}

        {activeTab === 'create' && (
          <motion.div
            key="create"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <GlassmorphCard className="p-6">
              <h2 className="text-xl font-bold mb-6 flex items-center">
                <Plus className="w-5 h-5 mr-2 text-purple-500" />
                Create SMART Goal
              </h2>

              <GoalForm 
                onSubmit={createSMARTGoal}
                categories={goalCategories}
                types={goalTypes}
                priorities={priorityLevels}
              />
            </GlassmorphCard>
          </motion.div>
        )}

        {activeTab === 'analytics' && (
          <motion.div
            key="analytics"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <GlassmorphCard className="p-6">
              <h2 className="text-xl font-bold mb-6 flex items-center">
                <BarChart3 className="w-5 h-5 mr-2 text-blue-500" />
                Goal Analytics
              </h2>

              {/* Category Breakdown */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="p-4">
                  <h3 className="font-semibold mb-4">Goals by Category</h3>
                  <div className="space-y-3">
                    {goalCategories.map(category => {
                      const count = categorizedGoals[category.id].length;
                      const percentage = stats.total > 0 ? (count / stats.total) * 100 : 0;
                      
                      return (
                        <div key={category.id} className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className={`w-3 h-3 ${category.color} rounded-full`}></div>
                            <span className="text-sm">{category.name}</span>
                          </div>
                          <div className="text-sm font-medium">{count} ({percentage.toFixed(0)}%)</div>
                        </div>
                      );
                    })}
                  </div>
                </Card>

                <Card className="p-4">
                  <h3 className="font-semibold mb-4">Progress Distribution</h3>
                  <div className="space-y-3">
                    {[
                      { label: 'Not Started (0%)', range: [0, 0] },
                      { label: 'Getting Started (1-25%)', range: [1, 25] },
                      { label: 'Making Progress (26-75%)', range: [26, 75] },
                      { label: 'Almost There (76-99%)', range: [76, 99] },
                      { label: 'Completed (100%)', range: [100, 100] }
                    ].map(item => {
                      const count = goals.filter(goal => 
                        goal.status === 'active' && 
                        goal.progress >= item.range[0] && 
                        goal.progress <= item.range[1]
                      ).length;
                      
                      return (
                        <div key={item.label} className="flex justify-between">
                          <span className="text-sm">{item.label}</span>
                          <span className="text-sm font-medium">{count}</span>
                        </div>
                      );
                    })}
                  </div>
                </Card>
              </div>
            </GlassmorphCard>
          </motion.div>
        )}

        {activeTab === 'archive' && (
          <motion.div
            key="archive"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <GlassmorphCard className="p-6">
              <h2 className="text-xl font-bold mb-6 flex items-center">
                <Archive className="w-5 h-5 mr-2 text-gray-500" />
                Archived Goals
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {goals.filter(goal => goal.status === 'archived').map(goal => (
                  <Card key={goal.id} className="bg-gray-50">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg text-gray-700">{goal.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <Progress value={goal.progress} className="h-2" />
                        <div className="flex justify-between text-sm text-gray-600">
                          <span>{goal.progress}% Complete</span>
                          <span>
                            Archived: {goal.archivedAt ? new Date(goal.archivedAt).toLocaleDateString() : 'Unknown'}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {goals.filter(goal => goal.status === 'archived').length === 0 && (
                <div className="text-center py-12">
                  <Archive className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No archived goals yet</p>
                </div>
              )}
            </GlassmorphCard>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Goal Detail Modal */}
      {selectedGoal && (
        <GoalDetailModal 
          goal={selectedGoal}
          onClose={() => setSelectedGoal(null)}
          onUpdate={(updatedGoal) => {
            const updatedGoals = goals.map(g => g.id === updatedGoal.id ? updatedGoal : g);
            saveGoals(updatedGoals);
            setSelectedGoal(updatedGoal);
          }}
        />
      )}
    </div>
  );
};

// Goal Form Component
const GoalForm = ({ onSubmit, categories, types, priorities }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: categories[0].id,
    type: types[0],
    priority: priorities[1],
    targetDate: '',
    milestones: [],
    visionStatement: '',
    timeBudgeted: '0',
    isPublic: false,
    recurring: false
  });

  const [newMilestone, setNewMilestone] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
    setFormData({
      title: '',
      description: '',
      category: categories[0].id,
      type: types[0],
      priority: priorities[1],
      targetDate: '',
      milestones: [],
      visionStatement: '',
      timeBudgeted: '0',
      isPublic: false,
      recurring: false
    });
  };

  const addMilestone = () => {
    if (newMilestone.trim()) {
      setFormData(prev => ({
        ...prev,
        milestones: [...prev.milestones, { title: newMilestone, completed: false }]
      }));
      setNewMilestone('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">Goal Title*</label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-purple-500"
            placeholder="What do you want to achieve?"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Category</label>
          <select
            value={formData.category}
            onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
            className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-purple-500"
          >
            {categories.map(cat => (
              <option key={cat.id} value={cat.id}>{cat.name}</option>
            ))}
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Description</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-purple-500"
          rows="3"
          placeholder="Describe your goal in detail..."
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">Type</label>
          <select
            value={formData.type}
            onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
            className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-purple-500"
          >
            {types.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Priority</label>
          <select
            value={formData.priority}
            onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value }))}
            className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-purple-500"
          >
            {priorities.map(priority => (
              <option key={priority} value={priority}>{priority}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Target Date</label>
          <input
            type="date"
            value={formData.targetDate}
            onChange={(e) => setFormData(prev => ({ ...prev, targetDate: e.target.value }))}
            className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-purple-500"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Milestones</label>
        <div className="flex space-x-2 mb-3">
          <input
            type="text"
            value={newMilestone}
            onChange={(e) => setNewMilestone(e.target.value)}
            className="flex-1 p-3 border rounded-lg focus:ring-2 focus:ring-purple-500"
            placeholder="Add a milestone..."
          />
          <Button type="button" onClick={addMilestone} className="bg-purple-500 hover:bg-purple-600">
            <Plus className="w-4 h-4" />
          </Button>
        </div>
        {formData.milestones.length > 0 && (
          <div className="space-y-2">
            {formData.milestones.map((milestone, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                <span className="text-sm">{milestone.title}</span>
                <Button
                  type="button"
                  size="sm"
                  variant="outline"
                  onClick={() => setFormData(prev => ({
                    ...prev,
                    milestones: prev.milestones.filter((_, i) => i !== index)
                  }))}
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Vision Statement</label>
        <textarea
          value={formData.visionStatement}
          onChange={(e) => setFormData(prev => ({ ...prev, visionStatement: e.target.value }))}
          className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-purple-500"
          rows="2"
          placeholder="Describe your vision when this goal is achieved..."
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Time Budget (hours)</label>
        <input
          type="number"
          value={formData.timeBudgeted}
          onChange={(e) => setFormData(prev => ({ ...prev, timeBudgeted: e.target.value }))}
          className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-purple-500"
          placeholder="How many hours will you dedicate?"
          min="0"
        />
      </div>

      <div className="flex justify-end">
        <Button type="submit" className="bg-gradient-to-r from-purple-500 to-blue-500">
          Create Goal
        </Button>
      </div>
    </form>
  );
};

// Goal Detail Modal Component
const GoalDetailModal = ({ goal, onClose, onUpdate }) => {
  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-start mb-6">
            <h2 className="text-2xl font-bold">{goal.title}</h2>
            <Button variant="outline" onClick={onClose}>
              ✕
            </Button>
          </div>

          <div className="space-y-6">
            <div>
              <h3 className="font-semibold mb-2">Progress</h3>
              <Progress value={goal.progress} className="h-3 mb-2" />
              <div className="flex justify-between text-sm text-gray-600">
                <span>{goal.progress}% Complete</span>
                <span>Target: {goal.targetDate ? new Date(goal.targetDate).toLocaleDateString() : 'No deadline'}</span>
              </div>
            </div>

            {goal.description && (
              <div>
                <h3 className="font-semibold mb-2">Description</h3>
                <p className="text-gray-700">{goal.description}</p>
              </div>
            )}

            {goal.visionStatement && (
              <div>
                <h3 className="font-semibold mb-2">Vision Statement</h3>
                <p className="text-gray-700 italic">{goal.visionStatement}</p>
              </div>
            )}

            {goal.milestones && goal.milestones.length > 0 && (
              <div>
                <h3 className="font-semibold mb-2">Milestones</h3>
                <div className="space-y-2">
                  {goal.milestones.map((milestone, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      {milestone.completed ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <Circle className="w-5 h-5 text-gray-400" />
                      )}
                      <span className={milestone.completed ? 'line-through text-gray-500' : ''}>
                        {milestone.title}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-2">Category</h3>
                <p className="text-gray-700 capitalize">{goal.category}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Priority</h3>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  goal.priority === 'Critical' ? 'bg-red-100 text-red-600' :
                  goal.priority === 'High' ? 'bg-orange-100 text-orange-600' :
                  goal.priority === 'Medium' ? 'bg-yellow-100 text-yellow-600' :
                  'bg-gray-100 text-gray-600'
                }`}>
                  {goal.priority}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GoalProjectManagement;
