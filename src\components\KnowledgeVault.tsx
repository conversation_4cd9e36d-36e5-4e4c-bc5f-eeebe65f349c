
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BookO<PERSON>, FolderPlus, FileText, Brain, Link, Globe,
  Camera, Tags, Search, Download, Plus, Trash2, Edit3,
  Star, ArrowRight, Archive, Hash, Eye, Share, Upload,
  ExternalLink, Copy, Zap, Network, FileDown, Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { GlassmorphCard } from '@/components/ui/glassmorphcard';

const KnowledgeVault = () => {
  const [activeTab, setActiveTab] = useState('notes');
  const [folders, setFolders] = useState([]);
  const [notes, setNotes] = useState([]);
  const [flashcards, setFlashcards] = useState([]);
  const [webClips, setWebClips] = useState([]);
  const [selectedFolder, setSelectedFolder] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showNoteEditor, setShowNoteEditor] = useState(false);
  const [editingNote, setEditingNote] = useState(null);

  useEffect(() => {
    const savedFolders = JSON.parse(localStorage.getItem('knowledgeFolders') || '[]');
    const savedNotes = JSON.parse(localStorage.getItem('knowledgeNotes') || '[]');
    const savedFlashcards = JSON.parse(localStorage.getItem('knowledgeFlashcards') || '[]');
    const savedWebClips = JSON.parse(localStorage.getItem('knowledgeWebClips') || '[]');
    
    setFolders(savedFolders);
    setNotes(savedNotes);
    setFlashcards(savedFlashcards);
    setWebClips(savedWebClips);
  }, []);

  const saveData = (type, data) => {
    localStorage.setItem(`knowledge${type}`, JSON.stringify(data));
  };

  const createFolder = (name, color = 'bg-blue-500') => {
    const newFolder = {
      id: Date.now(),
      name,
      color,
      createdAt: new Date(),
      noteCount: 0
    };
    const updatedFolders = [...folders, newFolder];
    setFolders(updatedFolders);
    saveData('Folders', updatedFolders);
  };

  const createNote = (noteData) => {
    const newNote = {
      id: Date.now(),
      title: noteData.title,
      content: noteData.content,
      folderId: noteData.folderId,
      tags: noteData.tags || [],
      links: noteData.links || [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    const updatedNotes = [...notes, newNote];
    setNotes(updatedNotes);
    saveData('Notes', updatedNotes);
    setShowNoteEditor(false);
  };

  const createFlashcard = (cardData) => {
    const newCard = {
      id: Date.now(),
      front: cardData.front,
      back: cardData.back,
      folderId: cardData.folderId,
      difficulty: 0,
      nextReview: new Date(),
      reviewCount: 0,
      createdAt: new Date()
    };
    const updatedCards = [...flashcards, newCard];
    setFlashcards(updatedCards);
    saveData('Flashcards', updatedCards);
  };

  const addWebClip = (clipData) => {
    const newClip = {
      id: Date.now(),
      title: clipData.title,
      url: clipData.url,
      content: clipData.content,
      screenshot: clipData.screenshot,
      tags: clipData.tags || [],
      folderId: clipData.folderId,
      createdAt: new Date()
    };
    const updatedClips = [...webClips, newClip];
    setWebClips(updatedClips);
    saveData('WebClips', updatedClips);
  };

  const tabs = [
    { id: 'notes', label: 'Notes', icon: FileText, count: notes.length },
    { id: 'flashcards', label: 'Flashcards', icon: Brain, count: flashcards.length },
    { id: 'webclips', label: 'Web Clips', icon: Globe, count: webClips.length },
    { id: 'folders', label: 'Folders', icon: BookOpen, count: folders.length }
  ];

  const filteredNotes = notes.filter(note =>
    note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    note.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
    note.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Export functions
  const exportToNotion = () => {
    alert('Notion export feature coming soon!');
  };

  const exportToObsidian = () => {
    const obsidianFormat = notes.map(note => {
      let content = `# ${note.title}\n\n`;
      content += `Created: ${new Date(note.createdAt).toLocaleDateString()}\n`;
      if (note.tags.length > 0) {
        content += `Tags: ${note.tags.map(tag => `#${tag}`).join(' ')}\n`;
      }
      content += `\n${note.content}\n\n`;

      // Add internal links
      if (note.links && note.links.length > 0) {
        content += `## Linked Notes\n`;
        note.links.forEach(linkId => {
          const linkedNote = notes.find(n => n.id === linkId);
          if (linkedNote) {
            content += `- [[${linkedNote.title}]]\n`;
          }
        });
      }

      return content;
    }).join('---\n\n');

    const blob = new Blob([obsidianFormat], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'knowledge-vault-obsidian.md';
    a.click();
    URL.revokeObjectURL(url);
  };

  const exportToPDF = () => {
    alert('PDF export feature coming soon!');
  };

  // AI Search Assistant
  const aiSearch = () => {
    const query = prompt('Ask AI about your knowledge base:');
    if (query) {
      // In a real app, this would use AI to search and analyze notes
      const relevantNotes = notes.filter(note =>
        note.content.toLowerCase().includes(query.toLowerCase()) ||
        note.title.toLowerCase().includes(query.toLowerCase())
      );

      if (relevantNotes.length > 0) {
        alert(`Found ${relevantNotes.length} relevant notes. AI analysis coming soon!`);
      } else {
        alert('No relevant notes found. Try a different search term.');
      }
    }
  };

  // Web Clipper
  const openWebClipper = () => {
    const url = prompt('Enter URL to clip:');
    if (url) {
      try {
        const domain = new URL(url).hostname;
        const title = prompt('Title for this clip:', domain);
        if (title) {
          addWebClip({
            title,
            url,
            content: 'Web clip content will be extracted automatically in the full version.',
            screenshot: null,
            tags: [domain],
            folderId: null
          });
          alert('Web clip added successfully!');
        }
      } catch (error) {
        alert('Invalid URL. Please enter a valid web address.');
      }
    }
  };

  // Internal linking
  const findLinkedNotes = (noteId) => {
    return notes.filter(note =>
      note.links && note.links.includes(noteId)
    );
  };

  return (
    <div className="min-h-screen p-4 pb-24 bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <h1 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
          Knowledge Vault 📚
        </h1>
        <p className="text-gray-600">Your digital brain for ideas, notes, and learning</p>
      </motion.div>

      {/* Search Bar */}
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <Input
          type="text"
          placeholder="Search notes, flashcards, and clips..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-20 bg-white/80 backdrop-blur-sm"
        />
        <Button
          onClick={aiSearch}
          size="sm"
          className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-purple-500 hover:bg-purple-600"
        >
          <Brain className="w-4 h-4 mr-1" />
          AI
        </Button>
      </div>

      {/* Toolbar */}
      <div className="flex flex-wrap gap-2 mb-6">
        <Button
          onClick={openWebClipper}
          variant="outline"
          size="sm"
          className="bg-white/80"
        >
          <Globe className="w-4 h-4 mr-2" />
          Web Clipper
        </Button>
        <Button
          onClick={exportToObsidian}
          variant="outline"
          size="sm"
          className="bg-white/80"
        >
          <Download className="w-4 h-4 mr-2" />
          Export to Obsidian
        </Button>
        <Button
          onClick={exportToNotion}
          variant="outline"
          size="sm"
          className="bg-white/80"
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          Export to Notion
        </Button>
        <Button
          onClick={exportToPDF}
          variant="outline"
          size="sm"
          className="bg-white/80"
        >
          <FileDown className="w-4 h-4 mr-2" />
          Export PDF
        </Button>
      </div>

      {/* Tab Navigation */}
      <div className="flex overflow-x-auto mb-6 space-x-2 pb-2">
        {tabs.map((tab) => (
          <Button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            variant={activeTab === tab.id ? 'default' : 'outline'}
            className={`flex items-center space-x-2 whitespace-nowrap ${
              activeTab === tab.id 
                ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white' 
                : 'bg-white/80'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span>{tab.label}</span>
            <span className="bg-white/20 px-2 py-0.5 rounded-full text-xs">
              {tab.count}
            </span>
          </Button>
        ))}
      </div>

      <AnimatePresence mode="wait">
        {activeTab === 'notes' && (
          <NotesView 
            notes={filteredNotes}
            folders={folders}
            onCreateNote={() => setShowNoteEditor(true)}
            onEditNote={setEditingNote}
          />
        )}
        
        {activeTab === 'flashcards' && (
          <FlashcardsView 
            flashcards={flashcards}
            folders={folders}
            onCreateCard={createFlashcard}
          />
        )}
        
        {activeTab === 'webclips' && (
          <WebClipsView 
            webClips={webClips}
            folders={folders}
            onAddClip={addWebClip}
          />
        )}
        
        {activeTab === 'folders' && (
          <FoldersView 
            folders={folders}
            onCreateFolder={createFolder}
            onSelectFolder={setSelectedFolder}
          />
        )}
      </AnimatePresence>

      {/* Note Editor Modal */}
      {showNoteEditor && (
        <NoteEditor
          folders={folders}
          onSave={createNote}
          onClose={() => setShowNoteEditor(false)}
        />
      )}
    </div>
  );
};

// Notes View Component
const NotesView = ({ notes, folders, onCreateNote, onEditNote }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className="space-y-4"
  >
    <div className="flex justify-between items-center">
      <h2 className="text-xl font-semibold">Notes & Documents</h2>
      <Button onClick={onCreateNote} className="bg-indigo-500 hover:bg-indigo-600">
        <Plus className="w-4 h-4 mr-2" />
        New Note
      </Button>
    </div>

    {notes.length === 0 ? (
      <GlassmorphCard className="p-12 text-center">
        <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-600 mb-2">No Notes Yet</h3>
        <p className="text-gray-500 mb-6">Start building your knowledge base</p>
        <Button onClick={onCreateNote} className="bg-gradient-to-r from-indigo-500 to-purple-500">
          <Plus className="w-4 h-4 mr-2" />
          Create Your First Note
        </Button>
      </GlassmorphCard>
    ) : (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {notes.map(note => (
          <Card key={note.id} className="hover:shadow-lg transition-all cursor-pointer">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg line-clamp-2">{note.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm line-clamp-3 mb-3">
                {note.content.substring(0, 150)}...
              </p>
              
              {note.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-3">
                  {note.tags.slice(0, 3).map(tag => (
                    <span key={tag} className="bg-indigo-100 text-indigo-600 px-2 py-1 rounded-full text-xs">
                      #{tag}
                    </span>
                  ))}
                </div>
              )}
              
              <div className="flex justify-between items-center text-xs text-gray-500">
                <span>{new Date(note.createdAt).toLocaleDateString()}</span>
                <div className="flex space-x-2">
                  <Button size="sm" variant="outline" onClick={() => onEditNote(note)}>
                    <Edit3 className="w-3 h-3" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Eye className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )}
  </motion.div>
);

// Flashcards View Component
const FlashcardsView = ({ flashcards, folders, onCreateCard }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className="space-y-4"
  >
    <div className="flex justify-between items-center">
      <h2 className="text-xl font-semibold">Flashcards & SRS</h2>
      <Button className="bg-purple-500 hover:bg-purple-600">
        <Plus className="w-4 h-4 mr-2" />
        New Flashcard
      </Button>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {flashcards.map(card => (
        <Card key={card.id} className="hover:shadow-lg transition-all">
          <CardContent className="p-4">
            <div className="text-center space-y-3">
              <div className="bg-gradient-to-r from-purple-100 to-pink-100 p-4 rounded-lg">
                <p className="font-medium">{card.front}</p>
              </div>
              <ArrowRight className="w-4 h-4 text-gray-400 mx-auto" />
              <div className="bg-gradient-to-r from-blue-100 to-indigo-100 p-4 rounded-lg">
                <p className="text-sm">{card.back}</p>
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>Reviews: {card.reviewCount}</span>
                <span>Next: {new Date(card.nextReview).toLocaleDateString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  </motion.div>
);

// Web Clips View Component
const WebClipsView = ({ webClips, folders, onAddClip }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className="space-y-4"
  >
    <div className="flex justify-between items-center">
      <h2 className="text-xl font-semibold">Web Clips & Screenshots</h2>
      <Button className="bg-green-500 hover:bg-green-600">
        <Globe className="w-4 h-4 mr-2" />
        Add Web Clip
      </Button>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {webClips.map(clip => (
        <Card key={clip.id} className="hover:shadow-lg transition-all">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <Globe className="w-4 h-4 mr-2 text-green-500" />
              {clip.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-2 line-clamp-3">{clip.content}</p>
            <div className="flex justify-between items-center text-xs text-gray-500">
              <a href={clip.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                {new URL(clip.url).hostname}
              </a>
              <span>{new Date(clip.createdAt).toLocaleDateString()}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  </motion.div>
);

// Folders View Component
const FoldersView = ({ folders, onCreateFolder, onSelectFolder }) => {
  const [showCreateFolder, setShowCreateFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');

  const handleCreateFolder = () => {
    if (newFolderName.trim()) {
      onCreateFolder(newFolderName);
      setNewFolderName('');
      setShowCreateFolder(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-4"
    >
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Folders & Organization</h2>
        <Button onClick={() => setShowCreateFolder(true)} className="bg-yellow-500 hover:bg-yellow-600">
          <FolderPlus className="w-4 h-4 mr-2" />
          New Folder
        </Button>
      </div>

      {showCreateFolder && (
        <Card className="p-4">
          <div className="flex space-x-2">
            <Input
              placeholder="Folder name..."
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleCreateFolder()}
            />
            <Button onClick={handleCreateFolder}>Create</Button>
            <Button variant="outline" onClick={() => setShowCreateFolder(false)}>Cancel</Button>
          </div>
        </Card>
      )}

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {folders.map(folder => (
          <Card key={folder.id} className="hover:shadow-lg transition-all cursor-pointer" onClick={() => onSelectFolder(folder)}>
            <CardContent className="p-4 text-center">
              <div className={`w-12 h-12 ${folder.color} rounded-lg flex items-center justify-center mx-auto mb-2`}>
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-medium">{folder.name}</h3>
              <p className="text-xs text-gray-500">{folder.noteCount} items</p>
            </CardContent>
          </Card>
        ))}
      </div>
    </motion.div>
  );
};

// Note Editor Component
const NoteEditor = ({ folders, onSave, onClose }) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [selectedFolder, setSelectedFolder] = useState('');
  const [tags, setTags] = useState('');

  const handleSave = () => {
    if (title.trim() && content.trim()) {
      onSave({
        title,
        content,
        folderId: selectedFolder,
        tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full h-[80vh] flex flex-col">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold">Create Note</h2>
            <Button variant="outline" onClick={onClose}>✕</Button>
          </div>
        </div>

        <div className="flex-1 p-6 space-y-4 overflow-y-auto">
          <Input
            placeholder="Note title..."
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="text-xl font-semibold"
          />

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Folder</label>
              <select
                value={selectedFolder}
                onChange={(e) => setSelectedFolder(e.target.value)}
                className="w-full p-2 border rounded-lg"
              >
                <option value="">No folder</option>
                {folders.map(folder => (
                  <option key={folder.id} value={folder.id}>{folder.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Tags</label>
              <Input
                placeholder="tag1, tag2, tag3..."
                value={tags}
                onChange={(e) => setTags(e.target.value)}
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Content (Markdown supported)</label>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="w-full h-64 p-3 border rounded-lg resize-none"
              placeholder="Write your note here... You can use markdown formatting!"
            />
          </div>
        </div>

        <div className="p-6 border-t flex justify-end space-x-4">
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSave} className="bg-indigo-500 hover:bg-indigo-600">
            Save Note
          </Button>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeVault;
