import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>, CardContent, CardHeader, CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue 
} from '@/components/ui/select';
import {
  Zap, Bot, Settings, Play, Pause, Clock, Calendar,
  Bell, Mail, MessageSquare, Smartphone, Laptop,
  Target, CheckCircle, AlertTriangle, Activity,
  Plus, Edit, Trash2, Copy, Eye, EyeOff,
  ArrowRight, ArrowDown, Filter, Search,
  Workflow, GitBranch, Repeat, Timer,
  Brain, Lightbulb, Rocket, <PERSON>, Crown
} from 'lucide-react';

// Automation & Workflow Interfaces
interface AutomationRule {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  trigger: {
    type: 'time' | 'event' | 'condition' | 'manual';
    config: {
      // Time-based triggers
      schedule?: string; // cron expression
      timezone?: string;
      
      // Event-based triggers
      eventType?: 'task_completed' | 'goal_achieved' | 'deadline_approaching' | 'low_energy' | 'focus_session_ended';
      eventFilters?: Record<string, any>;
      
      // Condition-based triggers
      conditions?: Array<{
        field: string;
        operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'not_equals';
        value: any;
      }>;
    };
  };
  actions: Array<{
    id: string;
    type: 'notification' | 'task_creation' | 'email' | 'webhook' | 'data_update' | 'ai_suggestion';
    config: Record<string, any>;
    delay?: number; // seconds
  }>;
  conditions?: Array<{
    field: string;
    operator: string;
    value: any;
  }>;
  tags: string[];
  category: string;
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  updatedAt: string;
  lastTriggered?: string;
  triggerCount: number;
  successRate: number;
}

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  steps: Array<{
    id: string;
    name: string;
    type: 'trigger' | 'condition' | 'action' | 'delay';
    config: Record<string, any>;
    position: { x: number; y: number };
    connections: string[]; // IDs of connected steps
  }>;
  variables: Array<{
    name: string;
    type: 'string' | 'number' | 'boolean' | 'date';
    defaultValue?: any;
    description: string;
  }>;
  tags: string[];
  popularity: number;
  isPublic: boolean;
  createdBy: string;
}

interface AIAssistant {
  id: string;
  name: string;
  personality: 'professional' | 'friendly' | 'motivational' | 'analytical';
  specialization: 'productivity' | 'health' | 'learning' | 'general';
  isActive: boolean;
  settings: {
    proactiveMode: boolean;
    suggestionFrequency: 'low' | 'medium' | 'high';
    learningMode: boolean;
    contextAwareness: boolean;
  };
  knowledgeBase: Array<{
    topic: string;
    expertise: number; // 1-10 scale
    lastUpdated: string;
  }>;
  interactions: Array<{
    id: string;
    timestamp: string;
    type: 'suggestion' | 'question' | 'reminder' | 'insight';
    content: string;
    userResponse?: 'accepted' | 'dismissed' | 'modified';
    effectiveness?: number; // 1-10 scale
  }>;
}

interface SmartNotification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'reminder';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string;
  scheduledFor?: string;
  channels: Array<'app' | 'email' | 'sms' | 'push'>;
  isRead: boolean;
  isActionable: boolean;
  actions?: Array<{
    id: string;
    label: string;
    action: string;
    style: 'primary' | 'secondary' | 'danger';
  }>;
  metadata: Record<string, any>;
  createdAt: string;
  readAt?: string;
}

const AutomationEngine: React.FC = () => {
  // State Management
  const [automationRules, setAutomationRules] = useState<AutomationRule[]>([]);
  const [workflowTemplates, setWorkflowTemplates] = useState<WorkflowTemplate[]>([]);
  const [aiAssistants, setAIAssistants] = useState<AIAssistant[]>([]);
  const [notifications, setNotifications] = useState<SmartNotification[]>([]);
  
  // UI State
  const [activeTab, setActiveTab] = useState<'rules' | 'workflows' | 'ai' | 'notifications'>('rules');
  const [showRuleForm, setShowRuleForm] = useState(false);
  const [editingRule, setEditingRule] = useState<AutomationRule | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Form State
  const [newRule, setNewRule] = useState({
    name: '',
    description: '',
    triggerType: 'time' as AutomationRule['trigger']['type'],
    schedule: '',
    eventType: '',
    actionType: 'notification',
    category: 'productivity',
    priority: 'medium' as AutomationRule['priority']
  });

  useEffect(() => {
    loadData();
    initializeAIAssistants();
  }, []);

  const loadData = () => {
    const savedRules = JSON.parse(localStorage.getItem('automation-rules') || '[]');
    const savedTemplates = JSON.parse(localStorage.getItem('workflow-templates') || '[]');
    const savedNotifications = JSON.parse(localStorage.getItem('smart-notifications') || '[]');
    
    setAutomationRules(savedRules);
    setWorkflowTemplates(savedTemplates);
    setNotifications(savedNotifications);

    // Initialize with sample data if empty
    if (savedRules.length === 0) {
      initializeSampleData();
    }
  };

  const saveData = () => {
    localStorage.setItem('automation-rules', JSON.stringify(automationRules));
    localStorage.setItem('workflow-templates', JSON.stringify(workflowTemplates));
    localStorage.setItem('smart-notifications', JSON.stringify(notifications));
  };

  const initializeSampleData = () => {
    const sampleRules: AutomationRule[] = [
      {
        id: 'rule-1',
        name: 'Daily Planning Reminder',
        description: 'Remind to plan the day every morning at 8 AM',
        isActive: true,
        trigger: {
          type: 'time',
          config: {
            schedule: '0 8 * * *', // 8 AM daily
            timezone: 'UTC'
          }
        },
        actions: [{
          id: 'action-1',
          type: 'notification',
          config: {
            title: 'Time to Plan Your Day!',
            message: 'Take 10 minutes to review your goals and plan your priorities.',
            channels: ['app', 'push']
          }
        }],
        tags: ['planning', 'morning'],
        category: 'productivity',
        priority: 'high',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        triggerCount: 15,
        successRate: 87
      },
      {
        id: 'rule-2',
        name: 'Focus Session Break',
        description: 'Suggest a break after 90 minutes of focused work',
        isActive: true,
        trigger: {
          type: 'event',
          config: {
            eventType: 'focus_session_ended',
            eventFilters: { duration: { min: 90 } }
          }
        },
        actions: [{
          id: 'action-2',
          type: 'ai_suggestion',
          config: {
            type: 'break_recommendation',
            duration: 15,
            activities: ['walk', 'stretch', 'hydrate', 'breathe']
          }
        }],
        tags: ['focus', 'breaks', 'wellness'],
        category: 'health',
        priority: 'medium',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        triggerCount: 42,
        successRate: 92
      },
      {
        id: 'rule-3',
        name: 'Goal Progress Check',
        description: 'Weekly review of goal progress and adjustments',
        isActive: true,
        trigger: {
          type: 'time',
          config: {
            schedule: '0 18 * * 5', // Friday 6 PM
            timezone: 'UTC'
          }
        },
        actions: [{
          id: 'action-3',
          type: 'task_creation',
          config: {
            title: 'Weekly Goal Review',
            description: 'Review progress on all active goals and adjust if needed',
            priority: 'high',
            dueDate: 'end_of_week'
          }
        }],
        tags: ['goals', 'review', 'weekly'],
        category: 'planning',
        priority: 'high',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        triggerCount: 8,
        successRate: 95
      }
    ];

    const sampleTemplates: WorkflowTemplate[] = [
      {
        id: 'template-1',
        name: 'Morning Productivity Routine',
        description: 'Automated morning routine to start the day productively',
        category: 'Daily Routines',
        icon: '🌅',
        steps: [],
        variables: [],
        tags: ['morning', 'routine', 'productivity'],
        popularity: 85,
        isPublic: true,
        createdBy: 'system'
      },
      {
        id: 'template-2',
        name: 'Project Deadline Tracker',
        description: 'Automatically track and remind about project deadlines',
        category: 'Project Management',
        icon: '📅',
        steps: [],
        variables: [],
        tags: ['deadlines', 'projects', 'tracking'],
        popularity: 92,
        isPublic: true,
        createdBy: 'system'
      }
    ];

    setAutomationRules(sampleRules);
    setWorkflowTemplates(sampleTemplates);
  };

  const initializeAIAssistants = () => {
    const assistants: AIAssistant[] = [
      {
        id: 'ai-1',
        name: 'Productivity Coach',
        personality: 'motivational',
        specialization: 'productivity',
        isActive: true,
        settings: {
          proactiveMode: true,
          suggestionFrequency: 'medium',
          learningMode: true,
          contextAwareness: true
        },
        knowledgeBase: [
          { topic: 'Time Management', expertise: 9, lastUpdated: new Date().toISOString() },
          { topic: 'Goal Setting', expertise: 8, lastUpdated: new Date().toISOString() },
          { topic: 'Focus Techniques', expertise: 9, lastUpdated: new Date().toISOString() }
        ],
        interactions: []
      },
      {
        id: 'ai-2',
        name: 'Wellness Advisor',
        personality: 'friendly',
        specialization: 'health',
        isActive: true,
        settings: {
          proactiveMode: true,
          suggestionFrequency: 'low',
          learningMode: true,
          contextAwareness: true
        },
        knowledgeBase: [
          { topic: 'Exercise', expertise: 8, lastUpdated: new Date().toISOString() },
          { topic: 'Nutrition', expertise: 7, lastUpdated: new Date().toISOString() },
          { topic: 'Sleep', expertise: 9, lastUpdated: new Date().toISOString() }
        ],
        interactions: []
      }
    ];

    setAIAssistants(assistants);
  };

  const createAutomationRule = () => {
    const rule: AutomationRule = {
      id: `rule-${Date.now()}`,
      name: newRule.name,
      description: newRule.description,
      isActive: true,
      trigger: {
        type: newRule.triggerType,
        config: newRule.triggerType === 'time' 
          ? { schedule: newRule.schedule, timezone: 'UTC' }
          : { eventType: newRule.eventType }
      },
      actions: [{
        id: `action-${Date.now()}`,
        type: newRule.actionType as any,
        config: {}
      }],
      tags: [],
      category: newRule.category,
      priority: newRule.priority,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      triggerCount: 0,
      successRate: 0
    };

    setAutomationRules([...automationRules, rule]);
    setShowRuleForm(false);
    resetForm();
  };

  const toggleRule = (ruleId: string) => {
    setAutomationRules(automationRules.map(rule =>
      rule.id === ruleId ? { ...rule, isActive: !rule.isActive } : rule
    ));
  };

  const deleteRule = (ruleId: string) => {
    setAutomationRules(automationRules.filter(rule => rule.id !== ruleId));
  };

  const resetForm = () => {
    setNewRule({
      name: '',
      description: '',
      triggerType: 'time',
      schedule: '',
      eventType: '',
      actionType: 'notification',
      category: 'productivity',
      priority: 'medium'
    });
  };

  // Automation Execution Engine
  useEffect(() => {
    const interval = setInterval(() => {
      executeAutomationRules();
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [automationRules]);

  const executeAutomationRules = () => {
    const now = new Date();

    automationRules.forEach(rule => {
      if (!rule.isActive) return;

      let shouldExecute = false;

      // Check trigger conditions
      switch (rule.trigger.type) {
        case 'time':
          shouldExecute = checkTimeBasedTrigger(rule, now);
          break;
        case 'event':
          shouldExecute = checkEventBasedTrigger(rule);
          break;
        case 'condition':
          shouldExecute = checkConditionBasedTrigger(rule);
          break;
      }

      if (shouldExecute) {
        executeRuleActions(rule);
      }
    });
  };

  const checkTimeBasedTrigger = (rule: AutomationRule, now: Date): boolean => {
    if (!rule.trigger.config.schedule) return false;

    // Simple time-based check (in a real app, you'd use a proper cron parser)
    const schedule = rule.trigger.config.schedule;
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    // Example: "08:00" format
    if (schedule.includes(':')) {
      const [hour, minute] = schedule.split(':').map(Number);
      return currentHour === hour && currentMinute === minute;
    }

    return false;
  };

  const checkEventBasedTrigger = (rule: AutomationRule): boolean => {
    // In a real app, this would check for actual events
    // For demo purposes, we'll simulate some events
    const eventType = rule.trigger.config.eventType;

    switch (eventType) {
      case 'task_completed':
        return Math.random() < 0.1; // 10% chance
      case 'goal_achieved':
        return Math.random() < 0.05; // 5% chance
      case 'deadline_approaching':
        return Math.random() < 0.15; // 15% chance
      default:
        return false;
    }
  };

  const checkConditionBasedTrigger = (rule: AutomationRule): boolean => {
    // In a real app, this would check actual conditions
    // For demo purposes, we'll simulate condition checking
    const conditions = rule.trigger.config.conditions || [];

    return conditions.every(condition => {
      // Simulate condition evaluation
      switch (condition.operator) {
        case 'greater_than':
          return Math.random() * 100 > condition.value;
        case 'less_than':
          return Math.random() * 100 < condition.value;
        case 'equals':
          return Math.random() < 0.5;
        default:
          return false;
      }
    });
  };

  const executeRuleActions = (rule: AutomationRule) => {
    console.log(`Executing automation rule: ${rule.name}`);

    rule.actions.forEach(action => {
      switch (action.type) {
        case 'notification':
          createSmartNotification(rule, action);
          break;
        case 'email':
          sendEmail(rule, action);
          break;
        case 'task_creation':
          createTask(rule, action);
          break;
        case 'data_sync':
          syncData(rule, action);
          break;
        case 'webhook':
          callWebhook(rule, action);
          break;
      }
    });

    // Update rule execution history
    const updatedRule = {
      ...rule,
      lastExecuted: new Date().toISOString(),
      executionCount: (rule.executionCount || 0) + 1
    };

    setAutomationRules(prev =>
      prev.map(r => r.id === rule.id ? updatedRule : r)
    );
  };

  const createSmartNotification = (rule: AutomationRule, action: any) => {
    const notification: SmartNotification = {
      id: `notif-${Date.now()}`,
      title: action.config.title || `Automation: ${rule.name}`,
      message: action.config.message || rule.description,
      type: action.config.type || 'info',
      priority: action.config.priority || 'medium',
      category: 'automation',
      isRead: false,
      metadata: { ruleId: rule.id, actionType: action.type },
      createdAt: new Date().toISOString()
    };

    setNotifications(prev => [notification, ...prev]);
  };

  const sendEmail = (rule: AutomationRule, action: any) => {
    // Simulate email sending
    console.log(`Sending email for rule: ${rule.name}`, action.config);

    createSmartNotification(rule, {
      config: {
        title: 'Email Sent',
        message: `Email sent successfully for automation rule: ${rule.name}`,
        type: 'success'
      }
    });
  };

  const createTask = (rule: AutomationRule, action: any) => {
    // Simulate task creation
    console.log(`Creating task for rule: ${rule.name}`, action.config);

    createSmartNotification(rule, {
      config: {
        title: 'Task Created',
        message: `New task created: ${action.config.title || 'Automated Task'}`,
        type: 'info'
      }
    });
  };

  const syncData = (rule: AutomationRule, action: any) => {
    // Simulate data synchronization
    console.log(`Syncing data for rule: ${rule.name}`, action.config);

    createSmartNotification(rule, {
      config: {
        title: 'Data Synced',
        message: `Data synchronization completed for: ${rule.name}`,
        type: 'success'
      }
    });
  };

  const callWebhook = (rule: AutomationRule, action: any) => {
    // Simulate webhook call
    console.log(`Calling webhook for rule: ${rule.name}`, action.config);

    createSmartNotification(rule, {
      config: {
        title: 'Webhook Called',
        message: `Webhook executed successfully for: ${rule.name}`,
        type: 'success'
      }
    });
  };

  const testRule = (rule: AutomationRule) => {
    createSmartNotification(rule, {
      config: {
        title: 'Test Execution',
        message: `Test execution of automation rule: ${rule.name}`,
        type: 'info'
      }
    });

    executeRuleActions(rule);
  };

  const filteredRules = automationRules.filter(rule => {
    const matchesCategory = selectedCategory === 'all' || rule.category === selectedCategory;
    const matchesSearch = !searchQuery ||
      rule.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rule.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen p-4 pb-24 bg-gradient-to-br from-violet-50 via-white to-cyan-50"
    >
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 space-y-4 lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3">
            <h1 className="text-3xl font-bold text-gray-800">Automation Engine</h1>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span className="text-sm text-green-600 font-medium">
                {automationRules.filter(r => r.isActive).length} Active Rules
              </span>
            </div>
          </div>
          <div className="flex items-center space-x-4 text-gray-600">
            <p>Smart workflows and AI-powered productivity</p>
            <span className="text-sm">
              Total executions: {automationRules.reduce((sum, rule) => sum + (rule.executionCount || 0), 0)}
            </span>
          </div>
        </div>
        
        <div className="flex flex-wrap items-center space-x-2">
          <Button 
            onClick={() => setShowRuleForm(true)}
            className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Rule
          </Button>
          <Button variant="outline">
            <Workflow className="w-4 h-4 mr-2" />
            Templates
          </Button>
        </div>
      </div>

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-4">
          <TabsTrigger value="rules" className="flex items-center text-xs md:text-sm">
            <Zap className="w-4 h-4 mr-1 md:mr-2" />
            Rules
          </TabsTrigger>
          <TabsTrigger value="workflows" className="flex items-center text-xs md:text-sm">
            <Workflow className="w-4 h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Workflows</span>
            <span className="sm:hidden">Flow</span>
          </TabsTrigger>
          <TabsTrigger value="ai" className="flex items-center text-xs md:text-sm">
            <Bot className="w-4 h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">AI Assistants</span>
            <span className="sm:hidden">AI</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center text-xs md:text-sm">
            <Bell className="w-4 h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Notifications</span>
            <span className="sm:hidden">Alerts</span>
          </TabsTrigger>
        </TabsList>

        {/* Rules Tab Content */}
        <TabsContent value="rules" className="space-y-6">
          {/* Filters and Search */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search automation rules..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-white/20 border-white/30"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-48 bg-white/20 border-white/30">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="productivity">Productivity</SelectItem>
                <SelectItem value="health">Health</SelectItem>
                <SelectItem value="planning">Planning</SelectItem>
                <SelectItem value="communication">Communication</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Rules List */}
          <div className="grid gap-4">
            {filteredRules.map((rule, index) => (
              <motion.div
                key={rule.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                        {rule.name}
                      </h3>
                      <Badge variant={rule.isActive ? "default" : "secondary"}>
                        {rule.isActive ? "Active" : "Inactive"}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {rule.category}
                      </Badge>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                      {rule.description}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>Triggered {rule.triggerCount || 0} times</span>
                      <span>Success rate: {rule.successRate || 100}%</span>
                      <span>Executions: {rule.executionCount || 0}</span>
                      {rule.lastExecuted && (
                        <span>Last executed: {new Date(rule.lastExecuted).toLocaleTimeString()}</span>
                      )}
                      {rule.lastTriggered && (
                        <span>Last triggered: {new Date(rule.lastTriggered).toLocaleDateString()}</span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={rule.isActive}
                      onCheckedChange={() => toggleRule(rule.id)}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => testRule(rule)}
                      className="text-green-600 hover:text-green-700 hover:bg-green-50"
                    >
                      <Play className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => setEditingRule(rule)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => deleteRule(rule.id)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Rule Details */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="bg-white/10 rounded-lg p-3">
                    <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-1">Trigger</h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      {rule.trigger.type === 'time' ? '⏰ Time-based' :
                       rule.trigger.type === 'event' ? '🎯 Event-based' :
                       rule.trigger.type === 'condition' ? '📊 Condition-based' : '👆 Manual'}
                    </p>
                  </div>
                  <div className="bg-white/10 rounded-lg p-3">
                    <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-1">Actions</h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      {rule.actions.length} action{rule.actions.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                  <div className="bg-white/10 rounded-lg p-3">
                    <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-1">Priority</h4>
                    <Badge variant={rule.priority === 'high' ? 'destructive' :
                                  rule.priority === 'medium' ? 'default' : 'secondary'}>
                      {rule.priority}
                    </Badge>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {filteredRules.length === 0 && (
            <div className="text-center py-12">
              <Bot className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
                No automation rules found
              </h3>
              <p className="text-gray-500 mb-4">
                Create your first automation rule to get started
              </p>
              <Button onClick={() => setShowRuleForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Rule
              </Button>
            </div>
          )}
        </TabsContent>

        {/* AI Assistants Tab */}
        <TabsContent value="ai" className="space-y-6">
          <div className="grid gap-6">
            {aiAssistants.map((assistant, index) => (
              <motion.div
                key={assistant.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                      <Bot className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                        {assistant.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {assistant.specialization} • {assistant.personality}
                      </p>
                    </div>
                  </div>
                  <Switch
                    checked={assistant.isActive}
                    onCheckedChange={() => {
                      setAIAssistants(aiAssistants.map(a =>
                        a.id === assistant.id ? { ...a, isActive: !a.isActive } : a
                      ));
                    }}
                  />
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {assistant.interactions.length}
                    </div>
                    <div className="text-xs text-gray-500">Interactions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {assistant.knowledgeBase.length}
                    </div>
                    <div className="text-xs text-gray-500">Knowledge Areas</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {Math.round(assistant.knowledgeBase.reduce((acc, kb) => acc + kb.expertise, 0) / assistant.knowledgeBase.length)}
                    </div>
                    <div className="text-xs text-gray-500">Avg Expertise</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {assistant.settings.suggestionFrequency}
                    </div>
                    <div className="text-xs text-gray-500">Frequency</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700 dark:text-gray-300">Knowledge Base</h4>
                  <div className="flex flex-wrap gap-2">
                    {assistant.knowledgeBase.map((kb, idx) => (
                      <Badge key={idx} variant="outline" className="text-xs">
                        {kb.topic} ({kb.expertise}/10)
                      </Badge>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Workflows Tab */}
        <TabsContent value="workflows" className="space-y-6">
          <div className="text-center py-12">
            <Workflow className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
              Workflow Builder Coming Soon
            </h3>
            <p className="text-gray-500 mb-4">
              Visual workflow builder with drag-and-drop interface
            </p>
          </div>
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-6">
          <div className="text-center py-12">
            <Bell className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
              Smart Notifications
            </h3>
            <p className="text-gray-500 mb-4">
              Intelligent notification management system
            </p>
          </div>
        </TabsContent>
      </Tabs>

      {/* Create Rule Form Modal */}
      <AnimatePresence>
        {showRuleForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowRuleForm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">Create Automation Rule</h3>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="rule-name">Rule Name</Label>
                  <Input
                    id="rule-name"
                    value={newRule.name}
                    onChange={(e) => setNewRule({ ...newRule, name: e.target.value })}
                    placeholder="Enter rule name"
                  />
                </div>

                <div>
                  <Label htmlFor="rule-description">Description</Label>
                  <Input
                    id="rule-description"
                    value={newRule.description}
                    onChange={(e) => setNewRule({ ...newRule, description: e.target.value })}
                    placeholder="Describe what this rule does"
                  />
                </div>

                <div>
                  <Label htmlFor="trigger-type">Trigger Type</Label>
                  <Select value={newRule.triggerType} onValueChange={(value: any) => setNewRule({ ...newRule, triggerType: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="time">Time-based</SelectItem>
                      <SelectItem value="event">Event-based</SelectItem>
                      <SelectItem value="condition">Condition-based</SelectItem>
                      <SelectItem value="manual">Manual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {newRule.triggerType === 'time' && (
                  <div>
                    <Label htmlFor="schedule">Schedule (Cron)</Label>
                    <Input
                      id="schedule"
                      value={newRule.schedule}
                      onChange={(e) => setNewRule({ ...newRule, schedule: e.target.value })}
                      placeholder="0 9 * * * (9 AM daily)"
                    />
                  </div>
                )}

                <div className="flex gap-2 pt-4">
                  <Button onClick={createAutomationRule} className="flex-1">
                    Create Rule
                  </Button>
                  <Button variant="outline" onClick={() => setShowRuleForm(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default AutomationEngine;
