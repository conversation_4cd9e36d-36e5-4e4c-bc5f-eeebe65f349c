// Security Service for App Lock and Fake Mode
import CryptoJS from 'crypto-js';

export interface SecurityConfig {
  isEnabled: boolean;
  passcode: string; // hashed
  fakeMode: {
    enabled: boolean;
    triggerCode: string; // hashed
    fakeData: any;
  };
  biometric: {
    enabled: boolean;
    type: 'fingerprint' | 'face' | 'none';
  };
  autoLock: {
    enabled: boolean;
    timeout: number; // minutes
  };
  attempts: {
    failed: number;
    maxAttempts: number;
    lockoutTime: number; // minutes
  };
}

const defaultFakeData = {
  tasks: [
    { id: 1, title: 'Buy groceries', completed: false, priority: 'medium' },
    { id: 2, title: 'Call dentist', completed: true, priority: 'low' },
    { id: 3, title: 'Finish report', completed: false, priority: 'high' }
  ],
  habits: [
    { id: 1, name: 'Morning walk', streak: 5, isActive: true },
    { id: 2, name: 'Read books', streak: 12, isActive: true }
  ],
  transactions: [
    { id: 1, type: 'expense', amount: 25.50, category: 'Food', date: '2024-01-15' },
    { id: 2, type: 'income', amount: 500, category: 'Salary', date: '2024-01-01' }
  ],
  budgets: [
    { id: 1, category: 'Food', limit: 300, spent: 125, period: 'monthly' },
    { id: 2, category: 'Entertainment', limit: 150, spent: 45, period: 'monthly' }
  ],
  goals: [
    { id: 1, title: 'Learn Spanish', progress: 35, target: 100 },
    { id: 2, title: 'Save $1000', progress: 60, target: 100 }
  ],
  journalEntries: [
    { id: 1, title: 'Good day', content: 'Had a productive day at work.', date: '2024-01-15' }
  ],
  moods: [
    { id: 1, mood: 'happy', intensity: 4, date: '2024-01-15' },
    { id: 2, mood: 'calm', intensity: 3, date: '2024-01-14' }
  ]
};

class SecurityService {
  private static instance: SecurityService;
  private config: SecurityConfig;
  private isUnlocked: boolean = false;
  private isFakeMode: boolean = false;
  private lastActivity: number = Date.now();
  private autoLockTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.loadConfig();
    this.setupAutoLock();
    this.setupActivityTracking();
  }

  static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService();
    }
    return SecurityService.instance;
  }

  private loadConfig(): void {
    const saved = localStorage.getItem('securityConfig');
    if (saved) {
      try {
        this.config = JSON.parse(saved);
      } catch (error) {
        this.config = this.getDefaultConfig();
      }
    } else {
      this.config = this.getDefaultConfig();
    }
  }

  private getDefaultConfig(): SecurityConfig {
    return {
      isEnabled: false,
      passcode: '',
      fakeMode: {
        enabled: false,
        triggerCode: '',
        fakeData: defaultFakeData
      },
      biometric: {
        enabled: false,
        type: 'none'
      },
      autoLock: {
        enabled: false,
        timeout: 5
      },
      attempts: {
        failed: 0,
        maxAttempts: 5,
        lockoutTime: 30
      }
    };
  }

  private saveConfig(): void {
    localStorage.setItem('securityConfig', JSON.stringify(this.config));
  }

  private hashCode(code: string): string {
    return CryptoJS.SHA256(code).toString();
  }

  private setupAutoLock(): void {
    if (this.config.autoLock.enabled) {
      this.resetAutoLockTimer();
    }
  }

  private resetAutoLockTimer(): void {
    if (this.autoLockTimer) {
      clearTimeout(this.autoLockTimer);
    }

    if (this.config.autoLock.enabled && this.isUnlocked) {
      this.autoLockTimer = setTimeout(() => {
        this.lock();
      }, this.config.autoLock.timeout * 60 * 1000);
    }
  }

  private setupActivityTracking(): void {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    events.forEach(event => {
      document.addEventListener(event, () => {
        this.lastActivity = Date.now();
        this.resetAutoLockTimer();
      }, true);
    });
  }

  // Public methods
  isSecurityEnabled(): boolean {
    return this.config.isEnabled;
  }

  isAppUnlocked(): boolean {
    return !this.config.isEnabled || this.isUnlocked;
  }

  isInFakeMode(): boolean {
    return this.isFakeMode;
  }

  setupSecurity(passcode: string, enableFakeMode: boolean = false, fakeCode?: string): void {
    this.config.isEnabled = true;
    this.config.passcode = this.hashCode(passcode);
    
    if (enableFakeMode && fakeCode) {
      this.config.fakeMode.enabled = true;
      this.config.fakeMode.triggerCode = this.hashCode(fakeCode);
    }
    
    this.saveConfig();
    this.isUnlocked = true;
  }

  async unlock(code: string): Promise<{ success: boolean; isFakeMode: boolean; message: string }> {
    // Check if locked out
    if (this.isLockedOut()) {
      return {
        success: false,
        isFakeMode: false,
        message: `Too many failed attempts. Try again in ${this.getRemainingLockoutTime()} minutes.`
      };
    }

    const hashedCode = this.hashCode(code);

    // Check for fake mode trigger
    if (this.config.fakeMode.enabled && hashedCode === this.config.fakeMode.triggerCode) {
      this.isUnlocked = true;
      this.isFakeMode = true;
      this.config.attempts.failed = 0;
      this.saveConfig();
      this.setupAutoLock();
      
      // Load fake data
      this.loadFakeData();
      
      return {
        success: true,
        isFakeMode: true,
        message: 'Unlocked in fake mode'
      };
    }

    // Check for real passcode
    if (hashedCode === this.config.passcode) {
      this.isUnlocked = true;
      this.isFakeMode = false;
      this.config.attempts.failed = 0;
      this.saveConfig();
      this.setupAutoLock();
      
      return {
        success: true,
        isFakeMode: false,
        message: 'Unlocked successfully'
      };
    }

    // Failed attempt
    this.config.attempts.failed++;
    this.saveConfig();

    return {
      success: false,
      isFakeMode: false,
      message: `Incorrect passcode. ${this.config.attempts.maxAttempts - this.config.attempts.failed} attempts remaining.`
    };
  }

  lock(): void {
    this.isUnlocked = false;
    this.isFakeMode = false;
    
    if (this.autoLockTimer) {
      clearTimeout(this.autoLockTimer);
      this.autoLockTimer = null;
    }
  }

  disableSecurity(currentPasscode: string): boolean {
    if (this.hashCode(currentPasscode) === this.config.passcode) {
      this.config.isEnabled = false;
      this.config.passcode = '';
      this.config.fakeMode.enabled = false;
      this.config.fakeMode.triggerCode = '';
      this.isUnlocked = true;
      this.isFakeMode = false;
      this.saveConfig();
      return true;
    }
    return false;
  }

  changePasscode(currentPasscode: string, newPasscode: string): boolean {
    if (this.hashCode(currentPasscode) === this.config.passcode) {
      this.config.passcode = this.hashCode(newPasscode);
      this.saveConfig();
      return true;
    }
    return false;
  }

  updateAutoLock(enabled: boolean, timeout: number): void {
    this.config.autoLock.enabled = enabled;
    this.config.autoLock.timeout = timeout;
    this.saveConfig();
    
    if (enabled) {
      this.setupAutoLock();
    } else if (this.autoLockTimer) {
      clearTimeout(this.autoLockTimer);
      this.autoLockTimer = null;
    }
  }

  private isLockedOut(): boolean {
    if (this.config.attempts.failed < this.config.attempts.maxAttempts) {
      return false;
    }

    const lockoutEnd = this.getLastFailedAttemptTime() + (this.config.attempts.lockoutTime * 60 * 1000);
    return Date.now() < lockoutEnd;
  }

  private getRemainingLockoutTime(): number {
    const lockoutEnd = this.getLastFailedAttemptTime() + (this.config.attempts.lockoutTime * 60 * 1000);
    const remaining = Math.ceil((lockoutEnd - Date.now()) / (60 * 1000));
    return Math.max(0, remaining);
  }

  private getLastFailedAttemptTime(): number {
    return parseInt(localStorage.getItem('lastFailedAttempt') || '0');
  }

  private loadFakeData(): void {
    // Replace real data with fake data
    Object.keys(this.config.fakeMode.fakeData).forEach(key => {
      localStorage.setItem(`fake_${key}`, JSON.stringify(this.config.fakeMode.fakeData[key]));
    });
  }

  getFakeData(key: string): any {
    if (this.isFakeMode) {
      const fakeData = localStorage.getItem(`fake_${key}`);
      return fakeData ? JSON.parse(fakeData) : this.config.fakeMode.fakeData[key] || [];
    }
    return null;
  }

  updateFakeData(key: string, data: any): void {
    if (this.isFakeMode) {
      this.config.fakeMode.fakeData[key] = data;
      localStorage.setItem(`fake_${key}`, JSON.stringify(data));
      this.saveConfig();
    }
  }

  // Biometric authentication (placeholder for future implementation)
  async setupBiometric(type: 'fingerprint' | 'face'): Promise<boolean> {
    // This would integrate with device biometric APIs
    // For now, just update config
    this.config.biometric.enabled = true;
    this.config.biometric.type = type;
    this.saveConfig();
    return true;
  }

  async authenticateWithBiometric(): Promise<boolean> {
    // Placeholder for biometric authentication
    // Would use device APIs like WebAuthn
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(Math.random() > 0.1); // 90% success rate for demo
      }, 1000);
    });
  }

  getSecurityConfig(): SecurityConfig {
    return { ...this.config };
  }

  // Emergency access (for development/testing)
  emergencyUnlock(masterKey: string): boolean {
    const masterHash = this.hashCode('FOCOS_MASTER_2024');
    if (this.hashCode(masterKey) === masterHash) {
      this.isUnlocked = true;
      this.isFakeMode = false;
      this.config.attempts.failed = 0;
      this.saveConfig();
      return true;
    }
    return false;
  }

  // Export/Import security settings (encrypted)
  exportSecuritySettings(password: string): string {
    const data = JSON.stringify(this.config);
    return CryptoJS.AES.encrypt(data, password).toString();
  }

  importSecuritySettings(encryptedData: string, password: string): boolean {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedData, password);
      const data = JSON.parse(decrypted.toString(CryptoJS.enc.Utf8));
      
      // Validate data structure
      if (data.isEnabled !== undefined && data.passcode !== undefined) {
        this.config = data;
        this.saveConfig();
        return true;
      }
    } catch (error) {
      console.error('Failed to import security settings:', error);
    }
    return false;
  }
}

export default SecurityService.getInstance();
