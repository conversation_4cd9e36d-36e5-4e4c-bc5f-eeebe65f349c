import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>, CardContent, CardHeader, CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue 
} from '@/components/ui/select';
import {
  Skull, Clock, Target, Calendar, TrendingUp, Award,
  Plus, Edit, Trash2, CheckCircle, Circle, Flag,
  BarChart3, PieChart, Activity, Zap, Heart,
  ArrowUp, ArrowDown, Star, Trophy, Crown,
  Lightbulb, Rocket, Diamond, FileText, Timer,
  AlertTriangle, Coffee, Mountain, Plane, Home,
  Users, Book, Briefcase, GraduationCap, MapPin
} from 'lucide-react';
import { GlassmorphCard } from '@/components/ui/glassmorphcard';

// Death Clock Interfaces
interface LifeGoal {
  id: string;
  title: string;
  description: string;
  category: 'career' | 'personal' | 'health' | 'relationships' | 'travel' | 'learning' | 'financial' | 'creative';
  priority: 'low' | 'medium' | 'high' | 'critical';
  targetDate: string;
  completed: boolean;
  completedDate?: string;
  progress: number; // 0-100
  milestones: Milestone[];
  notes: string;
  estimatedTimeNeeded: number; // in hours
  tags: string[];
}

interface Milestone {
  id: string;
  title: string;
  completed: boolean;
  completedDate?: string;
  dueDate?: string;
}

interface LifeStats {
  totalGoals: number;
  completedGoals: number;
  timeRemaining: number; // in seconds
  lifeProgress: number; // percentage of life lived
  goalsCompletionRate: number;
  averageGoalCompletionTime: number;
  mostProductiveCategory: string;
  urgentGoals: number;
}

interface DeathClockSettings {
  birthDate: string;
  estimatedDeathDate: string;
  lifeExpectancy: number;
  country: string;
  gender: 'male' | 'female' | 'other';
  lifestyle: 'poor' | 'average' | 'good' | 'excellent';
  showSeconds: boolean;
  darkMode: boolean;
  motivationalQuotes: boolean;
}

const DeathClock = () => {
  const [settings, setSettings] = useState<DeathClockSettings>({
    birthDate: '',
    estimatedDeathDate: '',
    lifeExpectancy: 75,
    country: 'Global',
    gender: 'other',
    lifestyle: 'average',
    showSeconds: true,
    darkMode: false,
    motivationalQuotes: true
  });

  const [goals, setGoals] = useState<LifeGoal[]>([]);
  const [showGoalForm, setShowGoalForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState<LifeGoal | null>(null);
  const [activeTab, setActiveTab] = useState('clock');
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [lifeProgress, setLifeProgress] = useState<number>(0);
  const [showSettings, setShowSettings] = useState(false);

  const [newGoal, setNewGoal] = useState<Partial<LifeGoal>>({
    title: '',
    description: '',
    category: 'personal',
    priority: 'medium',
    targetDate: '',
    progress: 0,
    milestones: [],
    notes: '',
    estimatedTimeNeeded: 0,
    tags: []
  });

  // Load data from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('deathClockSettings');
    const savedGoals = localStorage.getItem('lifeGoals');

    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }
    if (savedGoals) {
      setGoals(JSON.parse(savedGoals));
    }
  }, []);

  // Save data to localStorage
  useEffect(() => {
    localStorage.setItem('deathClockSettings', JSON.stringify(settings));
  }, [settings]);

  useEffect(() => {
    localStorage.setItem('lifeGoals', JSON.stringify(goals));
  }, [goals]);

  // Calculate time remaining and life progress
  useEffect(() => {
    if (settings.estimatedDeathDate && settings.birthDate) {
      const updateTimer = () => {
        const now = new Date().getTime();
        const deathTime = new Date(settings.estimatedDeathDate).getTime();
        const birthTime = new Date(settings.birthDate).getTime();
        
        const remaining = Math.max(0, deathTime - now);
        const totalLife = deathTime - birthTime;
        const lived = now - birthTime;
        
        setTimeRemaining(remaining);
        setLifeProgress((lived / totalLife) * 100);
      };

      updateTimer();
      const interval = setInterval(updateTimer, 1000);
      return () => clearInterval(interval);
    }
  }, [settings.estimatedDeathDate, settings.birthDate]);

  const formatTimeRemaining = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const years = Math.floor(days / 365);

    if (settings.showSeconds) {
      return {
        years: years,
        days: days % 365,
        hours: hours % 24,
        minutes: minutes % 60,
        seconds: seconds % 60
      };
    } else {
      return {
        years: years,
        days: days % 365,
        hours: hours % 24,
        minutes: minutes % 60
      };
    }
  };

  const getLifeStats = (): LifeStats => {
    const completedGoals = goals.filter(goal => goal.completed).length;
    const urgentGoals = goals.filter(goal => {
      const targetDate = new Date(goal.targetDate);
      const now = new Date();
      const daysUntilTarget = (targetDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
      return daysUntilTarget <= 30 && !goal.completed;
    }).length;

    const categoryStats = goals.reduce((acc, goal) => {
      if (goal.completed) {
        acc[goal.category] = (acc[goal.category] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const mostProductiveCategory = Object.entries(categoryStats).reduce((a, b) => 
      categoryStats[a[0]] > categoryStats[b[0]] ? a : b, ['none', 0])[0];

    return {
      totalGoals: goals.length,
      completedGoals,
      timeRemaining: timeRemaining / 1000,
      lifeProgress,
      goalsCompletionRate: goals.length > 0 ? (completedGoals / goals.length) * 100 : 0,
      averageGoalCompletionTime: 0, // Calculate based on completion dates
      mostProductiveCategory,
      urgentGoals
    };
  };

  const addGoal = () => {
    if (!newGoal.title || !newGoal.targetDate) return;

    const goal: LifeGoal = {
      id: Date.now().toString(),
      title: newGoal.title,
      description: newGoal.description || '',
      category: newGoal.category as any,
      priority: newGoal.priority as any,
      targetDate: newGoal.targetDate,
      completed: false,
      progress: 0,
      milestones: [],
      notes: newGoal.notes || '',
      estimatedTimeNeeded: newGoal.estimatedTimeNeeded || 0,
      tags: newGoal.tags || []
    };

    setGoals(prev => [goal, ...prev]);
    setNewGoal({
      title: '',
      description: '',
      category: 'personal',
      priority: 'medium',
      targetDate: '',
      progress: 0,
      milestones: [],
      notes: '',
      estimatedTimeNeeded: 0,
      tags: []
    });
    setShowGoalForm(false);
  };

  const toggleGoalCompletion = (goalId: string) => {
    setGoals(prev => prev.map(goal => 
      goal.id === goalId 
        ? { 
            ...goal, 
            completed: !goal.completed,
            completedDate: !goal.completed ? new Date().toISOString() : undefined,
            progress: !goal.completed ? 100 : goal.progress
          }
        : goal
    ));
  };

  const deleteGoal = (goalId: string) => {
    setGoals(prev => prev.filter(goal => goal.id !== goalId));
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'career': return <Briefcase className="w-4 h-4" />;
      case 'health': return <Heart className="w-4 h-4" />;
      case 'relationships': return <Users className="w-4 h-4" />;
      case 'travel': return <Plane className="w-4 h-4" />;
      case 'learning': return <GraduationCap className="w-4 h-4" />;
      case 'financial': return <Trophy className="w-4 h-4" />;
      case 'creative': return <Lightbulb className="w-4 h-4" />;
      default: return <Target className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const timeLeft = formatTimeRemaining(timeRemaining);
  const stats = getLifeStats();

  const motivationalQuotes = [
    "Time is the most valuable thing we have, because it is the most irrevocable.",
    "The time you enjoy wasting is not wasted time.",
    "Yesterday is history, tomorrow is a mystery, today is a gift.",
    "Time flies over us, but leaves its shadow behind.",
    "Make each day count, for time is the currency of life."
  ];

  const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`min-h-screen p-4 pb-24 ${settings.darkMode ? 'bg-gray-900 text-white' : 'bg-gradient-to-br from-gray-50 via-red-50 to-orange-50'}`}
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold flex items-center">
            <Skull className="w-8 h-8 mr-3 text-red-500" />
            Death Clock & Life Goals
          </h1>
          <p className="text-gray-600">Track your remaining time and achieve your life goals</p>
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={() => setShowSettings(true)}
            variant="outline"
            className="rounded-full p-3"
          >
            <Settings className="w-5 h-5" />
          </Button>
          <Button
            onClick={() => setShowGoalForm(true)}
            className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 rounded-full p-3"
          >
            <Plus className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Motivational Quote */}
      {settings.motivationalQuotes && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <GlassmorphCard className="p-4 text-center">
            <p className="text-lg italic text-gray-700">"{randomQuote}"</p>
          </GlassmorphCard>
        </motion.div>
      )}

      {/* Main Death Clock Display */}
      {settings.estimatedDeathDate && (
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="mb-8"
        >
          <GlassmorphCard className="p-8 text-center">
            <div className="mb-6">
              <h2 className="text-2xl font-bold mb-2 text-red-600">Time Remaining</h2>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-4xl font-bold text-red-500">{timeLeft.years}</div>
                  <div className="text-sm text-gray-600">Years</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-orange-500">{timeLeft.days}</div>
                  <div className="text-sm text-gray-600">Days</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-yellow-500">{timeLeft.hours}</div>
                  <div className="text-sm text-gray-600">Hours</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-green-500">{timeLeft.minutes}</div>
                  <div className="text-sm text-gray-600">Minutes</div>
                </div>
                {settings.showSeconds && (
                  <div className="text-center">
                    <div className="text-4xl font-bold text-blue-500">{timeLeft.seconds}</div>
                    <div className="text-sm text-gray-600">Seconds</div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">Life Progress</span>
                <span className="text-sm font-semibold">{lifeProgress.toFixed(2)}%</span>
              </div>
              <Progress value={lifeProgress} className="h-3" />
            </div>
            
            <div className="text-sm text-gray-600">
              Estimated death date: {new Date(settings.estimatedDeathDate).toLocaleDateString()}
            </div>
          </GlassmorphCard>
        </motion.div>
      )}

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="clock" className="flex items-center">
            <Clock className="w-4 h-4 mr-2" />
            Clock
          </TabsTrigger>
          <TabsTrigger value="goals" className="flex items-center">
            <Target className="w-4 h-4 mr-2" />
            Life Goals
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center">
            <BarChart3 className="w-4 h-4 mr-2" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="timeline" className="flex items-center">
            <Calendar className="w-4 h-4 mr-2" />
            Timeline
          </TabsTrigger>
        </TabsList>

        <TabsContent value="clock" className="space-y-6">
          {/* Life Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <GlassmorphCard className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <Target className="w-6 h-6 text-blue-500" />
              </div>
              <p className="text-2xl font-bold text-gray-800">{stats.totalGoals}</p>
              <p className="text-sm text-gray-600">Total Goals</p>
            </GlassmorphCard>

            <GlassmorphCard className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <CheckCircle className="w-6 h-6 text-green-500" />
              </div>
              <p className="text-2xl font-bold text-gray-800">{stats.completedGoals}</p>
              <p className="text-sm text-gray-600">Completed</p>
            </GlassmorphCard>

            <GlassmorphCard className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <AlertTriangle className="w-6 h-6 text-red-500" />
              </div>
              <p className="text-2xl font-bold text-gray-800">{stats.urgentGoals}</p>
              <p className="text-sm text-gray-600">Urgent</p>
            </GlassmorphCard>

            <GlassmorphCard className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <TrendingUp className="w-6 h-6 text-purple-500" />
              </div>
              <p className="text-2xl font-bold text-gray-800">{stats.goalsCompletionRate.toFixed(1)}%</p>
              <p className="text-sm text-gray-600">Success Rate</p>
            </GlassmorphCard>
          </div>

          {/* Recent Goals */}
          <GlassmorphCard className="p-6">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center">
                <Flag className="w-5 h-5 mr-2" />
                Recent Goals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {goals.slice(0, 5).map((goal) => (
                  <div key={goal.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => toggleGoalCompletion(goal.id)}
                        className="p-1"
                      >
                        {goal.completed ? (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        ) : (
                          <Circle className="w-5 h-5 text-gray-400" />
                        )}
                      </Button>
                      <div className="flex items-center space-x-2">
                        {getCategoryIcon(goal.category)}
                        <span className={goal.completed ? 'line-through text-gray-500' : ''}>
                          {goal.title}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className={getPriorityColor(goal.priority)}>
                        {goal.priority}
                      </Badge>
                      <span className="text-sm text-gray-500">
                        {new Date(goal.targetDate).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </GlassmorphCard>
        </TabsContent>

        <TabsContent value="goals" className="space-y-6">
          {/* Goals List */}
          <div className="space-y-4">
            {goals.map((goal) => (
              <motion.div
                key={goal.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-6 border rounded-lg bg-white/50 backdrop-blur-sm"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start space-x-3">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => toggleGoalCompletion(goal.id)}
                      className="p-1 mt-1"
                    >
                      {goal.completed ? (
                        <CheckCircle className="w-6 h-6 text-green-500" />
                      ) : (
                        <Circle className="w-6 h-6 text-gray-400" />
                      )}
                    </Button>
                    <div>
                      <h3 className={`text-lg font-semibold ${goal.completed ? 'line-through text-gray-500' : ''}`}>
                        {goal.title}
                      </h3>
                      <p className="text-gray-600 mt-1">{goal.description}</p>
                      <div className="flex items-center space-x-4 mt-2">
                        <div className="flex items-center space-x-1">
                          {getCategoryIcon(goal.category)}
                          <span className="text-sm text-gray-500 capitalize">{goal.category}</span>
                        </div>
                        <Badge variant="outline" className={getPriorityColor(goal.priority)}>
                          {goal.priority}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          Due: {new Date(goal.targetDate).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setEditingGoal(goal)}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => deleteGoal(goal.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {!goal.completed && (
                  <div className="mt-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">Progress</span>
                      <span className="text-sm font-semibold">{goal.progress}%</span>
                    </div>
                    <Progress value={goal.progress} className="h-2" />
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Analytics Dashboard */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <GlassmorphCard className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center">
                  <PieChart className="w-5 h-5 mr-2" />
                  Goals by Category
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(
                    goals.reduce((acc, goal) => {
                      acc[goal.category] = (acc[goal.category] || 0) + 1;
                      return acc;
                    }, {} as Record<string, number>)
                  ).map(([category, count]) => (
                    <div key={category} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getCategoryIcon(category)}
                        <span className="capitalize">{category}</span>
                      </div>
                      <Badge variant="secondary">{count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </GlassmorphCard>

            <GlassmorphCard className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center">
                  <Activity className="w-5 h-5 mr-2" />
                  Completion Stats
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">Overall Progress</span>
                      <span className="text-sm font-semibold">{stats.goalsCompletionRate.toFixed(1)}%</span>
                    </div>
                    <Progress value={stats.goalsCompletionRate} className="h-3" />
                  </div>

                  <div className="grid grid-cols-2 gap-4 pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{stats.completedGoals}</div>
                      <div className="text-sm text-gray-600">Completed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{stats.totalGoals - stats.completedGoals}</div>
                      <div className="text-sm text-gray-600">Remaining</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </GlassmorphCard>
          </div>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-6">
          {/* Timeline View */}
          <GlassmorphCard className="p-6">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Life Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {goals
                  .sort((a, b) => new Date(a.targetDate).getTime() - new Date(b.targetDate).getTime())
                  .map((goal, index) => (
                    <div key={goal.id} className="flex items-start space-x-4">
                      <div className="flex flex-col items-center">
                        <div className={`w-4 h-4 rounded-full ${goal.completed ? 'bg-green-500' : 'bg-gray-300'}`} />
                        {index < goals.length - 1 && <div className="w-0.5 h-8 bg-gray-200 mt-2" />}
                      </div>
                      <div className="flex-1 pb-4">
                        <div className="flex items-center justify-between">
                          <h4 className={`font-semibold ${goal.completed ? 'line-through text-gray-500' : ''}`}>
                            {goal.title}
                          </h4>
                          <span className="text-sm text-gray-500">
                            {new Date(goal.targetDate).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-gray-600 text-sm mt-1">{goal.description}</p>
                        <div className="flex items-center space-x-2 mt-2">
                          {getCategoryIcon(goal.category)}
                          <Badge variant="outline" className={getPriorityColor(goal.priority)}>
                            {goal.priority}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </GlassmorphCard>
        </TabsContent>
      </Tabs>

      {/* Goal Form Modal */}
      <AnimatePresence>
        {showGoalForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowGoalForm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">Add Life Goal</h3>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Goal Title</Label>
                  <Input
                    id="title"
                    value={newGoal.title}
                    onChange={(e) => setNewGoal(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Enter your life goal"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={newGoal.description}
                    onChange={(e) => setNewGoal(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe your goal in detail"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="category">Category</Label>
                    <Select value={newGoal.category} onValueChange={(value) => setNewGoal(prev => ({ ...prev, category: value as any }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="career">Career</SelectItem>
                        <SelectItem value="personal">Personal</SelectItem>
                        <SelectItem value="health">Health</SelectItem>
                        <SelectItem value="relationships">Relationships</SelectItem>
                        <SelectItem value="travel">Travel</SelectItem>
                        <SelectItem value="learning">Learning</SelectItem>
                        <SelectItem value="financial">Financial</SelectItem>
                        <SelectItem value="creative">Creative</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Select value={newGoal.priority} onValueChange={(value) => setNewGoal(prev => ({ ...prev, priority: value as any }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="targetDate">Target Date</Label>
                  <Input
                    id="targetDate"
                    type="date"
                    value={newGoal.targetDate}
                    onChange={(e) => setNewGoal(prev => ({ ...prev, targetDate: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="estimatedTime">Estimated Time Needed (hours)</Label>
                  <Input
                    id="estimatedTime"
                    type="number"
                    value={newGoal.estimatedTimeNeeded}
                    onChange={(e) => setNewGoal(prev => ({ ...prev, estimatedTimeNeeded: parseInt(e.target.value) || 0 }))}
                    placeholder="0"
                  />
                </div>

                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    value={newGoal.notes}
                    onChange={(e) => setNewGoal(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Additional notes or action steps"
                    rows={2}
                  />
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <Button onClick={addGoal} className="flex-1">
                  Add Goal
                </Button>
                <Button variant="outline" onClick={() => setShowGoalForm(false)}>
                  Cancel
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Settings Modal */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowSettings(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">Death Clock Settings</h3>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="birthDate">Birth Date</Label>
                  <Input
                    id="birthDate"
                    type="date"
                    value={settings.birthDate}
                    onChange={(e) => setSettings(prev => ({ ...prev, birthDate: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="lifeExpectancy">Life Expectancy (years)</Label>
                  <Input
                    id="lifeExpectancy"
                    type="number"
                    value={settings.lifeExpectancy}
                    onChange={(e) => {
                      const years = parseInt(e.target.value) || 75;
                      setSettings(prev => ({
                        ...prev,
                        lifeExpectancy: years,
                        estimatedDeathDate: prev.birthDate ?
                          new Date(new Date(prev.birthDate).getTime() + years * 365.25 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : ''
                      }));
                    }}
                    placeholder="75"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="gender">Gender</Label>
                    <Select value={settings.gender} onValueChange={(value) => setSettings(prev => ({ ...prev, gender: value as any }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="lifestyle">Lifestyle</Label>
                    <Select value={settings.lifestyle} onValueChange={(value) => setSettings(prev => ({ ...prev, lifestyle: value as any }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="poor">Poor</SelectItem>
                        <SelectItem value="average">Average</SelectItem>
                        <SelectItem value="good">Good</SelectItem>
                        <SelectItem value="excellent">Excellent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="showSeconds">Show Seconds</Label>
                    <Switch
                      id="showSeconds"
                      checked={settings.showSeconds}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, showSeconds: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="darkMode">Dark Mode</Label>
                    <Switch
                      id="darkMode"
                      checked={settings.darkMode}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, darkMode: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="motivationalQuotes">Motivational Quotes</Label>
                    <Switch
                      id="motivationalQuotes"
                      checked={settings.motivationalQuotes}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, motivationalQuotes: checked }))}
                    />
                  </div>
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <Button onClick={() => setShowSettings(false)} className="flex-1">
                  Save Settings
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default DeathClock;
