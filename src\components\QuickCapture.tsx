import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Mic, Camera, MapPin, Type, Image, FileText,
  Save, X, Play, Pause, Square, Upload,
  Zap, Clock, Tag, AlertCircle, CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { GlassmorphCard } from '@/components/ui/glassmorphcard';

interface CaptureItem {
  id: string;
  type: 'voice' | 'photo' | 'text' | 'location';
  content: string;
  transcription?: string;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  tags: string[];
  reminder?: {
    date: string;
    location?: string;
  };
  createdAt: string;
  processed: boolean;
}

const QuickCapture = () => {
  const [captures, setCaptures] = useState<CaptureItem[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [showCapture, setShowCapture] = useState(false);
  const [captureType, setCaptureType] = useState<'voice' | 'photo' | 'text' | 'location'>('text');
  const [currentCapture, setCurrentCapture] = useState({
    content: '',
    tags: '',
    reminderDate: '',
    reminderLocation: ''
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<GeolocationPosition | null>(null);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    loadCaptures();
    getCurrentLocation();
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isRecording]);

  const loadCaptures = () => {
    const saved = JSON.parse(localStorage.getItem('quickCaptures') || '[]');
    setCaptures(saved);
  };

  const saveCaptures = (newCaptures: CaptureItem[]) => {
    setCaptures(newCaptures);
    localStorage.setItem('quickCaptures', JSON.stringify(newCaptures));
  };

  const getCurrentLocation = () => {
    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => setCurrentLocation(position),
        (error) => console.log('Location access denied:', error)
      );
    }
  };

  const startVoiceRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);
        setCurrentCapture(prev => ({ ...prev, content: audioUrl }));
        
        // Simulate transcription (in real app, use speech-to-text API)
        simulateTranscription(audioBlob);
      };

      mediaRecorder.start();
      setIsRecording(true);
      setRecordingTime(0);
    } catch (error) {
      console.error('Error accessing microphone:', error);
      alert('Could not access microphone. Please check permissions.');
    }
  };

  const stopVoiceRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      // Stop all tracks
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
    }
  };

  const simulateTranscription = async (audioBlob: Blob) => {
    setIsProcessing(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock transcription result
    const mockTranscriptions = [
      "Remember to buy groceries on the way home",
      "Meeting with client tomorrow at 3 PM",
      "Great idea for the new project feature",
      "Don't forget to call mom this weekend",
      "Research topic for next presentation"
    ];
    
    const transcription = mockTranscriptions[Math.floor(Math.random() * mockTranscriptions.length)];
    setCurrentCapture(prev => ({ ...prev, content: prev.content, transcription }));
    setIsProcessing(false);
  };

  const startPhotoCapture = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      alert('Could not access camera. Please check permissions.');
    }
  };

  const capturePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const canvas = canvasRef.current;
      const video = videoRef.current;
      
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(video, 0, 0);
        const imageDataUrl = canvas.toDataURL('image/jpeg');
        setCurrentCapture(prev => ({ ...prev, content: imageDataUrl }));
        
        // Stop video stream
        const stream = video.srcObject as MediaStream;
        stream.getTracks().forEach(track => track.stop());
        
        // Simulate OCR processing
        simulateOCR(imageDataUrl);
      }
    }
  };

  const simulateOCR = async (imageData: string) => {
    setIsProcessing(true);
    
    // Simulate OCR processing delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Mock OCR results
    const mockOCRResults = [
      "Important document text extracted",
      "Meeting notes from whiteboard",
      "Recipe ingredients list",
      "Contact information captured",
      "Address or location details"
    ];
    
    const ocrText = mockOCRResults[Math.floor(Math.random() * mockOCRResults.length)];
    setCurrentCapture(prev => ({ 
      ...prev, 
      transcription: ocrText 
    }));
    setIsProcessing(false);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setCurrentCapture(prev => ({ ...prev, content: result }));
        
        if (file.type.startsWith('image/')) {
          simulateOCR(result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const saveCapture = () => {
    if (!currentCapture.content.trim() && !currentCapture.transcription) return;

    const capture: CaptureItem = {
      id: Date.now().toString(),
      type: captureType,
      content: currentCapture.content,
      transcription: currentCapture.transcription,
      location: currentLocation ? {
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude
      } : undefined,
      tags: currentCapture.tags.split(',').map(t => t.trim()).filter(t => t),
      reminder: currentCapture.reminderDate ? {
        date: currentCapture.reminderDate,
        location: currentCapture.reminderLocation || undefined
      } : undefined,
      createdAt: new Date().toISOString(),
      processed: !!currentCapture.transcription
    };

    const newCaptures = [capture, ...captures];
    saveCaptures(newCaptures);
    
    // Reset form
    setCurrentCapture({
      content: '',
      tags: '',
      reminderDate: '',
      reminderLocation: ''
    });
    setShowCapture(false);
    setCaptureType('text');
  };

  const deleteCapture = (id: string) => {
    const updated = captures.filter(c => c.id !== id);
    saveCaptures(updated);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const captureTypes = [
    { id: 'text', label: 'Text Note', icon: Type, color: 'bg-blue-500' },
    { id: 'voice', label: 'Voice Memo', icon: Mic, color: 'bg-red-500' },
    { id: 'photo', label: 'Photo/OCR', icon: Camera, color: 'bg-green-500' },
    { id: 'location', label: 'Location', icon: MapPin, color: 'bg-purple-500' }
  ];

  return (
    <div className="min-h-screen p-4 pb-24 bg-gradient-to-br from-emerald-50 via-white to-teal-50">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <h1 className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-2">
          Quick Capture ⚡
        </h1>
        <p className="text-gray-600">Capture ideas instantly with voice, photo, or text</p>
      </motion.div>

      {/* Quick Action Buttons */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        {captureTypes.map((type) => (
          <Button
            key={type.id}
            onClick={() => {
              setCaptureType(type.id as any);
              setShowCapture(true);
              if (type.id === 'photo') startPhotoCapture();
            }}
            className={`${type.color} text-white h-20 flex flex-col items-center justify-center space-y-2`}
          >
            <type.icon className="w-6 h-6" />
            <span className="text-sm">{type.label}</span>
          </Button>
        ))}
      </div>

      {/* Recent Captures */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Recent Captures</h2>
        
        {captures.length === 0 ? (
          <Card className="text-center p-8">
            <Zap className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No captures yet. Start by capturing your first idea!</p>
          </Card>
        ) : (
          captures.map((capture) => (
            <motion.div
              key={capture.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg p-4 shadow-sm border"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Badge className={captureTypes.find(t => t.id === capture.type)?.color}>
                    {capture.type}
                  </Badge>
                  {capture.processed && (
                    <Badge variant="outline" className="text-green-600 border-green-600">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Processed
                    </Badge>
                  )}
                </div>
                <Button
                  onClick={() => deleteCapture(capture.id)}
                  variant="ghost"
                  size="sm"
                  className="text-red-500 hover:text-red-700"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              
              {capture.type === 'voice' && capture.content && (
                <audio controls className="w-full mb-3">
                  <source src={capture.content} type="audio/wav" />
                </audio>
              )}
              
              {capture.type === 'photo' && capture.content && (
                <img 
                  src={capture.content} 
                  alt="Captured" 
                  className="w-full max-w-xs rounded-lg mb-3"
                />
              )}
              
              {capture.type === 'text' && (
                <p className="text-gray-800 mb-3">{capture.content}</p>
              )}
              
              {capture.transcription && (
                <div className="bg-blue-50 rounded-lg p-3 mb-3">
                  <div className="flex items-center mb-2">
                    <FileText className="w-4 h-4 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-blue-800">
                      {capture.type === 'voice' ? 'Transcription' : 'OCR Text'}
                    </span>
                  </div>
                  <p className="text-blue-700 text-sm">{capture.transcription}</p>
                </div>
              )}
              
              {capture.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-3">
                  {capture.tags.map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      <Tag className="w-3 h-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
              
              <div className="flex items-center justify-between text-xs text-gray-500">
                <div className="flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  {new Date(capture.createdAt).toLocaleString()}
                </div>
                
                {capture.location && (
                  <div className="flex items-center">
                    <MapPin className="w-3 h-3 mr-1" />
                    Location saved
                  </div>
                )}
              </div>
            </motion.div>
          ))
        )}
      </div>

      {/* Capture Modal */}
      <AnimatePresence>
        {showCapture && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-6 w-full max-w-lg max-h-[90vh] overflow-y-auto"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-bold">
                  {captureTypes.find(t => t.id === captureType)?.label}
                </h3>
                <Button
                  onClick={() => setShowCapture(false)}
                  variant="ghost"
                  size="sm"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {/* Voice Recording */}
              {captureType === 'voice' && (
                <div className="space-y-4">
                  <div className="text-center">
                    {isRecording ? (
                      <div className="space-y-4">
                        <div className="text-6xl text-red-500 animate-pulse">🎤</div>
                        <div className="text-2xl font-mono">{formatTime(recordingTime)}</div>
                        <Button
                          onClick={stopVoiceRecording}
                          className="bg-red-500 text-white"
                        >
                          <Square className="w-4 h-4 mr-2" />
                          Stop Recording
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="text-6xl text-gray-400">🎤</div>
                        <Button
                          onClick={startVoiceRecording}
                          className="bg-red-500 text-white"
                        >
                          <Mic className="w-4 h-4 mr-2" />
                          Start Recording
                        </Button>
                      </div>
                    )}
                  </div>

                  {currentCapture.content && (
                    <div className="space-y-3">
                      <audio controls className="w-full">
                        <source src={currentCapture.content} type="audio/wav" />
                      </audio>

                      {isProcessing ? (
                        <div className="text-center p-4 bg-blue-50 rounded-lg">
                          <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
                          <p className="text-blue-700">Transcribing audio...</p>
                        </div>
                      ) : currentCapture.transcription && (
                        <div className="p-3 bg-green-50 rounded-lg">
                          <p className="text-green-800 font-medium mb-1">Transcription:</p>
                          <p className="text-green-700">{currentCapture.transcription}</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* Photo Capture */}
              {captureType === 'photo' && (
                <div className="space-y-4">
                  <div className="text-center">
                    <video
                      ref={videoRef}
                      className="w-full max-w-sm rounded-lg mb-4"
                      style={{ display: currentCapture.content ? 'none' : 'block' }}
                    />
                    <canvas ref={canvasRef} style={{ display: 'none' }} />

                    {currentCapture.content ? (
                      <div className="space-y-3">
                        <img
                          src={currentCapture.content}
                          alt="Captured"
                          className="w-full max-w-sm rounded-lg mx-auto"
                        />

                        {isProcessing ? (
                          <div className="text-center p-4 bg-blue-50 rounded-lg">
                            <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
                            <p className="text-blue-700">Processing image with OCR...</p>
                          </div>
                        ) : currentCapture.transcription && (
                          <div className="p-3 bg-green-50 rounded-lg">
                            <p className="text-green-800 font-medium mb-1">Extracted Text:</p>
                            <p className="text-green-700">{currentCapture.transcription}</p>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <Button
                          onClick={capturePhoto}
                          className="bg-green-500 text-white"
                        >
                          <Camera className="w-4 h-4 mr-2" />
                          Take Photo
                        </Button>

                        <div className="text-center">
                          <p className="text-sm text-gray-500 mb-2">Or upload an image</p>
                          <input
                            ref={fileInputRef}
                            type="file"
                            accept="image/*"
                            onChange={handleFileUpload}
                            className="hidden"
                          />
                          <Button
                            onClick={() => fileInputRef.current?.click()}
                            variant="outline"
                          >
                            <Upload className="w-4 h-4 mr-2" />
                            Upload Image
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Text Input */}
              {captureType === 'text' && (
                <div className="space-y-4">
                  <Textarea
                    value={currentCapture.content}
                    onChange={(e) => setCurrentCapture(prev => ({ ...prev, content: e.target.value }))}
                    placeholder="Write your note here..."
                    rows={6}
                    className="w-full"
                  />
                </div>
              )}

              {/* Location Capture */}
              {captureType === 'location' && (
                <div className="space-y-4">
                  <div className="text-center p-6 bg-purple-50 rounded-lg">
                    <MapPin className="w-12 h-12 text-purple-500 mx-auto mb-4" />
                    {currentLocation ? (
                      <div>
                        <p className="text-purple-800 font-medium mb-2">Current Location Captured</p>
                        <p className="text-purple-600 text-sm">
                          Lat: {currentLocation.coords.latitude.toFixed(6)}<br />
                          Lng: {currentLocation.coords.longitude.toFixed(6)}
                        </p>
                      </div>
                    ) : (
                      <div>
                        <p className="text-purple-800 font-medium mb-2">Getting Location...</p>
                        <p className="text-purple-600 text-sm">Please allow location access</p>
                      </div>
                    )}
                  </div>

                  <Textarea
                    value={currentCapture.content}
                    onChange={(e) => setCurrentCapture(prev => ({ ...prev, content: e.target.value }))}
                    placeholder="Add notes about this location..."
                    rows={3}
                  />
                </div>
              )}

              {/* Common Fields */}
              <div className="space-y-4 mt-6">
                <div>
                  <label className="block text-sm font-medium mb-2">Tags (comma-separated)</label>
                  <Input
                    value={currentCapture.tags}
                    onChange={(e) => setCurrentCapture(prev => ({ ...prev, tags: e.target.value }))}
                    placeholder="work, important, idea"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Reminder Date</label>
                    <Input
                      type="datetime-local"
                      value={currentCapture.reminderDate}
                      onChange={(e) => setCurrentCapture(prev => ({ ...prev, reminderDate: e.target.value }))}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Reminder Location</label>
                    <Input
                      value={currentCapture.reminderLocation}
                      onChange={(e) => setCurrentCapture(prev => ({ ...prev, reminderLocation: e.target.value }))}
                      placeholder="Home, Office, etc."
                    />
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3 mt-6">
                <Button
                  onClick={saveCapture}
                  className="flex-1"
                  disabled={!currentCapture.content.trim() && !currentCapture.transcription}
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save Capture
                </Button>
                <Button
                  onClick={() => setShowCapture(false)}
                  variant="outline"
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default QuickCapture;
