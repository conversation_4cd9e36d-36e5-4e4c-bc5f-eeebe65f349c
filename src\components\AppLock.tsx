import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Lock, 
  Unlock, 
  Eye, 
  EyeOff, 
  Shield, 
  Fingerprint, 
  AlertTriangle,
  Settings,
  Clock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import SecurityService from '../services/SecurityService';
import ThemeEngine from '../services/ThemeEngine';

interface AppLockProps {
  onUnlock: (isFakeMode: boolean) => void;
}

const AppLock: React.FC<AppLockProps> = ({ onUnlock }) => {
  const [passcode, setPasscode] = useState('');
  const [showPasscode, setShowPasscode] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [attempts, setAttempts] = useState(0);
  const [showBiometric, setShowBiometric] = useState(false);
  const [lockoutTime, setLockoutTime] = useState(0);

  useEffect(() => {
    const config = SecurityService.getSecurityConfig();
    setAttempts(config.attempts.failed);
    setShowBiometric(config.biometric.enabled);
    
    // Check if locked out
    updateLockoutStatus();
    
    const interval = setInterval(updateLockoutStatus, 1000);
    return () => clearInterval(interval);
  }, []);

  const updateLockoutStatus = () => {
    // This would be implemented in SecurityService
    // For now, just clear lockout after some time
    if (lockoutTime > 0) {
      setLockoutTime(prev => Math.max(0, prev - 1));
    }
  };

  const handleUnlock = async () => {
    if (!passcode.trim()) {
      setError('Please enter your passcode');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await SecurityService.unlock(passcode);
      
      if (result.success) {
        ThemeEngine.playSound('taskComplete');
        ThemeEngine.triggerHaptic('heavy');
        onUnlock(result.isFakeMode);
      } else {
        setError(result.message);
        setAttempts(prev => prev + 1);
        ThemeEngine.triggerHaptic('heavy');
        
        // Shake animation on error
        const lockScreen = document.getElementById('lock-screen');
        if (lockScreen) {
          lockScreen.classList.add('animate-shake');
          setTimeout(() => {
            lockScreen.classList.remove('animate-shake');
          }, 500);
        }
      }
    } catch (error) {
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
      setPasscode('');
    }
  };

  const handleBiometricAuth = async () => {
    setIsLoading(true);
    
    try {
      const success = await SecurityService.authenticateWithBiometric();
      
      if (success) {
        ThemeEngine.playSound('taskComplete');
        ThemeEngine.triggerHaptic('heavy');
        onUnlock(false);
      } else {
        setError('Biometric authentication failed');
        ThemeEngine.triggerHaptic('heavy');
      }
    } catch (error) {
      setError('Biometric authentication unavailable');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleUnlock();
    }
  };

  const isLockedOut = lockoutTime > 0;

  return (
    <div 
      id="lock-screen"
      className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.3),transparent_50%)]" />
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_30%,rgba(255,255,255,0.1)_50%,transparent_70%)]" />
      </div>

      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="relative z-10 w-full max-w-md"
      >
        <Card className="shadow-2xl border-0 bg-white/10 backdrop-blur-xl">
          <CardHeader className="text-center pb-6">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto mb-4"
            >
              <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                <Lock className="w-10 h-10 text-white" />
              </div>
            </motion.div>
            
            <CardTitle className="text-2xl font-bold text-white mb-2">
              FOCOS
            </CardTitle>
            <p className="text-gray-300 text-sm">
              Enter your passcode to continue
            </p>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Lockout Warning */}
            <AnimatePresence>
              {isLockedOut && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 text-center"
                >
                  <AlertTriangle className="w-6 h-6 text-red-400 mx-auto mb-2" />
                  <p className="text-red-300 text-sm">
                    Too many failed attempts
                  </p>
                  <p className="text-red-200 text-xs mt-1">
                    Try again in {Math.floor(lockoutTime / 60)}:{(lockoutTime % 60).toString().padStart(2, '0')}
                  </p>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Passcode Input */}
            <div className="space-y-4">
              <div className="relative">
                <Input
                  type={showPasscode ? "text" : "password"}
                  placeholder="Enter passcode"
                  value={passcode}
                  onChange={(e) => setPasscode(e.target.value)}
                  onKeyPress={handleKeyPress}
                  disabled={isLoading || isLockedOut}
                  className="bg-white/10 border-white/20 text-white placeholder-gray-400 pr-12 text-center text-lg tracking-widest"
                  maxLength={20}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPasscode(!showPasscode)}
                  disabled={isLoading || isLockedOut}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  {showPasscode ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>

              {/* Error Message */}
              <AnimatePresence>
                {error && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="text-red-400 text-sm text-center bg-red-500/10 rounded-lg p-2"
                  >
                    {error}
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Unlock Button */}
              <Button
                onClick={handleUnlock}
                disabled={isLoading || isLockedOut || !passcode.trim()}
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-medium py-3 rounded-xl transition-all duration-200"
              >
                {isLoading ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                  />
                ) : (
                  <>
                    <Unlock className="w-5 h-5 mr-2" />
                    Unlock
                  </>
                )}
              </Button>
            </div>

            {/* Biometric Authentication */}
            {showBiometric && !isLockedOut && (
              <div className="space-y-3">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-600" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-transparent px-2 text-gray-400">Or</span>
                  </div>
                </div>

                <Button
                  onClick={handleBiometricAuth}
                  disabled={isLoading}
                  variant="outline"
                  className="w-full border-white/20 text-white hover:bg-white/10 py-3 rounded-xl"
                >
                  <Fingerprint className="w-5 h-5 mr-2" />
                  Use Biometric
                </Button>
              </div>
            )}

            {/* Attempt Counter */}
            {attempts > 0 && !isLockedOut && (
              <div className="text-center">
                <p className="text-gray-400 text-xs">
                  Failed attempts: {attempts}/5
                </p>
              </div>
            )}

            {/* Security Info */}
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center text-gray-400 text-xs">
                <Shield className="w-3 h-3 mr-1" />
                Your data is protected
              </div>
              
              {/* Fake Mode Hint */}
              <div className="text-gray-500 text-xs">
                💡 Tip: Different codes unlock different modes
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Emergency Access (Development Only) */}
        {process.env.NODE_ENV === 'development' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
            className="mt-4 text-center"
          >
            <Button
              onClick={() => {
                const masterKey = prompt('Enter master key:');
                if (masterKey && SecurityService.emergencyUnlock(masterKey)) {
                  onUnlock(false);
                }
              }}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-gray-300 text-xs"
            >
              Emergency Access
            </Button>
          </motion.div>
        )}
      </motion.div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/10 rounded-full"
            initial={{
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
            }}
            animate={{
              y: [null, -100],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default AppLock;
