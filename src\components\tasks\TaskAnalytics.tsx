
import React from 'react';
import { motion } from 'framer-motion';
import { X, Clock, Calendar, Tag, MapPin, Link2, T<PERSON>dingU<PERSON>, <PERSON>, <PERSON> } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Task } from '../Tasks';

interface TaskAnalyticsProps {
  task: Task;
  onClose: () => void;
  onUpdate: (taskId: string, updates: Partial<Task>) => void;
}

const TaskAnalytics: React.FC<TaskAnalyticsProps> = ({ task, onClose, onUpdate }) => {
  const getTimeSpent = () => {
    if (task.actualMinutes) {
      const hours = Math.floor(task.actualMinutes / 60);
      const minutes = task.actualMinutes % 60;
      return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
    }
    return 'Not tracked';
  };

  const getEstimatedVsActual = () => {
    if (task.estimatedMinutes && task.actualMinutes) {
      const diff = task.actualMinutes - task.estimatedMinutes;
      const percentage = ((diff / task.estimatedMinutes) * 100).toFixed(1);
      return {
        diff,
        percentage,
        isOver: diff > 0
      };
    }
    return null;
  };

  const getDaysActive = () => {
    const created = new Date(task.createdAt);
    const now = new Date();
    return Math.ceil((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));
  };

  const getProductivityScore = () => {
    let score = 0;
    
    // Completion bonus
    if (task.completed) score += 40;
    
    // Priority completion bonus
    const priorityBonus = { low: 5, medium: 10, high: 15, urgent: 20 };
    if (task.completed) score += priorityBonus[task.priority];
    
    // On-time bonus
    if (task.dueDate && task.completed) {
      const dueDate = new Date(task.dueDate);
      const completedDate = new Date(task.updatedAt);
      if (completedDate <= dueDate) score += 20;
    }
    
    // Efficiency bonus (if completed within estimated time)
    const timeComparison = getEstimatedVsActual();
    if (timeComparison && !timeComparison.isOver) score += 15;
    
    return Math.min(score, 100);
  };

  const timeComparison = getEstimatedVsActual();
  const productivityScore = getProductivityScore();

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Task Analytics</h2>
          <Button variant="ghost" onClick={onClose} className="p-2">
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Task Overview */}
        <Card className="mb-6 shadow-lg border-0 bg-gradient-to-r from-purple-50 to-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <div 
                className="w-4 h-4 rounded-full" 
                style={{ backgroundColor: task.colorLabel }}
              />
              <span>{task.title}</span>
              {task.completed && <Badge className="bg-green-600">Completed</Badge>}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {task.description && (
              <p className="text-gray-600 mb-4">{task.description}</p>
            )}
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{task.priority}</div>
                <div className="text-sm text-gray-600">Priority</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{task.status}</div>
                <div className="text-sm text-gray-600">Status</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{getDaysActive()}</div>
                <div className="text-sm text-gray-600">Days Active</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{Math.round(task.aiScore || 0)}</div>
                <div className="text-sm text-gray-600">AI Score</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Time Tracking */}
        <Card className="mb-6 shadow-lg border-0">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-blue-600" />
              <span>Time Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-xl">
                <div className="text-xl font-bold text-blue-600">
                  {task.estimatedMinutes ? `${task.estimatedMinutes}m` : 'Not set'}
                </div>
                <div className="text-sm text-gray-600">Estimated</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-xl">
                <div className="text-xl font-bold text-green-600">{getTimeSpent()}</div>
                <div className="text-sm text-gray-600">Actual</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-xl">
                <div className={`text-xl font-bold ${
                  timeComparison?.isOver ? 'text-red-600' : 'text-green-600'
                }`}>
                  {timeComparison ? `${timeComparison.percentage}%` : 'N/A'}
                </div>
                <div className="text-sm text-gray-600">
                  {timeComparison?.isOver ? 'Over estimate' : 'Under estimate'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Productivity Score */}
        <Card className="mb-6 shadow-lg border-0">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              <span>Productivity Score</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-4xl font-bold text-green-600 mb-2">{productivityScore}</div>
              <div className="text-sm text-gray-600 mb-4">Out of 100</div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all"
                  style={{ width: `${productivityScore}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Task Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Metadata */}
          <Card className="shadow-lg border-0">
            <CardHeader>
              <CardTitle className="text-lg">Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {task.dueDate && (
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <span className="text-sm">Due: {new Date(task.dueDate).toLocaleString()}</span>
                </div>
              )}
              
              {task.location && (
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4 text-gray-500" />
                  <span className="text-sm">{task.location}</span>
                </div>
              )}
              
              {task.project && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm">📁 {task.project}</span>
                </div>
              )}
              
              <div className="text-xs text-gray-500">
                Created: {new Date(task.createdAt).toLocaleString()}
              </div>
              <div className="text-xs text-gray-500">
                Updated: {new Date(task.updatedAt).toLocaleString()}
              </div>
            </CardContent>
          </Card>

          {/* Tags & Links */}
          <Card className="shadow-lg border-0">
            <CardHeader>
              <CardTitle className="text-lg">Organization</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {task.tags.length > 0 && (
                <div>
                  <div className="text-sm font-medium mb-2">Tags</div>
                  <div className="flex flex-wrap gap-1">
                    {task.tags.map(tag => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        <Tag className="w-2 h-2 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              {task.linkedTasks && task.linkedTasks.length > 0 && (
                <div>
                  <div className="text-sm font-medium mb-2">Linked Tasks</div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Link2 className="w-4 h-4 mr-1" />
                    {task.linkedTasks.length} connected tasks
                  </div>
                </div>
              )}
              
              {task.isRecurring && (
                <div className="flex items-center text-sm text-purple-600">
                  <span>🔄 Recurring ({task.recurringPattern})</span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* AI Insights */}
        <Card className="mt-6 shadow-lg border-0 bg-gradient-to-r from-purple-50 to-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Brain className="w-5 h-5 text-purple-600" />
              <span>AI Insights</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <Star className="w-4 h-4 text-yellow-500" />
                <span>This task has high AI priority score due to {task.priority} priority and due date proximity.</span>
              </div>
              
              {timeComparison?.isOver && (
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-orange-500" />
                  <span>Consider breaking down similar tasks for better time estimation.</span>
                </div>
              )}
              
              {task.completed && (
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4 text-green-500" />
                  <span>Great job completing this task! Your productivity score reflects consistent progress.</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default TaskAnalytics;
