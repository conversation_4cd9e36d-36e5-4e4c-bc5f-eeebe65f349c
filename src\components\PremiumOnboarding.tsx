import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronRight, ArrowLeft, Check, Target, DollarSign, Heart, Brain, TrendingUp, 
  Briefcase, Sun, Moon, Clock, Layers, Zap, Bell, BellOff, Sparkles, Star,
  Rocket, Crown, Diamond, Palette, Smartphone, Globe, Shield, Award, Play
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface OnboardingData {
  name: string;
  avatar: string;
  goals: string[];
  workStyle: string;
  motto: string;
  notificationsEnabled: boolean;
}

const PremiumOnboarding = ({ onComplete }: { onComplete: (data: OnboardingData) => void }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showParticles, setShowParticles] = useState(true);
  const [data, setData] = useState<OnboardingData>({
    name: '',
    avatar: '🚀',
    goals: [],
    workStyle: '',
    motto: '',
    notificationsEnabled: true
  });

  // Clear any existing data when onboarding starts
  useEffect(() => {
    localStorage.removeItem('billionDollarTasks');
    localStorage.removeItem('billionDollarMood');
    localStorage.removeItem('billionDollarHabits');
    localStorage.removeItem('billionDollarGoals');
    localStorage.removeItem('billionDollarFocusTime');
    localStorage.removeItem('billionDollarWeeklyStreak');
    localStorage.removeItem('billionDollarTotalTasksCompleted');
  }, []);

  // Floating particles for premium feel
  const particles = Array.from({ length: 30 }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    delay: Math.random() * 3,
    duration: 4 + Math.random() * 3,
    size: 2 + Math.random() * 4
  }));

  const steps = [
    {
      title: "Welcome to FOCOS",
      subtitle: "The Ultimate Productivity Experience",
      description: "Join millions who've transformed their lives with our premium productivity suite",
      gradient: "from-purple-600 via-pink-600 to-blue-600",
      icon: Crown
    },
    {
      title: "Create Your Identity",
      subtitle: "Let's make this uniquely yours",
      description: "Choose your avatar and tell us your name to personalize your journey",
      gradient: "from-blue-600 via-cyan-500 to-teal-500",
      icon: Sparkles
    },
    {
      title: "Define Your Success",
      subtitle: "What matters most to you?",
      description: "Select the areas where you want to excel and achieve greatness",
      gradient: "from-emerald-500 via-green-500 to-lime-500",
      icon: Target
    },
    {
      title: "Your Peak Performance",
      subtitle: "When do you shine brightest?",
      description: "We'll optimize your experience based on your natural energy patterns",
      gradient: "from-orange-500 via-red-500 to-pink-500",
      icon: Zap
    },
    {
      title: "Your Daily Inspiration",
      subtitle: "What drives you forward?",
      description: "A personal motto to keep you motivated and focused on your goals",
      gradient: "from-violet-600 via-purple-600 to-indigo-600",
      icon: Heart
    },
    {
      title: "Smart Notifications",
      subtitle: "Stay in your flow state",
      description: "Intelligent reminders that enhance your productivity without disruption",
      gradient: "from-rose-500 via-pink-500 to-purple-500",
      icon: Bell
    }
  ];

  const avatars = ['🚀', '⭐', '💎', '👑', '🔥', '⚡', '🌟', '💫', '🎯', '🏆', '💪', '🧠', '❤️', '🌈', '🦄', '✨'];
  
  const goals = [
    { id: 'productivity', label: 'Peak Productivity', icon: Target, color: 'from-purple-500 to-pink-500', desc: 'Master your time and tasks' },
    { id: 'health', label: 'Optimal Health', icon: Heart, color: 'from-pink-500 to-red-500', desc: 'Physical and mental wellness' },
    { id: 'finance', label: 'Financial Freedom', icon: DollarSign, color: 'from-green-500 to-emerald-500', desc: 'Build wealth and security' },
    { id: 'learning', label: 'Continuous Learning', icon: Brain, color: 'from-blue-500 to-indigo-500', desc: 'Expand knowledge and skills' },
    { id: 'mindfulness', label: 'Inner Peace', icon: Sparkles, color: 'from-teal-500 to-cyan-500', desc: 'Mindfulness and balance' },
    { id: 'career', label: 'Career Excellence', icon: TrendingUp, color: 'from-orange-500 to-yellow-500', desc: 'Professional growth' }
  ];

  const workStyles = [
    { id: 'early-bird', label: 'Early Bird', desc: 'Peak performance at dawn', icon: Sun, time: '5:00 AM - 11:00 AM' },
    { id: 'night-owl', label: 'Night Owl', desc: 'Thrives in the evening', icon: Moon, time: '6:00 PM - 12:00 AM' },
    { id: 'flexible', label: 'Adaptive Flow', desc: 'Flexible with any schedule', icon: Clock, time: 'Anytime' },
    { id: 'structured', label: 'Structured Rhythm', desc: 'Loves routine and planning', icon: Layers, time: '9:00 AM - 5:00 PM' },
    { id: 'creative-bursts', label: 'Creative Sprints', desc: 'Intense focused sessions', icon: Zap, time: 'Burst sessions' }
  ];

  const handleNext = async () => {
    if (currentStep === steps.length - 1) {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      onComplete(data);
    } else {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) setCurrentStep(currentStep - 1);
  };

  const currentStepData = steps[currentStep];
  const progress = ((currentStep + 1) / steps.length) * 100;

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <div className={`absolute inset-0 bg-gradient-to-br ${currentStepData.gradient} transition-all duration-1000`}>
        <div className="absolute inset-0 bg-black/20" />
        
        {/* Floating Particles */}
        {showParticles && (
          <div className="absolute inset-0">
            {particles.map((particle) => (
              <motion.div
                key={particle.id}
                className="absolute bg-white/30 rounded-full"
                style={{
                  left: `${particle.x}%`,
                  top: `${particle.y}%`,
                  width: particle.size,
                  height: particle.size,
                }}
                animate={{
                  y: [-20, -100],
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0],
                }}
                transition={{
                  duration: particle.duration,
                  delay: particle.delay,
                  repeat: Infinity,
                  ease: "easeOut"
                }}
              />
            ))}
          </div>
        )}

        {/* Mesh Gradient Overlay */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-0 left-0 w-72 h-72 bg-white rounded-full mix-blend-multiply filter blur-xl animate-pulse" />
          <div className="absolute top-0 right-0 w-72 h-72 bg-yellow-200 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000" />
          <div className="absolute bottom-0 left-0 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-2000" />
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Progress Bar */}
        <div className="w-full bg-white/20 h-1">
          <motion.div
            className="h-full bg-white"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>

        {/* Header */}
        <div className="flex items-center justify-between p-6">
          <Button
            onClick={handleBack}
            variant="ghost"
            size="sm"
            className={`text-white hover:bg-white/20 rounded-2xl ${currentStep === 0 ? 'invisible' : ''}`}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          
          <div className="text-center">
            <div className="text-white/80 text-sm font-medium">
              Step {currentStep + 1} of {steps.length}
            </div>
          </div>

          <div className="w-16" /> {/* Spacer */}
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col items-center justify-center px-6 pb-20">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-md w-full"
          >
            {/* Step Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="w-20 h-20 mx-auto mb-8 bg-white/20 backdrop-blur-xl rounded-3xl flex items-center justify-center"
            >
              <currentStepData.icon className="w-10 h-10 text-white" />
            </motion.div>

            {/* Title */}
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-4xl md:text-5xl font-bold text-white mb-4"
            >
              {currentStepData.title}
            </motion.h1>

            {/* Subtitle */}
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-xl text-white/90 mb-3"
            >
              {currentStepData.subtitle}
            </motion.h2>

            {/* Description */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="text-white/70 mb-8 leading-relaxed"
            >
              {currentStepData.description}
            </motion.p>

            {/* Step Content */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="mb-8"
            >
              {currentStep === 0 && <WelcomeStep />}
              {currentStep === 1 && <PersonalStep data={data} setData={setData} avatars={avatars} />}
              {currentStep === 2 && <GoalsStep data={data} setData={setData} goals={goals} />}
              {currentStep === 3 && <WorkStyleStep data={data} setData={setData} workStyles={workStyles} />}
              {currentStep === 4 && <MottoStep data={data} setData={setData} />}
              {currentStep === 5 && <NotificationsStep data={data} setData={setData} />}
            </motion.div>

            {/* Continue Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
            >
              <Button
                onClick={handleNext}
                disabled={isLoading || (currentStep === 1 && !data.name.trim())}
                className="w-full h-14 bg-white text-gray-900 hover:bg-white/90 rounded-2xl text-lg font-semibold shadow-xl transition-all duration-300 hover:scale-105"
              >
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-5 h-5 border-2 border-gray-400 border-t-gray-900 rounded-full animate-spin" />
                    <span>Setting up your workspace...</span>
                  </div>
                ) : currentStep === steps.length - 1 ? (
                  <div className="flex items-center space-x-2">
                    <Rocket className="w-5 h-5" />
                    <span>Launch FOCOS</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <span>Continue</span>
                    <ChevronRight className="w-5 h-5" />
                  </div>
                )}
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Step Components
const WelcomeStep = () => (
  <div className="space-y-6">
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.8, type: "spring" }}
      className="text-6xl mb-4"
    >
      🚀
    </motion.div>
    <div className="grid grid-cols-2 gap-4">
      {[
        { icon: Crown, label: 'Premium Design' },
        { icon: Shield, label: 'Secure & Private' },
        { icon: Zap, label: 'Lightning Fast' },
        { icon: Award, label: 'Award Winning' }
      ].map((feature, index) => (
        <motion.div
          key={feature.label}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 + index * 0.1 }}
          className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 text-center"
        >
          <feature.icon className="w-6 h-6 text-white mx-auto mb-2" />
          <p className="text-white/90 text-sm font-medium">{feature.label}</p>
        </motion.div>
      ))}
    </div>
  </div>
);

const PersonalStep = ({ data, setData, avatars }: any) => (
  <div className="space-y-6">
    {/* Avatar Selection */}
    <div>
      <p className="text-white/90 text-sm mb-4">Choose your avatar</p>
      <div className="grid grid-cols-4 gap-3 mb-6">
        {avatars.map((avatar: string) => (
          <motion.button
            key={avatar}
            onClick={() => setData({ ...data, avatar })}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            className={`w-12 h-12 rounded-2xl text-2xl flex items-center justify-center transition-all ${
              data.avatar === avatar
                ? 'bg-white text-gray-900 shadow-lg'
                : 'bg-white/20 hover:bg-white/30'
            }`}
          >
            {avatar}
          </motion.button>
        ))}
      </div>
    </div>

    {/* Name Input */}
    <div>
      <p className="text-white/90 text-sm mb-3">What should we call you?</p>
      <Input
        value={data.name}
        onChange={(e) => setData({ ...data, name: e.target.value })}
        placeholder="Enter your name"
        className="bg-white/20 border-white/30 text-white placeholder:text-white/60 rounded-2xl h-12 text-lg backdrop-blur-xl"
      />
    </div>
  </div>
);

const GoalsStep = ({ data, setData, goals }: any) => (
  <div className="space-y-4">
    <p className="text-white/90 text-sm mb-4">Select all that apply (choose 2-4 for best results)</p>
    <div className="grid grid-cols-1 gap-3">
      {goals.map((goal: any) => (
        <motion.button
          key={goal.id}
          onClick={() => {
            const newGoals = data.goals.includes(goal.id)
              ? data.goals.filter((g: string) => g !== goal.id)
              : [...data.goals, goal.id];
            setData({ ...data, goals: newGoals });
          }}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className={`p-4 rounded-2xl text-left transition-all backdrop-blur-xl ${
            data.goals.includes(goal.id)
              ? 'bg-white text-gray-900 shadow-lg'
              : 'bg-white/20 hover:bg-white/30'
          }`}
        >
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
              data.goals.includes(goal.id) ? 'bg-gray-100' : 'bg-white/20'
            }`}>
              <goal.icon className={`w-5 h-5 ${
                data.goals.includes(goal.id) ? 'text-gray-700' : 'text-white'
              }`} />
            </div>
            <div className="flex-1">
              <h3 className={`font-semibold ${
                data.goals.includes(goal.id) ? 'text-gray-900' : 'text-white'
              }`}>
                {goal.label}
              </h3>
              <p className={`text-sm ${
                data.goals.includes(goal.id) ? 'text-gray-600' : 'text-white/70'
              }`}>
                {goal.desc}
              </p>
            </div>
            {data.goals.includes(goal.id) && (
              <Check className="w-5 h-5 text-green-600" />
            )}
          </div>
        </motion.button>
      ))}
    </div>
  </div>
);

const WorkStyleStep = ({ data, setData, workStyles }: any) => (
  <div className="space-y-4">
    <p className="text-white/90 text-sm mb-4">When do you feel most productive?</p>
    <div className="space-y-3">
      {workStyles.map((style: any) => (
        <motion.button
          key={style.id}
          onClick={() => setData({ ...data, workStyle: style.id })}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className={`w-full p-4 rounded-2xl text-left transition-all backdrop-blur-xl ${
            data.workStyle === style.id
              ? 'bg-white text-gray-900 shadow-lg'
              : 'bg-white/20 hover:bg-white/30'
          }`}
        >
          <div className="flex items-center space-x-3">
            <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
              data.workStyle === style.id ? 'bg-gray-100' : 'bg-white/20'
            }`}>
              <style.icon className={`w-6 h-6 ${
                data.workStyle === style.id ? 'text-gray-700' : 'text-white'
              }`} />
            </div>
            <div className="flex-1">
              <h3 className={`font-semibold ${
                data.workStyle === style.id ? 'text-gray-900' : 'text-white'
              }`}>
                {style.label}
              </h3>
              <p className={`text-sm ${
                data.workStyle === style.id ? 'text-gray-600' : 'text-white/70'
              }`}>
                {style.desc}
              </p>
              <p className={`text-xs ${
                data.workStyle === style.id ? 'text-gray-500' : 'text-white/50'
              }`}>
                {style.time}
              </p>
            </div>
            {data.workStyle === style.id && (
              <Check className="w-5 h-5 text-green-600" />
            )}
          </div>
        </motion.button>
      ))}
    </div>
  </div>
);

const MottoStep = ({ data, setData }: any) => (
  <div className="space-y-6">
    <div>
      <p className="text-white/90 text-sm mb-4">What's your personal motto or favorite quote?</p>
      <Textarea
        value={data.motto}
        onChange={(e) => setData({ ...data, motto: e.target.value })}
        placeholder="e.g., 'Progress over perfection' or 'Dream big, work hard'"
        className="bg-white/20 border-white/30 text-white placeholder:text-white/60 rounded-2xl backdrop-blur-xl min-h-[100px] resize-none"
      />
    </div>

    {/* Suggested Mottos */}
    <div>
      <p className="text-white/90 text-sm mb-3">Or choose from these popular ones:</p>
      <div className="space-y-2">
        {[
          "Progress over perfection",
          "Dream big, work hard",
          "Focus on what matters",
          "Every day is a fresh start",
          "Small steps, big dreams"
        ].map((motto) => (
          <motion.button
            key={motto}
            onClick={() => setData({ ...data, motto })}
            whileHover={{ scale: 1.02 }}
            className="w-full p-3 bg-white/20 hover:bg-white/30 rounded-xl text-white/90 text-left transition-all backdrop-blur-xl"
          >
            "{motto}"
          </motion.button>
        ))}
      </div>
    </div>
  </div>
);

const NotificationsStep = ({ data, setData }: any) => (
  <div className="space-y-6">
    <div className="text-center">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2, type: "spring" }}
        className="w-20 h-20 mx-auto mb-4 bg-white/20 rounded-3xl flex items-center justify-center"
      >
        <Bell className="w-10 h-10 text-white" />
      </motion.div>
    </div>

    <div className="space-y-4">
      <motion.button
        onClick={() => setData({ ...data, notificationsEnabled: true })}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className={`w-full p-4 rounded-2xl text-left transition-all backdrop-blur-xl ${
          data.notificationsEnabled
            ? 'bg-white text-gray-900 shadow-lg'
            : 'bg-white/20 hover:bg-white/30'
        }`}
      >
        <div className="flex items-center space-x-3">
          <Bell className={`w-6 h-6 ${
            data.notificationsEnabled ? 'text-gray-700' : 'text-white'
          }`} />
          <div>
            <h3 className={`font-semibold ${
              data.notificationsEnabled ? 'text-gray-900' : 'text-white'
            }`}>
              Enable Smart Notifications
            </h3>
            <p className={`text-sm ${
              data.notificationsEnabled ? 'text-gray-600' : 'text-white/70'
            }`}>
              Get gentle reminders and productivity insights
            </p>
          </div>
          {data.notificationsEnabled && (
            <Check className="w-5 h-5 text-green-600" />
          )}
        </div>
      </motion.button>

      <motion.button
        onClick={() => setData({ ...data, notificationsEnabled: false })}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className={`w-full p-4 rounded-2xl text-left transition-all backdrop-blur-xl ${
          !data.notificationsEnabled
            ? 'bg-white text-gray-900 shadow-lg'
            : 'bg-white/20 hover:bg-white/30'
        }`}
      >
        <div className="flex items-center space-x-3">
          <BellOff className={`w-6 h-6 ${
            !data.notificationsEnabled ? 'text-gray-700' : 'text-white'
          }`} />
          <div>
            <h3 className={`font-semibold ${
              !data.notificationsEnabled ? 'text-gray-900' : 'text-white'
            }`}>
              Silent Mode
            </h3>
            <p className={`text-sm ${
              !data.notificationsEnabled ? 'text-gray-600' : 'text-white/70'
            }`}>
              I'll manage my own schedule
            </p>
          </div>
          {!data.notificationsEnabled && (
            <Check className="w-5 h-5 text-green-600" />
          )}
        </div>
      </motion.button>
    </div>
  </div>
);

export default PremiumOnboarding;
