import React from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';

interface MobileCardProps {
  title?: string;
  subtitle?: string;
  icon?: LucideIcon;
  value?: string | number;
  unit?: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
  children?: React.ReactNode;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'gradient' | 'glass' | 'minimal';
  onClick?: () => void;
  badge?: string;
  badgeColor?: string;
}

const MobileCard: React.FC<MobileCardProps> = ({
  title,
  subtitle,
  icon: Icon,
  value,
  unit,
  trend,
  trendValue,
  children,
  className = '',
  headerClassName = '',
  contentClassName = '',
  size = 'md',
  variant = 'default',
  onClick,
  badge,
  badgeColor = 'bg-blue-100 text-blue-800'
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm': return 'p-2 sm:p-3';
      case 'md': return 'p-3 sm:p-4';
      case 'lg': return 'p-4 sm:p-6';
      default: return 'p-3 sm:p-4';
    }
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'gradient':
        return 'bg-gradient-to-br from-white to-gray-50 border-0 shadow-lg';
      case 'glass':
        return 'bg-white/80 backdrop-blur-sm border-white/20 shadow-xl';
      case 'minimal':
        return 'bg-white border border-gray-200 shadow-sm';
      default:
        return 'bg-white border-0 shadow-md';
    }
  };

  const getTrendIcon = () => {
    if (trend === 'up') return '↗️';
    if (trend === 'down') return '↘️';
    return '➡️';
  };

  const getTrendColor = () => {
    if (trend === 'up') return 'text-green-600';
    if (trend === 'down') return 'text-red-600';
    return 'text-gray-600';
  };

  const cardClasses = `
    ${getVariantClasses()}
    ${getSizeClasses()}
    ${onClick ? 'cursor-pointer hover:shadow-lg transition-all duration-200' : ''}
    ${className}
  `.trim();

  return (
    <Card className={cardClasses} onClick={onClick}>
      {(title || Icon || badge) && (
        <CardHeader className={`pb-2 ${headerClassName}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 min-w-0 flex-1">
              {Icon && (
                <div className="flex-shrink-0">
                  <Icon className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
                </div>
              )}
              <div className="min-w-0 flex-1">
                {title && (
                  <CardTitle className="text-sm sm:text-base font-medium text-gray-800 truncate">
                    {title}
                  </CardTitle>
                )}
                {subtitle && (
                  <p className="text-xs sm:text-sm text-gray-600 truncate mt-1">
                    {subtitle}
                  </p>
                )}
              </div>
            </div>
            {badge && (
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${badgeColor} flex-shrink-0`}>
                {badge}
              </span>
            )}
          </div>
        </CardHeader>
      )}
      
      <CardContent className={`${getSizeClasses()} pt-0 ${contentClassName}`}>
        {(value !== undefined || trendValue) && (
          <div className="flex items-center justify-between mb-2">
            <div className="min-w-0 flex-1">
              {value !== undefined && (
                <div className="flex items-baseline space-x-1">
                  <span className="text-lg sm:text-2xl font-bold text-gray-900 truncate">
                    {value}
                  </span>
                  {unit && (
                    <span className="text-xs sm:text-sm text-gray-600 flex-shrink-0">
                      {unit}
                    </span>
                  )}
                </div>
              )}
            </div>
            {trendValue && (
              <div className={`flex items-center space-x-1 ${getTrendColor()} flex-shrink-0`}>
                <span className="text-xs">{getTrendIcon()}</span>
                <span className="text-xs font-medium">{trendValue}</span>
              </div>
            )}
          </div>
        )}
        
        {children && (
          <div className="min-w-0">
            {children}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MobileCard;
