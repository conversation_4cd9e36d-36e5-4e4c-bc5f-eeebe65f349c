import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home, FileText, CheckSquare, Heart, DollarSign, RotateCcw, Target, Clock,
  CalendarDays, Activity, Settings, Zap, Brain, Sparkles, BarChart3, Bot,
  Wrench, Skull, Timer, Menu, X, ChevronRight, Folder, FolderOpen,
  BookOpen, Lightbulb, Shield, Users, Palette, Smartphone, Cpu, Kanban
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onToggle }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['core']);

  const toggleCategory = (category: string) => {
    setExpandedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const navigationCategories = [
    {
      id: 'core',
      name: 'Core Features',
      icon: Home,
      items: [
        { path: '/', icon: Home, label: 'Dashboard', badge: null },
        { path: '/tasks', icon: CheckSquare, label: 'Tasks', badge: null },
        { path: '/habits', icon: RotateCcw, label: 'Habits', badge: null },
        { path: '/goals', icon: Target, label: 'Goals', badge: null },
        { path: '/calendar', icon: CalendarDays, label: 'Calendar', badge: null },
        { path: '/notes', icon: FileText, label: 'Notes', badge: null },
      ]
    },
    {
      id: 'wellness',
      name: 'Health & Wellness',
      icon: Activity,
      items: [
        { path: '/health', icon: Activity, label: 'Health Tracker', badge: 'Enhanced' },
        { path: '/mood', icon: Heart, label: 'Mood Tracker', badge: null },
        { path: '/wellness', icon: Sparkles, label: 'Wellness Tools', badge: null },
        { path: '/routines', icon: RotateCcw, label: 'Routines', badge: null },
        { path: '/spiritual', icon: Sparkles, label: 'Spiritual Hub', badge: null },
      ]
    },
    {
      id: 'productivity',
      name: 'Productivity Tools',
      icon: Zap,
      items: [
        { path: '/enhanced-kanban', icon: Kanban, label: 'Enhanced Kanban', badge: 'Pro' },
        { path: '/advanced-goals', icon: Brain, label: 'Advanced Goals', badge: 'Pro' },
        { path: '/productivity-dashboard', icon: BarChart3, label: 'Analytics', badge: 'Pro' },
        { path: '/automation', icon: Bot, label: 'Automation', badge: 'Pro' },
        { path: '/pomodoro', icon: Timer, label: 'Pomodoro Timer', badge: 'New' },
        { path: '/time-tracker', icon: Clock, label: 'Time Tracker', badge: null },
      ]
    },
    {
      id: 'finance',
      name: 'Financial Tools',
      icon: DollarSign,
      items: [
        { path: '/finance', icon: DollarSign, label: 'Finance Manager', badge: 'Enhanced' },
      ]
    },
    {
      id: 'tools',
      name: 'Utility Tools',
      icon: Wrench,
      items: [
        { path: '/toolbox', icon: Wrench, label: 'Toolbox', badge: '20+ Tools' },
        { path: '/death-clock', icon: Skull, label: 'Death Clock', badge: 'Unique' },
        { path: '/quick-capture', icon: FileText, label: 'Quick Capture', badge: null },
      ]
    },
    {
      id: 'knowledge',
      name: 'Knowledge & Learning',
      icon: BookOpen,
      items: [
        { path: '/journal', icon: FileText, label: 'Journaling Hub', badge: null },
        { path: '/knowledge-vault', icon: BookOpen, label: 'Knowledge Vault', badge: null },
        { path: '/planner', icon: CalendarDays, label: 'Planner', badge: null },
      ]
    },
    {
      id: 'projects',
      name: 'Project Management',
      icon: Folder,
      items: [
        { path: '/projects', icon: Folder, label: 'Project Board', badge: null },
        { path: '/goal-management', icon: Target, label: 'Goal Management', badge: null },
      ]
    },
    {
      id: 'customization',
      name: 'Customization',
      icon: Palette,
      items: [
        { path: '/theme-customizer', icon: Palette, label: 'Theme Customizer', badge: null },
        { path: '/settings', icon: Settings, label: 'Settings', badge: null },
      ]
    }
  ];

  const getBadgeColor = (badge: string | null) => {
    if (!badge) return '';
    switch (badge.toLowerCase()) {
      case 'pro': return 'bg-purple-500 text-white';
      case 'new': return 'bg-green-500 text-white';
      case 'enhanced': return 'bg-blue-500 text-white';
      case 'unique': return 'bg-orange-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  return (
    <>
      {/* Mobile Overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={onToggle}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.div
        initial={{ x: -320 }}
        animate={{ x: isOpen ? 0 : -320 }}
        transition={{ type: "spring", damping: 25, stiffness: 200 }}
        className={`
          fixed left-0 top-0 h-full w-80 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl
          border-r border-gray-200 dark:border-gray-700 z-50 overflow-y-auto
          lg:relative lg:translate-x-0 lg:z-10
          ${isOpen ? 'lg:block' : 'lg:hidden'}
        `}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="font-bold text-gray-900 dark:text-white">Spark Life</h2>
              <p className="text-xs text-gray-500 dark:text-gray-400">Compass</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="lg:hidden"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Navigation */}
        <div className="p-4 space-y-2">
          {navigationCategories.map((category) => {
            const isExpanded = expandedCategories.includes(category.id);
            const CategoryIcon = category.icon;

            return (
              <div key={category.id} className="space-y-1">
                {/* Category Header */}
                <Button
                  variant="ghost"
                  onClick={() => toggleCategory(category.id)}
                  className="w-full justify-between p-2 h-auto text-left hover:bg-gray-100 dark:hover:bg-gray-800"
                >
                  <div className="flex items-center space-x-3">
                    <CategoryIcon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    <span className="font-medium text-gray-700 dark:text-gray-300 text-sm">
                      {category.name}
                    </span>
                  </div>
                  <motion.div
                    animate={{ rotate: isExpanded ? 90 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronRight className="w-4 h-4 text-gray-400" />
                  </motion.div>
                </Button>

                {/* Category Items */}
                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="overflow-hidden ml-4 space-y-1"
                    >
                      {category.items.map((item) => {
                        const isActive = location.pathname === item.path;
                        const ItemIcon = item.icon;

                        return (
                          <motion.button
                            key={item.path}
                            onClick={() => {
                              navigate(item.path);
                              if (window.innerWidth < 1024) onToggle();
                            }}
                            className={`w-full flex items-center justify-between p-2 rounded-lg text-left transition-all ${
                              isActive
                                ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg'
                                : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400'
                            }`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <div className="flex items-center space-x-3">
                              <ItemIcon className={`w-4 h-4 ${isActive ? 'text-white' : ''}`} />
                              <span className={`text-sm font-medium ${isActive ? 'text-white' : ''}`}>
                                {item.label}
                              </span>
                            </div>
                            {item.badge && (
                              <Badge
                                variant="secondary"
                                className={`text-xs px-2 py-0.5 ${getBadgeColor(item.badge)}`}
                              >
                                {item.badge}
                              </Badge>
                            )}
                          </motion.button>
                        );
                      })}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 dark:border-gray-700 bg-white/95 dark:bg-gray-900/95">
          <div className="text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Spark Life Compass v2.0
            </p>
            <p className="text-xs text-gray-400 dark:text-gray-500">
              Your Complete Life Management System
            </p>
          </div>
        </div>
      </motion.div>
    </>
  );
};

export default Sidebar;
