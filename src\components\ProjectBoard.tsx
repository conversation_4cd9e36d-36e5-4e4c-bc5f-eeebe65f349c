import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus, MoreHorizontal, Calendar, User, Flag, Clock,
  CheckCircle, Circle, AlertCircle, ArrowRight, Trash2,
  Edit3, Tag, Link, MessageSquare, Paperclip
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { GlassmorphCard } from '@/components/ui/glassmorphcard';

interface Subtask {
  id: string;
  title: string;
  completed: boolean;
  assignee?: string;
  dueDate?: string;
}

interface ProjectTask {
  id: string;
  title: string;
  description: string;
  status: 'backlog' | 'todo' | 'in-progress' | 'review' | 'done';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignee?: string;
  dueDate?: string;
  tags: string[];
  subtasks: Subtask[];
  dependencies: string[];
  estimatedHours?: number;
  actualHours?: number;
  createdAt: string;
  updatedAt: string;
  comments: Array<{
    id: string;
    text: string;
    author: string;
    timestamp: string;
  }>;
  attachments: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
  }>;
}

interface Project {
  id: string;
  name: string;
  description: string;
  color: string;
  tasks: ProjectTask[];
  teamMembers: string[];
  createdAt: string;
}

const ProjectBoard = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [showNewProject, setShowNewProject] = useState(false);
  const [showNewTask, setShowNewTask] = useState(false);
  const [selectedTask, setSelectedTask] = useState<ProjectTask | null>(null);
  const [draggedTask, setDraggedTask] = useState<ProjectTask | null>(null);

  const [newProject, setNewProject] = useState({
    name: '',
    description: '',
    color: '#8B5CF6'
  });

  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    priority: 'medium' as ProjectTask['priority'],
    assignee: '',
    dueDate: '',
    tags: '',
    estimatedHours: 0
  });

  const columns = [
    { id: 'backlog', title: 'Backlog', color: 'bg-gray-100' },
    { id: 'todo', title: 'To Do', color: 'bg-blue-100' },
    { id: 'in-progress', title: 'In Progress', color: 'bg-yellow-100' },
    { id: 'review', title: 'Review', color: 'bg-purple-100' },
    { id: 'done', title: 'Done', color: 'bg-green-100' }
  ];

  const priorityColors = {
    low: 'bg-green-100 text-green-800',
    medium: 'bg-yellow-100 text-yellow-800',
    high: 'bg-orange-100 text-orange-800',
    urgent: 'bg-red-100 text-red-800'
  };

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = () => {
    const saved = JSON.parse(localStorage.getItem('projects') || '[]');
    setProjects(saved);
    if (saved.length > 0 && !selectedProject) {
      setSelectedProject(saved[0]);
    }
  };

  const saveProjects = (updatedProjects: Project[]) => {
    setProjects(updatedProjects);
    localStorage.setItem('projects', JSON.stringify(updatedProjects));
  };

  const createProject = () => {
    if (!newProject.name.trim()) return;

    const project: Project = {
      id: Date.now().toString(),
      name: newProject.name,
      description: newProject.description,
      color: newProject.color,
      tasks: [],
      teamMembers: [],
      createdAt: new Date().toISOString()
    };

    const updatedProjects = [...projects, project];
    saveProjects(updatedProjects);
    setSelectedProject(project);
    setNewProject({ name: '', description: '', color: '#8B5CF6' });
    setShowNewProject(false);
  };

  const createTask = () => {
    if (!newTask.title.trim() || !selectedProject) return;

    const task: ProjectTask = {
      id: Date.now().toString(),
      title: newTask.title,
      description: newTask.description,
      status: 'backlog',
      priority: newTask.priority,
      assignee: newTask.assignee || undefined,
      dueDate: newTask.dueDate || undefined,
      tags: newTask.tags.split(',').map(t => t.trim()).filter(t => t),
      subtasks: [],
      dependencies: [],
      estimatedHours: newTask.estimatedHours || undefined,
      actualHours: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      comments: [],
      attachments: []
    };

    const updatedProject = {
      ...selectedProject,
      tasks: [...selectedProject.tasks, task]
    };

    const updatedProjects = projects.map(p => 
      p.id === selectedProject.id ? updatedProject : p
    );

    saveProjects(updatedProjects);
    setSelectedProject(updatedProject);
    setNewTask({
      title: '',
      description: '',
      priority: 'medium',
      assignee: '',
      dueDate: '',
      tags: '',
      estimatedHours: 0
    });
    setShowNewTask(false);
  };

  const moveTask = (taskId: string, newStatus: ProjectTask['status']) => {
    if (!selectedProject) return;

    const updatedTasks = selectedProject.tasks.map(task =>
      task.id === taskId 
        ? { ...task, status: newStatus, updatedAt: new Date().toISOString() }
        : task
    );

    const updatedProject = { ...selectedProject, tasks: updatedTasks };
    const updatedProjects = projects.map(p => 
      p.id === selectedProject.id ? updatedProject : p
    );

    saveProjects(updatedProjects);
    setSelectedProject(updatedProject);
  };

  const getTasksByStatus = (status: ProjectTask['status']) => {
    return selectedProject?.tasks.filter(task => task.status === status) || [];
  };

  const getProjectStats = () => {
    if (!selectedProject) return { total: 0, completed: 0, inProgress: 0 };
    
    const total = selectedProject.tasks.length;
    const completed = selectedProject.tasks.filter(t => t.status === 'done').length;
    const inProgress = selectedProject.tasks.filter(t => t.status === 'in-progress').length;
    
    return { total, completed, inProgress };
  };

  const handleDragStart = (task: ProjectTask) => {
    setDraggedTask(task);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, status: ProjectTask['status']) => {
    e.preventDefault();
    if (draggedTask) {
      moveTask(draggedTask.id, status);
      setDraggedTask(null);
    }
  };

  const stats = getProjectStats();

  return (
    <div className="min-h-screen p-4 pb-24 bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <h1 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
          Project Board 📋
        </h1>
        <p className="text-gray-600">Manage projects with Kanban-style boards</p>
      </motion.div>

      {/* Project Selector */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <select
            value={selectedProject?.id || ''}
            onChange={(e) => {
              const project = projects.find(p => p.id === e.target.value);
              setSelectedProject(project || null);
            }}
            className="px-4 py-2 border rounded-lg bg-white"
          >
            <option value="">Select a project</option>
            {projects.map(project => (
              <option key={project.id} value={project.id}>
                {project.name}
              </option>
            ))}
          </select>
          
          <Button
            onClick={() => setShowNewProject(true)}
            className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Project
          </Button>
        </div>

        {selectedProject && (
          <Button
            onClick={() => setShowNewTask(true)}
            variant="outline"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Task
          </Button>
        )}
      </div>

      {selectedProject && (
        <>
          {/* Project Stats */}
          <GlassmorphCard className="p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h2 className="text-xl font-bold">{selectedProject.name}</h2>
                <p className="text-gray-600">{selectedProject.description}</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold">{stats.completed}/{stats.total}</div>
                <div className="text-sm text-gray-600">Tasks Completed</div>
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-lg font-bold text-blue-600">{stats.total}</div>
                <div className="text-sm text-blue-600">Total Tasks</div>
              </div>
              <div className="text-center p-3 bg-yellow-50 rounded-lg">
                <div className="text-lg font-bold text-yellow-600">{stats.inProgress}</div>
                <div className="text-sm text-yellow-600">In Progress</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-lg font-bold text-green-600">{stats.completed}</div>
                <div className="text-sm text-green-600">Completed</div>
              </div>
            </div>
            
            <Progress value={stats.total > 0 ? (stats.completed / stats.total) * 100 : 0} className="h-3" />
          </GlassmorphCard>

          {/* Kanban Board */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 overflow-x-auto">
            {columns.map(column => (
              <div
                key={column.id}
                className={`${column.color} rounded-lg p-4 min-h-96`}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, column.id as ProjectTask['status'])}
              >
                <h3 className="font-semibold mb-4 flex items-center justify-between">
                  {column.title}
                  <Badge variant="secondary">
                    {getTasksByStatus(column.id as ProjectTask['status']).length}
                  </Badge>
                </h3>
                
                <div className="space-y-3">
                  {getTasksByStatus(column.id as ProjectTask['status']).map(task => (
                    <motion.div
                      key={task.id}
                      layout
                      draggable
                      onDragStart={() => handleDragStart(task)}
                      onClick={() => setSelectedTask(task)}
                      className="bg-white rounded-lg p-4 shadow-sm cursor-pointer hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium text-sm">{task.title}</h4>
                        <Badge className={priorityColors[task.priority]}>
                          {task.priority}
                        </Badge>
                      </div>
                      
                      {task.description && (
                        <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                          {task.description}
                        </p>
                      )}
                      
                      {task.subtasks.length > 0 && (
                        <div className="flex items-center text-xs text-gray-500 mb-2">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          {task.subtasks.filter(s => s.completed).length}/{task.subtasks.length}
                        </div>
                      )}
                      
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        {task.dueDate && (
                          <div className="flex items-center">
                            <Calendar className="w-3 h-3 mr-1" />
                            {new Date(task.dueDate).toLocaleDateString()}
                          </div>
                        )}
                        
                        {task.assignee && (
                          <div className="flex items-center">
                            <User className="w-3 h-3 mr-1" />
                            {task.assignee}
                          </div>
                        )}
                      </div>
                      
                      {task.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {task.tags.map(tag => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </>
      )}

      {/* New Project Modal */}
      <AnimatePresence>
        {showNewProject && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-6 w-full max-w-md"
            >
              <h3 className="text-xl font-bold mb-4">Create New Project</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Project Name</label>
                  <Input
                    value={newProject.name}
                    onChange={(e) => setNewProject(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter project name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Description</label>
                  <Textarea
                    value={newProject.description}
                    onChange={(e) => setNewProject(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Project description"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Color</label>
                  <div className="flex space-x-2">
                    {['#8B5CF6', '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5A2B'].map(color => (
                      <button
                        key={color}
                        onClick={() => setNewProject(prev => ({ ...prev, color }))}
                        className={`w-8 h-8 rounded-full border-2 ${
                          newProject.color === color ? 'border-gray-800' : 'border-gray-300'
                        }`}
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <Button onClick={createProject} className="flex-1">
                  Create Project
                </Button>
                <Button
                  onClick={() => setShowNewProject(false)}
                  variant="outline"
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* New Task Modal */}
      <AnimatePresence>
        {showNewTask && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-6 w-full max-w-lg max-h-[90vh] overflow-y-auto"
            >
              <h3 className="text-xl font-bold mb-4">Create New Task</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Task Title</label>
                  <Input
                    value={newTask.title}
                    onChange={(e) => setNewTask(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Enter task title"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Description</label>
                  <Textarea
                    value={newTask.description}
                    onChange={(e) => setNewTask(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Task description"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Priority</label>
                    <select
                      value={newTask.priority}
                      onChange={(e) => setNewTask(prev => ({ ...prev, priority: e.target.value as ProjectTask['priority'] }))}
                      className="w-full px-3 py-2 border rounded-lg"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Estimated Hours</label>
                    <Input
                      type="number"
                      value={newTask.estimatedHours}
                      onChange={(e) => setNewTask(prev => ({ ...prev, estimatedHours: parseInt(e.target.value) || 0 }))}
                      placeholder="0"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Assignee</label>
                    <Input
                      value={newTask.assignee}
                      onChange={(e) => setNewTask(prev => ({ ...prev, assignee: e.target.value }))}
                      placeholder="Assign to..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Due Date</label>
                    <Input
                      type="date"
                      value={newTask.dueDate}
                      onChange={(e) => setNewTask(prev => ({ ...prev, dueDate: e.target.value }))}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Tags (comma-separated)</label>
                  <Input
                    value={newTask.tags}
                    onChange={(e) => setNewTask(prev => ({ ...prev, tags: e.target.value }))}
                    placeholder="frontend, urgent, feature"
                  />
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <Button onClick={createTask} className="flex-1">
                  Create Task
                </Button>
                <Button
                  onClick={() => setShowNewTask(false)}
                  variant="outline"
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ProjectBoard;
