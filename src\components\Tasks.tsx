import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus, Clock, Target, AlertCircle, CheckCircle, Circle, Trash2,
  Calendar, Grid, List, Brain, Search, Filter, Tag, Inbox,
  Star, MoreHorizontal, Edit, Archive, Timer, Share2,
  MapPin, Repeat, Link, ChevronDown, ChevronRight, Zap,
  FolderOpen, Bell, Users, Palette, SortAsc, Eye, Pause
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

interface Subtask {
  id: string;
  title: string;
  completed: boolean;
  createdAt: string;
}

interface Task {
  id: string;
  title: string;
  description: string;
  priority: 'urgent' | 'high' | 'medium' | 'low';
  status: 'todo' | 'in-progress' | 'completed' | 'archived';
  dueDate?: string;
  reminderDate?: string;
  tags: string[];
  category: string;
  colorLabel: string;
  location?: string;
  project: string;
  workspace: string;
  subtasks: Subtask[];
  parentTaskId?: string;
  linkedTasks: string[];
  isRecurring: boolean;
  recurringPattern?: 'daily' | 'weekly' | 'monthly';
  assignedTo?: string;
  aiScore: number;
  estimatedTime?: number;
  actualTime?: number;
  createdAt: string;
  updatedAt: string;
  snoozedUntil?: string;
  isExpanded?: boolean;
}

interface Project {
  id: string;
  name: string;
  color: string;
  workspace: string;
}

interface Workspace {
  id: string;
  name: string;
  color: string;
}

const Tasks = () => {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const saved = localStorage.getItem('billionDollarDarkMode');
    return saved ? JSON.parse(saved) : false;
  });

  // Core State
  const [tasks, setTasks] = useState<Task[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [inboxTasks, setInboxTasks] = useState<Task[]>([]);

  // UI State
  const [view, setView] = useState<'list' | 'kanban' | 'calendar'>('list');
  const [showForm, setShowForm] = useState(false);
  const [showInbox, setShowInbox] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [showTaskDetails, setShowTaskDetails] = useState(false);

  // Filter State
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProject, setSelectedProject] = useState('');
  const [selectedWorkspace, setSelectedWorkspace] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedPriority, setSelectedPriority] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<'ai' | 'due' | 'priority' | 'created' | 'updated'>('ai');
  const [showCompleted, setShowCompleted] = useState(true);
  const [showArchived, setShowArchived] = useState(false);

  // Form State
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    priority: 'medium' as Task['priority'],
    dueDate: '',
    reminderDate: '',
    tags: [] as string[],
    category: '',
    colorLabel: '#3B82F6',
    location: '',
    project: '',
    workspace: '',
    assignedTo: '',
    estimatedTime: 60,
    isRecurring: false,
    recurringPattern: 'weekly' as Task['recurringPattern'],
    linkedTasks: [] as string[]
  });

  useEffect(() => {
    loadData();
    setupNotifications();
  }, []);

  const loadData = () => {
    const savedTasks = localStorage.getItem('smartTasks');
    const savedProjects = localStorage.getItem('taskProjects');
    const savedWorkspaces = localStorage.getItem('taskWorkspaces');
    const savedInbox = localStorage.getItem('taskInbox');

    if (savedTasks) setTasks(JSON.parse(savedTasks));
    if (savedProjects) setProjects(JSON.parse(savedProjects));
    if (savedWorkspaces) setWorkspaces(JSON.parse(savedWorkspaces));
    if (savedInbox) setInboxTasks(JSON.parse(savedInbox));

    // Initialize default workspace and project if none exist
    if (!savedWorkspaces) {
      const defaultWorkspace = { id: 'default', name: 'Personal', color: '#3B82F6' };
      setWorkspaces([defaultWorkspace]);
      localStorage.setItem('taskWorkspaces', JSON.stringify([defaultWorkspace]));
    }
    if (!savedProjects) {
      const defaultProject = { id: 'default', name: 'General', color: '#10B981', workspace: 'default' };
      setProjects([defaultProject]);
      localStorage.setItem('taskProjects', JSON.stringify([defaultProject]));
    }
  };

  const saveTasks = (updatedTasks: Task[]) => {
    setTasks(updatedTasks);
    localStorage.setItem('smartTasks', JSON.stringify(updatedTasks));
  };

  const setupNotifications = () => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  // AI Scoring Algorithm
  const calculateAIScore = useCallback((task: Partial<Task>) => {
    let score = 50; // Base score

    // Priority weight (40% of score)
    const priorityWeights = { urgent: 40, high: 30, medium: 20, low: 10 };
    score += priorityWeights[task.priority || 'medium'];

    // Due date urgency (30% of score)
    if (task.dueDate) {
      const daysUntilDue = Math.ceil((new Date(task.dueDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24));
      if (daysUntilDue < 0) score += 30; // Overdue
      else if (daysUntilDue <= 1) score += 25; // Due today/tomorrow
      else if (daysUntilDue <= 3) score += 20; // Due this week
      else if (daysUntilDue <= 7) score += 15; // Due next week
      else score += 10; // Future
    }

    // Estimated time impact (20% of score)
    if (task.estimatedTime) {
      if (task.estimatedTime <= 30) score += 20; // Quick wins
      else if (task.estimatedTime <= 120) score += 15; // Medium tasks
      else score += 10; // Long tasks
    }

    // Subtasks completion (10% of score)
    if (task.subtasks && task.subtasks.length > 0) {
      const completedSubtasks = task.subtasks.filter(st => st.completed).length;
      const completionRatio = completedSubtasks / task.subtasks.length;
      score += completionRatio * 10;
    }

    return Math.min(100, Math.max(0, score));
  }, []);

  // Task CRUD Operations
  const addTask = () => {
    if (!newTask.title.trim()) return;

    const task: Task = {
      id: Date.now().toString(),
      title: newTask.title,
      description: newTask.description,
      priority: newTask.priority,
      status: 'todo',
      dueDate: newTask.dueDate,
      reminderDate: newTask.reminderDate,
      tags: newTask.tags,
      category: newTask.category,
      colorLabel: newTask.colorLabel,
      location: newTask.location,
      project: newTask.project || 'default',
      workspace: newTask.workspace || 'default',
      subtasks: [],
      linkedTasks: newTask.linkedTasks,
      isRecurring: newTask.isRecurring,
      recurringPattern: newTask.recurringPattern,
      assignedTo: newTask.assignedTo,
      aiScore: 0,
      estimatedTime: newTask.estimatedTime,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    task.aiScore = calculateAIScore(task);

    const updatedTasks = [...tasks, task];
    saveTasks(updatedTasks);

    // Reset form
    setNewTask({
      title: '',
      description: '',
      priority: 'medium',
      dueDate: '',
      reminderDate: '',
      tags: [],
      category: '',
      colorLabel: '#3B82F6',
      location: '',
      project: '',
      workspace: '',
      assignedTo: '',
      estimatedTime: 60,
      isRecurring: false,
      recurringPattern: 'weekly',
      linkedTasks: []
    });
    setShowForm(false);

    // Create recurring task if needed
    if (task.isRecurring) {
      createRecurringTask(task);
    }
  };

  const toggleTaskStatus = (taskId: string) => {
    const updatedTasks = tasks.map(task => {
      if (task.id === taskId) {
        const newStatus = task.status === 'completed' ? 'todo' : 'completed';
        const updatedTask = {
          ...task,
          status: newStatus,
          updatedAt: new Date().toISOString()
        };

        // Update AI score
        updatedTask.aiScore = calculateAIScore(updatedTask);

        return updatedTask;
      }
      return task;
    });
    saveTasks(updatedTasks);
  };

  const deleteTask = (taskId: string) => {
    const updatedTasks = tasks.filter(task => task.id !== taskId);
    saveTasks(updatedTasks);
  };

  const archiveTask = (taskId: string) => {
    const updatedTasks = tasks.map(task =>
      task.id === taskId
        ? { ...task, status: 'archived' as Task['status'], updatedAt: new Date().toISOString() }
        : task
    );
    saveTasks(updatedTasks);
  };

  const snoozeTask = (taskId: string, hours: number) => {
    const snoozeUntil = new Date();
    snoozeUntil.setHours(snoozeUntil.getHours() + hours);

    const updatedTasks = tasks.map(task =>
      task.id === taskId
        ? { ...task, snoozedUntil: snoozeUntil.toISOString(), updatedAt: new Date().toISOString() }
        : task
    );
    saveTasks(updatedTasks);
  };

  const createRecurringTask = (originalTask: Task) => {
    const nextDate = new Date(originalTask.dueDate || Date.now());

    switch (originalTask.recurringPattern) {
      case 'daily':
        nextDate.setDate(nextDate.getDate() + 1);
        break;
      case 'weekly':
        nextDate.setDate(nextDate.getDate() + 7);
        break;
      case 'monthly':
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
    }

    const recurringTask: Task = {
      ...originalTask,
      id: Date.now().toString() + '_recurring',
      status: 'todo',
      dueDate: nextDate.toISOString().split('T')[0],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      subtasks: originalTask.subtasks.map(st => ({ ...st, completed: false }))
    };

    setTimeout(() => {
      const currentTasks = JSON.parse(localStorage.getItem('smartTasks') || '[]');
      saveTasks([...currentTasks, recurringTask]);
    }, 1000);
  };

  // Subtask Management
  const addSubtask = (taskId: string, subtaskTitle: string) => {
    const updatedTasks = tasks.map(task => {
      if (task.id === taskId) {
        const newSubtask: Subtask = {
          id: Date.now().toString(),
          title: subtaskTitle,
          completed: false,
          createdAt: new Date().toISOString()
        };
        const updatedTask = {
          ...task,
          subtasks: [...task.subtasks, newSubtask],
          updatedAt: new Date().toISOString()
        };
        updatedTask.aiScore = calculateAIScore(updatedTask);
        return updatedTask;
      }
      return task;
    });
    saveTasks(updatedTasks);
  };

  const toggleSubtask = (taskId: string, subtaskId: string) => {
    const updatedTasks = tasks.map(task => {
      if (task.id === taskId) {
        const updatedSubtasks = task.subtasks.map(st =>
          st.id === subtaskId ? { ...st, completed: !st.completed } : st
        );
        const updatedTask = {
          ...task,
          subtasks: updatedSubtasks,
          updatedAt: new Date().toISOString()
        };
        updatedTask.aiScore = calculateAIScore(updatedTask);
        return updatedTask;
      }
      return task;
    });
    saveTasks(updatedTasks);
  };

  // Filtering and Sorting
  const getFilteredTasks = () => {
    let filtered = tasks.filter(task => {
      // Basic filters
      const matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           task.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           task.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesProject = !selectedProject || selectedProject === 'all' || task.project === selectedProject;
      const matchesWorkspace = !selectedWorkspace || task.workspace === selectedWorkspace;
      const matchesCategory = !selectedCategory || task.category === selectedCategory;
      const matchesPriority = !selectedPriority || task.priority === selectedPriority;
      const matchesStatus = !selectedStatus || task.status === selectedStatus;
      const matchesTags = selectedTags.length === 0 ||
                         selectedTags.some(tag => task.tags.includes(tag));

      // Status filters
      const isCompleted = task.status === 'completed';
      const isArchived = task.status === 'archived';
      const isSnoozed = task.snoozedUntil && new Date(task.snoozedUntil) > new Date();

      if (!showCompleted && isCompleted) return false;
      if (!showArchived && isArchived) return false;
      if (isSnoozed) return false;

      return matchesSearch && matchesProject && matchesWorkspace &&
             matchesCategory && matchesPriority && matchesStatus && matchesTags;
    });

    // Sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'ai':
          return b.aiScore - a.aiScore;
        case 'due':
          if (!a.dueDate && !b.dueDate) return 0;
          if (!a.dueDate) return 1;
          if (!b.dueDate) return -1;
          return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
        case 'priority':
          const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'updated':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        default:
          return 0;
      }
    });

    return filtered;
  };

  const getTaskStats = () => {
    const total = tasks.filter(t => t.status !== 'archived').length;
    const completed = tasks.filter(t => t.status === 'completed').length;
    const inProgress = tasks.filter(t => t.status === 'in-progress').length;
    const overdue = tasks.filter(t =>
      t.dueDate && new Date(t.dueDate) < new Date() && t.status !== 'completed' && t.status !== 'archived'
    ).length;
    const archived = tasks.filter(t => t.status === 'archived').length;
    const highPriority = tasks.filter(t =>
      (t.priority === 'urgent' || t.priority === 'high') && t.status !== 'completed' && t.status !== 'archived'
    ).length;

    return { total, completed, inProgress, overdue, archived, highPriority };
  };

  const getAllTags = () => {
    return [...new Set(tasks.flatMap(task => task.tags))];
  };

  const getAllCategories = () => {
    return [...new Set(tasks.map(task => task.category).filter(Boolean))];
  };

  const stats = getTaskStats();
  const filteredTasks = getFilteredTasks();
  const allTags = getAllTags();
  const allCategories = getAllCategories();

  const priorityColors = {
    urgent: 'bg-red-100 text-red-800 border-red-200',
    high: 'bg-orange-100 text-orange-800 border-orange-200',
    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    low: 'bg-green-100 text-green-800 border-green-200'
  };

  const statusColors = {
    todo: 'bg-gray-100 text-gray-800 border-gray-200',
    'in-progress': 'bg-blue-100 text-blue-800 border-blue-200',
    completed: 'bg-green-100 text-green-800 border-green-200',
    archived: 'bg-purple-100 text-purple-800 border-purple-200'
  };

  const priorityIcons = {
    urgent: <AlertCircle className="w-4 h-4 text-red-600" />,
    high: <Star className="w-4 h-4 text-orange-600" />,
    medium: <Circle className="w-4 h-4 text-yellow-600" />,
    low: <Target className="w-4 h-4 text-green-600" />
  };

  return (
    <div className={`min-h-screen transition-all duration-700 ${
      isDarkMode
        ? 'bg-gradient-to-br from-slate-900 via-blue-900/30 to-slate-900'
        : 'bg-gradient-to-br from-blue-50 via-white to-purple-50'
    }`}>
      <div className="relative z-10 max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-4 pb-32">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-4 sm:mb-6"
        >
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
              🧠 Smart Task Manager
            </h1>
            <p className={`text-sm sm:text-base ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              AI-powered productivity with smart sorting & automation
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-2 flex-shrink-0">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={() => setShowInbox(true)}
                variant="outline"
                className={`w-full sm:w-auto rounded-2xl backdrop-blur-xl border-2 text-sm ${
                  isDarkMode
                    ? 'bg-gray-800/50 border-gray-600 text-gray-300 hover:bg-gray-700/50'
                    : 'bg-white/50 border-gray-200 hover:bg-white/80'
                }`}
                size="sm"
              >
                <Inbox className="w-4 h-4 mr-2" />
                Quick Capture
              </Button>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={() => setShowForm(true)}
                className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-2xl shadow-lg text-sm"
                size="sm"
              >
                <Plus className="w-4 h-4 mr-2" />
                New Task
              </Button>
            </motion.div>
          </div>
        </motion.div>

        {/* Controls Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex flex-col lg:flex-row gap-4 mb-6"
        >
          {/* Search and Filters */}
          <div className="flex-1 flex flex-col sm:flex-row gap-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search tasks, tags, or descriptions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 rounded-2xl border-2"
              />
            </div>
            <div className="flex gap-2">
              <Select value={selectedProject} onValueChange={setSelectedProject}>
                <SelectTrigger className="w-32 rounded-2xl">
                  <SelectValue placeholder="Project" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Projects</SelectItem>
                  {projects.map(project => (
                    <SelectItem key={project.id} value={project.id}>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: project.color }}
                        />
                        {project.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-32 rounded-2xl">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ai">
                    <div className="flex items-center gap-2">
                      <Brain className="w-4 h-4" />
                      AI Smart
                    </div>
                  </SelectItem>
                  <SelectItem value="due">Due Date</SelectItem>
                  <SelectItem value="priority">Priority</SelectItem>
                  <SelectItem value="created">Created</SelectItem>
                  <SelectItem value="updated">Updated</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* View Toggle */}
          <div className="flex gap-2">
            <Button
              variant={view === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setView('list')}
              className="rounded-2xl"
            >
              <List className="w-4 h-4" />
            </Button>
            <Button
              variant={view === 'kanban' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setView('kanban')}
              className="rounded-2xl"
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={view === 'calendar' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setView('calendar')}
              className="rounded-2xl"
            >
              <Calendar className="w-4 h-4" />
            </Button>
          </div>
        </motion.div>

        {/* Enhanced Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-2 lg:grid-cols-6 gap-3 sm:gap-4 mb-6"
        >
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-blue-100 to-indigo-100 border-0 shadow-lg hover:shadow-xl transition-all">
            <Target className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-blue-600" />
            <div className="text-xl sm:text-2xl font-bold text-blue-700">{stats.total}</div>
            <div className="text-xs sm:text-sm text-blue-600">Active Tasks</div>
          </Card>
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-green-100 to-emerald-100 border-0 shadow-lg hover:shadow-xl transition-all">
            <CheckCircle className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-green-600" />
            <div className="text-xl sm:text-2xl font-bold text-green-700">{stats.completed}</div>
            <div className="text-xs sm:text-sm text-green-600">Completed</div>
          </Card>
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-yellow-100 to-orange-100 border-0 shadow-lg hover:shadow-xl transition-all">
            <Clock className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-orange-600" />
            <div className="text-xl sm:text-2xl font-bold text-orange-700">{stats.inProgress}</div>
            <div className="text-xs sm:text-sm text-orange-600">In Progress</div>
          </Card>
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-red-100 to-pink-100 border-0 shadow-lg hover:shadow-xl transition-all">
            <AlertCircle className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-red-600" />
            <div className="text-xl sm:text-2xl font-bold text-red-700">{stats.overdue}</div>
            <div className="text-xs sm:text-sm text-red-600">Overdue</div>
          </Card>
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-purple-100 to-violet-100 border-0 shadow-lg hover:shadow-xl transition-all">
            <Star className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-purple-600" />
            <div className="text-xl sm:text-2xl font-bold text-purple-700">{stats.highPriority}</div>
            <div className="text-xs sm:text-sm text-purple-600">High Priority</div>
          </Card>
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-gray-100 to-slate-100 border-0 shadow-lg hover:shadow-xl transition-all">
            <Archive className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-gray-600" />
            <div className="text-xl sm:text-2xl font-bold text-gray-700">{stats.archived}</div>
            <div className="text-xs sm:text-sm text-gray-600">Archived</div>
          </Card>
        </motion.div>

        {/* Quick Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex flex-wrap gap-2 mb-6"
        >
          <div className="flex items-center gap-2">
            <Switch
              checked={showCompleted}
              onCheckedChange={setShowCompleted}
              id="show-completed"
            />
            <Label htmlFor="show-completed" className="text-sm">Show Completed</Label>
          </div>
          <div className="flex items-center gap-2">
            <Switch
              checked={showArchived}
              onCheckedChange={setShowArchived}
              id="show-archived"
            />
            <Label htmlFor="show-archived" className="text-sm">Show Archived</Label>
          </div>
          {allTags.slice(0, 5).map(tag => (
            <Button
              key={tag}
              variant={selectedTags.includes(tag) ? "default" : "outline"}
              size="sm"
              onClick={() => {
                setSelectedTags(prev =>
                  prev.includes(tag)
                    ? prev.filter(t => t !== tag)
                    : [...prev, tag]
                );
              }}
              className="rounded-full text-xs"
            >
              <Tag className="w-3 h-3 mr-1" />
              {tag}
            </Button>
          ))}
        </motion.div>

        {/* Task List */}
        <div className="space-y-3 sm:space-y-4">
          <AnimatePresence>
            {filteredTasks.map(task => (
              <motion.div
                key={task.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="group"
              >
                <Card className={`border-0 shadow-lg hover:shadow-xl transition-all duration-300 ${
                  isDarkMode ? 'bg-gray-800/80' : 'bg-white/80'
                } backdrop-blur-sm rounded-2xl overflow-hidden`}
                style={{ borderLeft: `4px solid ${task.colorLabel}` }}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1 min-w-0">
                        <Button
                          onClick={() => toggleTaskStatus(task.id)}
                          variant="ghost"
                          size="sm"
                          className="mt-1 p-0 w-6 h-6 rounded-full"
                        >
                          {task.status === 'completed' ? (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          ) : task.status === 'in-progress' ? (
                            <Clock className="w-5 h-5 text-blue-600" />
                          ) : (
                            <Circle className="w-5 h-5 text-gray-400" />
                          )}
                        </Button>

                        <div className="flex-1 min-w-0">
                          {/* Task Header */}
                          <div className="flex items-center gap-2 mb-2">
                            {priorityIcons[task.priority]}
                            <h3 className={`font-medium text-sm sm:text-base ${
                              task.status === 'completed' ? 'line-through text-gray-500' : ''
                            } ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                              {task.title}
                            </h3>
                            <Badge className={`text-xs ${statusColors[task.status]}`}>
                              {task.status.replace('-', ' ')}
                            </Badge>
                            {task.aiScore > 80 && (
                              <Badge className="text-xs bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border-purple-200">
                                <Zap className="w-3 h-3 mr-1" />
                                AI High
                              </Badge>
                            )}
                          </div>

                          {/* Task Description */}
                          {task.description && (
                            <p className={`text-xs sm:text-sm mb-2 ${
                              isDarkMode ? 'text-gray-400' : 'text-gray-600'
                            }`}>
                              {task.description}
                            </p>
                          )}

                          {/* Task Meta Info */}
                          <div className="flex flex-wrap items-center gap-2 mb-2 text-xs text-gray-500">
                            {task.dueDate && (
                              <div className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                <span className={
                                  new Date(task.dueDate) < new Date() && task.status !== 'completed'
                                    ? 'text-red-600 font-medium'
                                    : ''
                                }>
                                  {new Date(task.dueDate).toLocaleDateString()}
                                </span>
                              </div>
                            )}
                            {task.location && (
                              <div className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                <span>{task.location}</span>
                              </div>
                            )}
                            {task.estimatedTime && (
                              <div className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                <span>{task.estimatedTime}m</span>
                              </div>
                            )}
                            {task.assignedTo && (
                              <div className="flex items-center gap-1">
                                <Users className="w-3 h-3" />
                                <span>{task.assignedTo}</span>
                              </div>
                            )}
                            {task.isRecurring && (
                              <div className="flex items-center gap-1">
                                <Repeat className="w-3 h-3" />
                                <span>{task.recurringPattern}</span>
                              </div>
                            )}
                          </div>

                          {/* Subtasks */}
                          {task.subtasks.length > 0 && (
                            <div className="mb-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const updatedTasks = tasks.map(t =>
                                    t.id === task.id ? { ...t, isExpanded: !t.isExpanded } : t
                                  );
                                  saveTasks(updatedTasks);
                                }}
                                className="p-0 h-auto text-xs text-gray-600 hover:text-gray-800"
                              >
                                {task.isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                                {task.subtasks.filter(st => st.completed).length}/{task.subtasks.length} subtasks
                              </Button>

                              {task.isExpanded && (
                                <div className="mt-2 space-y-1">
                                  {task.subtasks.map(subtask => (
                                    <div key={subtask.id} className="flex items-center gap-2 text-sm">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => toggleSubtask(task.id, subtask.id)}
                                        className="p-0 w-4 h-4"
                                      >
                                        {subtask.completed ? (
                                          <CheckCircle className="w-4 h-4 text-green-600" />
                                        ) : (
                                          <Circle className="w-4 h-4 text-gray-400" />
                                        )}
                                      </Button>
                                      <span className={subtask.completed ? 'line-through text-gray-500' : ''}>
                                        {subtask.title}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          )}

                          {/* Tags and Labels */}
                          <div className="flex flex-wrap items-center gap-2">
                            <Badge className={`text-xs ${priorityColors[task.priority]}`}>
                              {task.priority}
                            </Badge>
                            {task.category && (
                              <Badge variant="outline" className="text-xs">
                                <FolderOpen className="w-3 h-3 mr-1" />
                                {task.category}
                              </Badge>
                            )}
                            {task.tags.map(tag => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                #{tag}
                              </Badge>
                            ))}
                            {task.linkedTasks.length > 0 && (
                              <Badge variant="outline" className="text-xs">
                                <Link className="w-3 h-3 mr-1" />
                                {task.linkedTasks.length} linked
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          onClick={() => {
                            setSelectedTask(task);
                            setShowTaskDetails(true);
                          }}
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:bg-blue-50"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          onClick={() => snoozeTask(task.id, 24)}
                          variant="ghost"
                          size="sm"
                          className="text-yellow-600 hover:bg-yellow-50"
                        >
                          <Pause className="w-4 h-4" />
                        </Button>
                        <Button
                          onClick={() => archiveTask(task.id)}
                          variant="ghost"
                          size="sm"
                          className="text-purple-600 hover:bg-purple-50"
                        >
                          <Archive className="w-4 h-4" />
                        </Button>
                        <Button
                          onClick={() => deleteTask(task.id)}
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Empty State */}
        {filteredTasks.length === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-12"
          >
            <Target className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-500 mb-2">
              {tasks.length === 0 ? 'No tasks yet' : 'No tasks match your filters'}
            </h3>
            <p className="text-gray-400 mb-4">
              {tasks.length === 0
                ? 'Create your first smart task to get started with AI-powered productivity!'
                : 'Try adjusting your filters or search query to find tasks.'
              }
            </p>
            <div className="flex gap-2 justify-center">
              <Button
                onClick={() => setShowForm(true)}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Task
              </Button>
              <Button
                onClick={() => setShowInbox(true)}
                variant="outline"
              >
                <Inbox className="w-4 h-4 mr-2" />
                Quick Capture
              </Button>
            </div>
          </motion.div>
        )}

        {/* Task Creation Form Dialog */}
        <Dialog open={showForm} onOpenChange={setShowForm}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Plus className="w-5 h-5" />
                Create Smart Task
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label htmlFor="title">Task Title *</Label>
                <Input
                  id="title"
                  placeholder="What needs to be done?"
                  value={newTask.title}
                  onChange={(e) => setNewTask(prev => ({ ...prev, title: e.target.value }))}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Add more details..."
                  value={newTask.description}
                  onChange={(e) => setNewTask(prev => ({ ...prev, description: e.target.value }))}
                  className="mt-1"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="priority">Priority</Label>
                  <Select value={newTask.priority} onValueChange={(value: any) => setNewTask(prev => ({ ...prev, priority: value }))}>
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">🟢 Low</SelectItem>
                      <SelectItem value="medium">🟡 Medium</SelectItem>
                      <SelectItem value="high">🟠 High</SelectItem>
                      <SelectItem value="urgent">🔴 Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    placeholder="Work, Personal, etc."
                    value={newTask.category}
                    onChange={(e) => setNewTask(prev => ({ ...prev, category: e.target.value }))}
                    className="mt-1"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="dueDate">Due Date</Label>
                  <Input
                    id="dueDate"
                    type="date"
                    value={newTask.dueDate}
                    onChange={(e) => setNewTask(prev => ({ ...prev, dueDate: e.target.value }))}
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="estimatedTime">Estimated Time (minutes)</Label>
                  <Input
                    id="estimatedTime"
                    type="number"
                    placeholder="60"
                    value={newTask.estimatedTime}
                    onChange={(e) => setNewTask(prev => ({ ...prev, estimatedTime: parseInt(e.target.value) || 60 }))}
                    className="mt-1"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="location">Location (optional)</Label>
                <Input
                  id="location"
                  placeholder="Office, Home, etc."
                  value={newTask.location}
                  onChange={(e) => setNewTask(prev => ({ ...prev, location: e.target.value }))}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="tags">Tags (comma separated)</Label>
                <Input
                  id="tags"
                  placeholder="urgent, meeting, review"
                  value={newTask.tags.join(', ')}
                  onChange={(e) => setNewTask(prev => ({
                    ...prev,
                    tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                  }))}
                  className="mt-1"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="recurring"
                  checked={newTask.isRecurring}
                  onCheckedChange={(checked) => setNewTask(prev => ({ ...prev, isRecurring: checked }))}
                />
                <Label htmlFor="recurring">Recurring Task</Label>
                {newTask.isRecurring && (
                  <Select value={newTask.recurringPattern} onValueChange={(value: any) => setNewTask(prev => ({ ...prev, recurringPattern: value }))}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={() => setShowForm(false)}>
                  Cancel
                </Button>
                <Button onClick={addTask} disabled={!newTask.title.trim()}>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Task
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Quick Capture Inbox */}
        <Dialog open={showInbox} onOpenChange={setShowInbox}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Inbox className="w-5 h-5" />
                Quick Capture
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Input
                  placeholder="Quick task idea..."
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                      const quickTask: Task = {
                        id: Date.now().toString(),
                        title: e.currentTarget.value.trim(),
                        description: '',
                        priority: 'medium',
                        status: 'todo',
                        tags: [],
                        category: '',
                        colorLabel: '#3B82F6',
                        project: 'default',
                        workspace: 'default',
                        subtasks: [],
                        linkedTasks: [],
                        isRecurring: false,
                        aiScore: 50,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                      };
                      quickTask.aiScore = calculateAIScore(quickTask);
                      saveTasks([...tasks, quickTask]);
                      e.currentTarget.value = '';
                    }
                  }}
                  className="text-lg"
                />
              </div>
              <p className="text-sm text-gray-500">
                Press Enter to quickly add a task. You can edit details later.
              </p>
            </div>
          </DialogContent>
        </Dialog>

        {/* Task Details Dialog */}
        <Dialog open={showTaskDetails} onOpenChange={setShowTaskDetails}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Edit className="w-5 h-5" />
                Task Details
              </DialogTitle>
            </DialogHeader>

            {selectedTask && (
              <Tabs defaultValue="details" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="subtasks">Subtasks</TabsTrigger>
                  <TabsTrigger value="links">Links</TabsTrigger>
                  <TabsTrigger value="analytics">Analytics</TabsTrigger>
                </TabsList>

                <TabsContent value="details" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>AI Score</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all"
                            style={{ width: `${selectedTask.aiScore}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium">{Math.round(selectedTask.aiScore)}</span>
                      </div>
                    </div>
                    <div>
                      <Label>Status</Label>
                      <Badge className={`mt-1 ${statusColors[selectedTask.status]}`}>
                        {selectedTask.status.replace('-', ' ')}
                      </Badge>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Created</Label>
                      <p className="text-sm text-gray-600 mt-1">
                        {new Date(selectedTask.createdAt).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <Label>Last Updated</Label>
                      <p className="text-sm text-gray-600 mt-1">
                        {new Date(selectedTask.updatedAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="subtasks" className="space-y-4">
                  <div className="space-y-2">
                    {selectedTask.subtasks.map(subtask => (
                      <div key={subtask.id} className="flex items-center gap-2 p-2 border rounded">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleSubtask(selectedTask.id, subtask.id)}
                          className="p-0 w-5 h-5"
                        >
                          {subtask.completed ? (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          ) : (
                            <Circle className="w-5 h-5 text-gray-400" />
                          )}
                        </Button>
                        <span className={subtask.completed ? 'line-through text-gray-500' : ''}>
                          {subtask.title}
                        </span>
                      </div>
                    ))}
                  </div>

                  <div className="flex gap-2">
                    <Input
                      placeholder="Add subtask..."
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          addSubtask(selectedTask.id, e.currentTarget.value.trim());
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <Button size="sm">
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="links" className="space-y-4">
                  <div className="text-center py-8 text-gray-500">
                    <Link className="w-12 h-12 mx-auto mb-2" />
                    <p>Task linking feature coming soon!</p>
                  </div>
                </TabsContent>

                <TabsContent value="analytics" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Card className="p-4">
                      <div className="text-center">
                        <Brain className="w-8 h-8 mx-auto mb-2 text-purple-600" />
                        <div className="text-2xl font-bold">{Math.round(selectedTask.aiScore)}</div>
                        <div className="text-sm text-gray-600">AI Priority Score</div>
                      </div>
                    </Card>
                    <Card className="p-4">
                      <div className="text-center">
                        <Clock className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                        <div className="text-2xl font-bold">{selectedTask.estimatedTime || 0}m</div>
                        <div className="text-sm text-gray-600">Estimated Time</div>
                      </div>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default Tasks;
