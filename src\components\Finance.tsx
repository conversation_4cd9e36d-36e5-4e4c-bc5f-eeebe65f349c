
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, DollarSign, Calendar, TrendingUp, PieChart, Target, AlertCircle, Calculator, CreditCard, Percent, TrendingDown, FileText, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PageHeader from '@/components/ui/PageHeader';
import PageContainer from '@/components/ui/PageContainer';
import ResponsiveGrid from '@/components/ui/ResponsiveGrid';
import NotificationService from '../services/NotificationService';
import DataSyncService from '../services/DataSyncService';

interface Transaction {
  id: number;
  type: 'income' | 'expense';
  amount: number;
  category: string;
  description: string;
  date: string;
  createdAt: string;
}

interface Budget {
  id: number;
  category: string;
  limit: number;
  spent: number;
  period: 'monthly' | 'weekly';
}

const Finance = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [budgets, setBudgets] = useState<Budget[]>([]);
  const [showNewTransaction, setShowNewTransaction] = useState(false);
  const [showNewBudget, setShowNewBudget] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [showCalculator, setShowCalculator] = useState(false);
  const [calculatorType, setCalculatorType] = useState<'basic' | 'debt' | 'interest' | 'investment' | 'loan' | 'tax' | 'budget'>('basic');
  const [showBudgetPlanner, setShowBudgetPlanner] = useState(false);
  const [showInvestmentTracker, setShowInvestmentTracker] = useState(false);
  const [showExpenseAnalyzer, setShowExpenseAnalyzer] = useState(false);
  const [showGoalTracker, setShowGoalTracker] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const saved = localStorage.getItem('billionDollarDarkMode');
    return saved ? JSON.parse(saved) : false;
  });

  // Currency settings
  const [selectedCurrency, setSelectedCurrency] = useState(() => {
    return localStorage.getItem('selectedCurrency') || 'USD';
  });

  const currencies = {
    USD: { symbol: '$', name: 'US Dollar', rate: 1 },
    EUR: { symbol: '€', name: 'Euro', rate: 0.85 },
    GBP: { symbol: '£', name: 'British Pound', rate: 0.73 },
    INR: { symbol: '₹', name: 'Indian Rupee', rate: 83.12 },
    NPR: { symbol: 'रू', name: 'Nepali Rupee', rate: 133.0 },
    JPY: { symbol: '¥', name: 'Japanese Yen', rate: 110.0 },
    CAD: { symbol: 'C$', name: 'Canadian Dollar', rate: 1.25 },
    AUD: { symbol: 'A$', name: 'Australian Dollar', rate: 1.35 },
    CHF: { symbol: 'Fr', name: 'Swiss Franc', rate: 0.92 },
    CNY: { symbol: '¥', name: 'Chinese Yuan', rate: 6.45 },
    KRW: { symbol: '₩', name: 'South Korean Won', rate: 1180.0 },
    BRL: { symbol: 'R$', name: 'Brazilian Real', rate: 5.20 },
    MXN: { symbol: '$', name: 'Mexican Peso', rate: 18.50 },
    SGD: { symbol: 'S$', name: 'Singapore Dollar', rate: 1.35 },
    HKD: { symbol: 'HK$', name: 'Hong Kong Dollar', rate: 7.80 },
    SEK: { symbol: 'kr', name: 'Swedish Krona', rate: 9.75 },
    NOK: { symbol: 'kr', name: 'Norwegian Krone', rate: 8.90 },
    DKK: { symbol: 'kr', name: 'Danish Krone', rate: 6.35 },
    PLN: { symbol: 'zł', name: 'Polish Złoty', rate: 3.95 },
    CZK: { symbol: 'Kč', name: 'Czech Koruna', rate: 22.50 },
    HUF: { symbol: 'Ft', name: 'Hungarian Forint', rate: 310.0 },
    RUB: { symbol: '₽', name: 'Russian Ruble', rate: 75.0 },
    TRY: { symbol: '₺', name: 'Turkish Lira', rate: 19.50 },
    ZAR: { symbol: 'R', name: 'South African Rand', rate: 15.20 },
    AED: { symbol: 'د.إ', name: 'UAE Dirham', rate: 3.67 },
    SAR: { symbol: 'ر.س', name: 'Saudi Riyal', rate: 3.75 },
    EGP: { symbol: 'ج.م', name: 'Egyptian Pound', rate: 30.90 },
    NGN: { symbol: '₦', name: 'Nigerian Naira', rate: 460.0 },
    KES: { symbol: 'KSh', name: 'Kenyan Shilling', rate: 110.0 },
    GHS: { symbol: '₵', name: 'Ghanaian Cedi', rate: 6.20 },
    MAD: { symbol: 'د.م.', name: 'Moroccan Dirham', rate: 9.80 },
    TND: { symbol: 'د.ت', name: 'Tunisian Dinar', rate: 2.75 },
    LKR: { symbol: 'Rs', name: 'Sri Lankan Rupee', rate: 200.0 },
    PKR: { symbol: 'Rs', name: 'Pakistani Rupee', rate: 280.0 },
    BDT: { symbol: '৳', name: 'Bangladeshi Taka', rate: 85.0 },
    NPR: { symbol: 'Rs', name: 'Nepalese Rupee', rate: 133.0 },
    LBP: { symbol: 'ل.ل', name: 'Lebanese Pound', rate: 1500.0 },
    JOD: { symbol: 'د.ا', name: 'Jordanian Dinar', rate: 0.71 },
    KWD: { symbol: 'د.ك', name: 'Kuwaiti Dinar', rate: 0.30 },
    QAR: { symbol: 'ر.ق', name: 'Qatari Riyal', rate: 3.64 },
    BHD: { symbol: '.د.ب', name: 'Bahraini Dinar', rate: 0.38 },
    OMR: { symbol: 'ر.ع.', name: 'Omani Rial', rate: 0.38 },
    ILS: { symbol: '₪', name: 'Israeli Shekel', rate: 3.20 },
    PHP: { symbol: '₱', name: 'Philippine Peso', rate: 55.0 },
    THB: { symbol: '฿', name: 'Thai Baht', rate: 33.0 },
    MYR: { symbol: 'RM', name: 'Malaysian Ringgit', rate: 4.20 },
    IDR: { symbol: 'Rp', name: 'Indonesian Rupiah', rate: 14500.0 },
    VND: { symbol: '₫', name: 'Vietnamese Dong', rate: 23000.0 }
  };

  // Calculator states
  const [basicCalc, setBasicCalc] = useState({ display: '0', operation: '', previousValue: '' });
  const [debtCalc, setDebtCalc] = useState({
    balance: '',
    interestRate: '',
    monthlyPayment: '',
    result: null as any
  });
  const [interestCalc, setInterestCalc] = useState({
    principal: '',
    rate: '',
    time: '',
    compound: 'annually',
    result: null as any
  });

  // Investment Calculator State
  const [investmentCalc, setInvestmentCalc] = useState({
    initialInvestment: '',
    monthlyContribution: '',
    annualReturn: '',
    years: '',
    result: null as any
  });

  // Loan Calculator State
  const [loanCalc, setLoanCalc] = useState({
    principal: '',
    interestRate: '',
    loanTerm: '',
    result: null as any
  });

  // Tax Calculator State
  const [taxCalc, setTaxCalc] = useState({
    income: '',
    filingStatus: 'single',
    deductions: '',
    customTaxRate: '',
    stateTaxRate: '',
    useCustomRate: false,
    result: null as any
  });

  // Budget Planner State
  const [budgetPlanner, setBudgetPlanner] = useState({
    monthlyIncome: '',
    expenses: {
      housing: '',
      transportation: '',
      food: '',
      utilities: '',
      insurance: '',
      healthcare: '',
      entertainment: '',
      other: ''
    },
    result: null as any
  });
  const [newTransaction, setNewTransaction] = useState({
    type: 'expense' as 'income' | 'expense',
    amount: '',
    category: '',
    description: '',
    date: new Date().toISOString().split('T')[0]
  });
  const [newBudget, setNewBudget] = useState({
    category: '',
    limit: '',
    period: 'monthly' as 'monthly' | 'weekly'
  });

  const expenseCategories = [
    'Food & Dining', 'Transportation', 'Shopping', 'Entertainment', 
    'Bills & Utilities', 'Healthcare', 'Education', 'Groceries', 
    'Rent/Mortgage', 'Insurance', 'Subscriptions', 'Other'
  ];

  const incomeCategories = [
    'Salary', 'Freelance', 'Business', 'Investment', 'Gift', 'Bonus', 'Other'
  ];

  // Currency formatter function
  const formatCurrency = (amount: number): string => {
    const currency = currencies[selectedCurrency as keyof typeof currencies];
    const convertedAmount = amount * currency.rate;

    if (selectedCurrency === 'JPY' || selectedCurrency === 'KRW' || selectedCurrency === 'VND' || selectedCurrency === 'IDR' || selectedCurrency === 'NPR') {
      return `${currency.symbol}${Math.round(convertedAmount).toLocaleString()}`;
    }
    return `${currency.symbol}${convertedAmount.toFixed(2)}`;
  };

  // Save currency preference
  const handleCurrencyChange = (newCurrency: string) => {
    setSelectedCurrency(newCurrency);
    localStorage.setItem('selectedCurrency', newCurrency);
  };

  useEffect(() => {
    // Subscribe to real-time data updates
    const unsubscribeTransactions = DataSyncService.subscribe('transactions', (data) => {
      setTransactions(data);
    });

    const unsubscribeBudgets = DataSyncService.subscribe('budgets', (data) => {
      setBudgets(data);
    });

    return () => {
      unsubscribeTransactions();
      unsubscribeBudgets();
    };
  }, []);

  // Real-time calculation effects
  useEffect(() => {
    if (investmentCalc.initialInvestment && investmentCalc.monthlyContribution &&
        investmentCalc.annualReturn && investmentCalc.years) {
      calculateInvestmentROI();
    }
  }, [investmentCalc.initialInvestment, investmentCalc.monthlyContribution,
      investmentCalc.annualReturn, investmentCalc.years]);

  useEffect(() => {
    if (loanCalc.principal && loanCalc.interestRate && loanCalc.loanTerm) {
      calculateLoanPayment();
    }
  }, [loanCalc.principal, loanCalc.interestRate, loanCalc.loanTerm]);

  useEffect(() => {
    if (taxCalc.income) {
      calculateTax();
    }
  }, [taxCalc.income, taxCalc.filingStatus, taxCalc.deductions,
      taxCalc.customTaxRate, taxCalc.stateTaxRate, taxCalc.useCustomRate]);

  useEffect(() => {
    if (budgetPlanner.monthlyIncome) {
      calculateBudgetPlan();
    }
  }, [budgetPlanner.monthlyIncome, budgetPlanner.expenses]);

  useEffect(() => {
    if (debtCalc.balance && debtCalc.interestRate && debtCalc.monthlyPayment) {
      calculateDebtPayoff();
    }
  }, [debtCalc.balance, debtCalc.interestRate, debtCalc.monthlyPayment]);

  useEffect(() => {
    if (interestCalc.principal && interestCalc.rate && interestCalc.time) {
      calculateCompoundInterest();
    }
  }, [interestCalc.principal, interestCalc.rate, interestCalc.time, interestCalc.compound]);

  const addTransaction = () => {
    if (!newTransaction.amount || !newTransaction.category) {
      alert('⚠️ Please fill in amount and category!');
      return;
    }

    const amount = parseFloat(newTransaction.amount);
    if (amount <= 0) {
      alert('⚠️ Amount must be greater than 0!');
      return;
    }

    const transaction: Transaction = {
      id: Date.now(),
      ...newTransaction,
      amount: amount,
      createdAt: new Date().toISOString(),
    };

    const updated = [...transactions, transaction];
    DataSyncService.saveData('transactions', updated);

    // Update budget spending
    if (transaction.type === 'expense') {
      updateBudgetSpending(transaction.category, transaction.amount);
    }

    // Show success notification
    const icon = transaction.type === 'income' ? '💰' : '💸';
    NotificationService.sendNotification({
      title: `${icon} Transaction Added!`,
      body: `${transaction.type === 'income' ? 'Income' : 'Expense'}: ${formatCurrency(amount)} - ${transaction.category}`,
      tag: 'transaction-added'
    });

    setNewTransaction({
      type: 'expense',
      amount: '',
      category: '',
      description: '',
      date: new Date().toISOString().split('T')[0]
    });
    setShowNewTransaction(false);
  };

  const addBudget = () => {
    if (!newBudget.category || !newBudget.limit) {
      alert('⚠️ Please fill in category and budget limit!');
      return;
    }

    const limit = parseFloat(newBudget.limit);
    if (limit <= 0) {
      alert('⚠️ Budget limit must be greater than 0!');
      return;
    }

    // Check if budget already exists for this category
    const existingBudget = budgets.find(b => b.category === newBudget.category);
    if (existingBudget) {
      alert('⚠️ Budget already exists for this category! Please edit the existing one.');
      return;
    }

    const budget: Budget = {
      id: Date.now(),
      category: newBudget.category,
      limit: limit,
      spent: 0,
      period: newBudget.period
    };

    const updated = [...budgets, budget];
    DataSyncService.saveData('budgets', updated);

    // Show success notification
    NotificationService.sendNotification({
      title: '🎯 Budget Created!',
      body: `${newBudget.period} budget of ${formatCurrency(limit)} set for ${newBudget.category}`,
      tag: 'budget-created'
    });

    setNewBudget({ category: '', limit: '', period: 'monthly' });
    setShowNewBudget(false);
  };

  const updateBudgetSpending = (category: string, amount: number) => {
    const updatedBudgets = budgets.map(budget => {
      if (budget.category === category) {
        const newSpent = budget.spent + amount;
        const percentage = (newSpent / budget.limit) * 100;

        // Send budget alerts
        if (percentage >= 90 && budget.spent / budget.limit < 0.9) {
          sendBudgetAlert(category, 'warning', `90% of budget used (${percentage.toFixed(1)}%)`);
        } else if (percentage >= 100 && budget.spent / budget.limit < 1.0) {
          sendBudgetAlert(category, 'danger', `Budget exceeded! (${percentage.toFixed(1)}%)`);
        } else if (percentage >= 75 && budget.spent / budget.limit < 0.75) {
          sendBudgetAlert(category, 'info', `75% of budget used (${percentage.toFixed(1)}%)`);
        }

        return { ...budget, spent: newSpent };
      }
      return budget;
    });
    DataSyncService.saveData('budgets', updatedBudgets);
  };

  const sendBudgetAlert = (category: string, type: 'info' | 'warning' | 'danger', message: string) => {
    const icons = { info: '💡', warning: '⚠️', danger: '🚨' };

    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(`${icons[type]} Budget Alert: ${category}`, {
        body: message,
        icon: '/favicon.ico',
        tag: `budget-${category}`
      });
    }

    // Also show in-app toast
    console.log(`Budget Alert: ${category} - ${message}`);
  };

  const deleteTransaction = (id: number) => {
    if (confirm('Are you sure you want to delete this transaction?')) {
      const updated = transactions.filter(t => t.id !== id);
      DataSyncService.saveData('transactions', updated);

      NotificationService.sendNotification({
        title: '🗑️ Transaction Deleted',
        body: 'Transaction has been removed from your records',
        tag: 'transaction-deleted'
      });
    }
  };

  const deleteBudget = (id: number) => {
    if (confirm('Are you sure you want to delete this budget?')) {
      const updated = budgets.filter(b => b.id !== id);
      DataSyncService.saveData('budgets', updated);

      NotificationService.sendNotification({
        title: '🗑️ Budget Deleted',
        body: 'Budget has been removed',
        tag: 'budget-deleted'
      });
    }
  };

  const getStats = () => {
    const thisMonth = new Date().getMonth();
    const thisYear = new Date().getFullYear();
    
    const thisMonthTransactions = transactions.filter(t => {
      const tDate = new Date(t.date);
      return tDate.getMonth() === thisMonth && tDate.getFullYear() === thisYear;
    });

    const income = thisMonthTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);

    const expenses = thisMonthTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    return { income, expenses, balance: income - expenses };
  };

  const getCategoryAnalysis = () => {
    const thisMonth = new Date().getMonth();
    const thisYear = new Date().getFullYear();
    
    const categoryTotals: Record<string, number> = {};
    
    transactions
      .filter(t => {
        const tDate = new Date(t.date);
        return t.type === 'expense' && 
               tDate.getMonth() === thisMonth && 
               tDate.getFullYear() === thisYear;
      })
      .forEach(t => {
        categoryTotals[t.category] = (categoryTotals[t.category] || 0) + t.amount;
      });

    return Object.entries(categoryTotals)
      .sort(([,a], [,b]) => (b as number) - (a as number));
  };

  const getBudgetProgress = () => {
    return budgets.map(budget => ({
      ...budget,
      percentage: (budget.spent / budget.limit) * 100,
      remaining: budget.limit - budget.spent,
      isOverBudget: budget.spent > budget.limit
    }));
  };

  // Calculator functions
  const handleBasicCalc = (value: string) => {
    if (value === 'C') {
      setBasicCalc({ display: '0', operation: '', previousValue: '' });
    } else if (value === '=') {
      if (basicCalc.operation && basicCalc.previousValue) {
        const prev = parseFloat(basicCalc.previousValue);
        const current = parseFloat(basicCalc.display);
        let result = 0;

        switch (basicCalc.operation) {
          case '+': result = prev + current; break;
          case '-': result = prev - current; break;
          case '*': result = prev * current; break;
          case '/': result = prev / current; break;
        }

        setBasicCalc({ display: result.toString(), operation: '', previousValue: '' });
      }
    } else if (['+', '-', '*', '/'].includes(value)) {
      setBasicCalc({
        ...basicCalc,
        operation: value,
        previousValue: basicCalc.display,
        display: '0'
      });
    } else {
      setBasicCalc({
        ...basicCalc,
        display: basicCalc.display === '0' ? value : basicCalc.display + value
      });
    }
  };

  const calculateDebtPayoff = () => {
    const balance = parseFloat(debtCalc.balance);
    const rate = parseFloat(debtCalc.interestRate) / 100 / 12;
    const payment = parseFloat(debtCalc.monthlyPayment);

    if (balance && rate && payment && payment > (balance * rate)) {
      const months = Math.log(1 + (balance * rate) / payment) / Math.log(1 + rate);
      const totalPaid = payment * months;
      const totalInterest = totalPaid - balance;

      setDebtCalc({
        ...debtCalc,
        result: {
          months: Math.ceil(months),
          years: Math.ceil(months / 12),
          totalPaid: totalPaid.toFixed(2),
          totalInterest: totalInterest.toFixed(2)
        }
      });

      // Show success notification
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('💰 Debt Calculation Complete!', {
          body: `You'll be debt-free in ${Math.ceil(months)} months!`,
          icon: '/favicon.ico'
        });
      }
    } else if (payment <= (balance * rate)) {
      alert('⚠️ Monthly payment must be greater than monthly interest to pay off debt!');
    }
  };

  const calculateCompoundInterest = () => {
    const principal = parseFloat(interestCalc.principal);
    const rate = parseFloat(interestCalc.rate) / 100;
    const time = parseFloat(interestCalc.time);

    const compoundFreq = {
      annually: 1,
      semiannually: 2,
      quarterly: 4,
      monthly: 12,
      daily: 365
    };

    const n = compoundFreq[interestCalc.compound as keyof typeof compoundFreq];

    if (principal && rate && time && principal > 0 && rate >= 0 && time > 0) {
      const amount = principal * Math.pow(1 + rate / n, n * time);
      const interest = amount - principal;

      setInterestCalc({
        ...interestCalc,
        result: {
          finalAmount: amount.toFixed(2),
          totalInterest: interest.toFixed(2),
          effectiveRate: ((amount / principal - 1) * 100).toFixed(2)
        }
      });

      // Show success notification
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('📈 Investment Calculation Complete!', {
          body: `Your ${formatCurrency(principal)} will grow to ${formatCurrency(amount)} in ${time} years!`,
          icon: '/favicon.ico'
        });
      }
    } else {
      alert('⚠️ Please enter valid positive numbers for all fields!');
    }
  };

  // Investment ROI Calculator (Real-time)
  const calculateInvestmentROI = () => {
    const initial = parseFloat(investmentCalc.initialInvestment) || 0;
    const monthly = parseFloat(investmentCalc.monthlyContribution) || 0;
    const annualReturn = parseFloat(investmentCalc.annualReturn) || 0;
    const years = parseFloat(investmentCalc.years) || 0;

    if (initial >= 0 && monthly >= 0 && annualReturn >= 0 && years > 0) {
      const monthlyReturn = (annualReturn / 100) / 12;
      const totalMonths = years * 12;

      // Future value of initial investment
      const futureValueInitial = initial * Math.pow(1 + (annualReturn / 100), years);

      // Future value of monthly contributions (annuity)
      let futureValueMonthly = 0;
      if (monthlyReturn > 0) {
        futureValueMonthly = monthly * (Math.pow(1 + monthlyReturn, totalMonths) - 1) / monthlyReturn;
      } else {
        futureValueMonthly = monthly * totalMonths;
      }

      const totalInvested = initial + (monthly * totalMonths);
      const finalAmount = futureValueInitial + futureValueMonthly;
      const totalReturn = finalAmount - totalInvested;
      const roi = totalInvested > 0 ? ((finalAmount - totalInvested) / totalInvested) * 100 : 0;

      setInvestmentCalc({
        ...investmentCalc,
        result: {
          finalAmount: formatCurrency(finalAmount),
          totalInvested: formatCurrency(totalInvested),
          totalReturn: formatCurrency(totalReturn),
          roi: roi.toFixed(2),
          monthlyGrowth: formatCurrency(finalAmount / totalMonths),
          yearlyGrowth: formatCurrency(finalAmount / years)
        }
      });
    } else if (investmentCalc.initialInvestment || investmentCalc.monthlyContribution ||
               investmentCalc.annualReturn || investmentCalc.years) {
      // Clear results if inputs are invalid but not empty
      setInvestmentCalc({ ...investmentCalc, result: null });
    }
  };

  // Loan Payment Calculator (Real-time)
  const calculateLoanPayment = () => {
    const principal = parseFloat(loanCalc.principal) || 0;
    const annualRate = parseFloat(loanCalc.interestRate) || 0;
    const years = parseFloat(loanCalc.loanTerm) || 0;

    if (principal > 0 && annualRate >= 0 && years > 0) {
      const monthlyRate = (annualRate / 100) / 12;
      const totalPayments = years * 12;

      let monthlyPayment;
      if (monthlyRate === 0) {
        monthlyPayment = principal / totalPayments;
      } else {
        monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, totalPayments)) /
                        (Math.pow(1 + monthlyRate, totalPayments) - 1);
      }

      const totalPaid = monthlyPayment * totalPayments;
      const totalInterest = totalPaid - principal;

      setLoanCalc({
        ...loanCalc,
        result: {
          monthlyPayment: formatCurrency(monthlyPayment),
          totalPaid: formatCurrency(totalPaid),
          totalInterest: formatCurrency(totalInterest),
          interestPercentage: ((totalInterest / principal) * 100).toFixed(2),
          principalPercentage: ((principal / totalPaid) * 100).toFixed(2),
          payoffDate: new Date(Date.now() + (years * 365.25 * 24 * 60 * 60 * 1000)).toLocaleDateString()
        }
      });
    } else if (loanCalc.principal || loanCalc.interestRate || loanCalc.loanTerm) {
      setLoanCalc({ ...loanCalc, result: null });
    }
  };

  // Enhanced Tax Calculator with custom rates
  const calculateTax = () => {
    const income = parseFloat(taxCalc.income);
    const deductions = parseFloat(taxCalc.deductions) || 0;
    const customRate = parseFloat(taxCalc.customTaxRate) || 0;
    const stateRate = parseFloat(taxCalc.stateTaxRate) || 0;

    if (income > 0) {
      const taxableIncome = Math.max(0, income - deductions);
      let federalTax = 0;
      let marginalRate = 0;

      if (taxCalc.useCustomRate && customRate > 0) {
        // Use custom flat tax rate
        federalTax = taxableIncome * (customRate / 100);
        marginalRate = customRate;
      } else {
        // Use progressive tax brackets (2024 US Federal)
        const brackets = [
          { min: 0, max: 11000, rate: 0.10 },
          { min: 11000, max: 44725, rate: 0.12 },
          { min: 44725, max: 95375, rate: 0.22 },
          { min: 95375, max: 182050, rate: 0.24 },
          { min: 182050, max: 231250, rate: 0.32 },
          { min: 231250, max: 578125, rate: 0.35 },
          { min: 578125, max: Infinity, rate: 0.37 }
        ];

        let remainingIncome = taxableIncome;

        for (const bracket of brackets) {
          if (remainingIncome <= 0) break;

          const taxableAtThisBracket = Math.min(remainingIncome, bracket.max - bracket.min);
          federalTax += taxableAtThisBracket * bracket.rate;
          remainingIncome -= taxableAtThisBracket;

          if (taxableIncome > bracket.min && taxableIncome <= bracket.max) {
            marginalRate = bracket.rate * 100;
          }
        }
      }

      // Calculate state tax
      const stateTax = taxableIncome * (stateRate / 100);
      const totalTax = federalTax + stateTax;
      const effectiveRate = (totalTax / income) * 100;
      const afterTaxIncome = income - totalTax;

      setTaxCalc({
        ...taxCalc,
        result: {
          taxableIncome: formatCurrency(taxableIncome),
          federalTax: formatCurrency(federalTax),
          stateTax: formatCurrency(stateTax),
          totalTax: formatCurrency(totalTax),
          effectiveRate: effectiveRate.toFixed(2),
          marginalRate: marginalRate.toFixed(2),
          afterTaxIncome: formatCurrency(afterTaxIncome),
          takeHomeMonthly: formatCurrency(afterTaxIncome / 12)
        }
      });
    }
  };

  // Budget Planner Calculator (Real-time)
  const calculateBudgetPlan = () => {
    const income = parseFloat(budgetPlanner.monthlyIncome) || 0;
    const expenses = budgetPlanner.expenses;

    if (income > 0) {
      const totalExpenses = Object.values(expenses).reduce((sum, expense) => {
        return sum + (parseFloat(expense as string) || 0);
      }, 0);

      const remaining = income - totalExpenses;
      const savingsRate = (remaining / income) * 100;

      // Budget recommendations (50/30/20 rule)
      const needs = income * 0.5; // 50% for needs
      const wants = income * 0.3; // 30% for wants
      const savings = income * 0.2; // 20% for savings

      // Calculate actual spending by category
      const actualNeeds = (parseFloat(expenses.housing) || 0) +
                          (parseFloat(expenses.utilities) || 0) +
                          (parseFloat(expenses.food) || 0) +
                          (parseFloat(expenses.transportation) || 0) +
                          (parseFloat(expenses.insurance) || 0) +
                          (parseFloat(expenses.healthcare) || 0);

      const actualWants = (parseFloat(expenses.entertainment) || 0) +
                          (parseFloat(expenses.other) || 0);

      setBudgetPlanner({
        ...budgetPlanner,
        result: {
          totalExpenses: formatCurrency(totalExpenses),
          remaining: formatCurrency(Math.abs(remaining)),
          savingsRate: savingsRate.toFixed(2),
          recommendations: {
            needs: formatCurrency(needs),
            wants: formatCurrency(wants),
            savings: formatCurrency(savings)
          },
          actual: {
            needs: formatCurrency(actualNeeds),
            wants: formatCurrency(actualWants),
            needsPercentage: ((actualNeeds / income) * 100).toFixed(1),
            wantsPercentage: ((actualWants / income) * 100).toFixed(1)
          },
          status: remaining >= 0 ? 'surplus' : 'deficit',
          annualSavings: formatCurrency(remaining * 12),
          emergencyFund: formatCurrency(totalExpenses * 6) // 6 months of expenses
        }
      });
    } else if (budgetPlanner.monthlyIncome) {
      setBudgetPlanner({ ...budgetPlanner, result: null });
    }
  };

  const stats = getStats();
  const categoryAnalysis = getCategoryAnalysis();
  const budgetProgress = getBudgetProgress();

  return (
    <PageContainer background="default" maxWidth="xl" padding="md">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="pb-32 overflow-x-hidden"
      >
        {/* Enhanced Header */}
        <PageHeader
          title="💰 Finance Pro"
          subtitle="Professional Financial Management"
          icon={DollarSign}
          badge="Enhanced"
          badgeVariant="secondary"
          actions={
            <div className="flex items-center space-x-3">
              {/* Currency Selector */}
              <select
                value={selectedCurrency}
                onChange={(e) => handleCurrencyChange(e.target.value)}
                className={`px-3 py-2 rounded-xl border-2 text-sm font-medium ${
                  isDarkMode
                    ? 'bg-gray-800 border-gray-600 text-white'
                    : 'bg-white border-gray-200 text-gray-800'
                } focus:border-purple-500 focus:outline-none`}
              >
                {Object.entries(currencies).map(([code, currency]) => (
                  <option key={code} value={code}>
                    {currency.symbol} {code} - {currency.name}
                  </option>
                ))}
              </select>

              <Button
                onClick={() => setShowNewTransaction(true)}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-full p-3"
              >
                <Plus className="w-5 h-5" />
              </Button>
            </div>
          }
        />

        {/* Quick Stats */}
        <ResponsiveGrid
          columns={{ default: 1, sm: 3 }}
          gap="md"
          className="mb-6"
        >
        <Card className={`text-center p-4 shadow-xl border-0 backdrop-blur-xl rounded-2xl ${
          isDarkMode ? 'bg-green-900/40 border-green-500/20' : 'bg-white/80'
        }`}>
          <div className="text-2xl font-bold text-green-600">{formatCurrency(stats.income)}</div>
          <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Income</div>
        </Card>
        <Card className={`text-center p-4 shadow-xl border-0 backdrop-blur-xl rounded-2xl ${
          isDarkMode ? 'bg-red-900/40 border-red-500/20' : 'bg-white/80'
        }`}>
          <div className="text-2xl font-bold text-red-600">{formatCurrency(stats.expenses)}</div>
          <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Expenses</div>
        </Card>
        <Card className={`text-center p-4 shadow-xl border-0 backdrop-blur-xl rounded-2xl ${
          isDarkMode
            ? stats.balance >= 0 ? 'bg-green-900/40 border-green-500/20' : 'bg-red-900/40 border-red-500/20'
            : 'bg-white/80'
        }`}>
          <div className={`text-2xl font-bold ${stats.balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {formatCurrency(stats.balance)}
          </div>
          <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Balance</div>
        </Card>
        </ResponsiveGrid>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className={`grid w-full grid-cols-3 sm:grid-cols-5 backdrop-blur-sm ${
          isDarkMode ? 'bg-gray-800/80' : 'bg-white/80'
        }`}>
          <TabsTrigger value="overview" className="text-xs sm:text-sm">Overview</TabsTrigger>
          <TabsTrigger value="budget" className="text-xs sm:text-sm">Budget</TabsTrigger>
          <TabsTrigger value="analytics" className="text-xs sm:text-sm">Analytics</TabsTrigger>
          <TabsTrigger value="calculators" className="text-xs sm:text-sm">Tools</TabsTrigger>
          <TabsTrigger value="transactions" className="text-xs sm:text-sm">History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Budget Alerts */}
          {budgetProgress.filter(b => b.isOverBudget).length > 0 && (
            <Card className={`shadow-xl border-0 backdrop-blur-xl rounded-2xl ${
              isDarkMode ? 'bg-red-900/40 border-red-500/20' : 'bg-red-50/80 border-red-200'
            }`}>
              <CardHeader>
                <CardTitle className="text-red-700 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-5 h-5 mr-2" />
                  Budget Alerts
                </CardTitle>
              </CardHeader>
              <CardContent>
                {budgetProgress
                  .filter(b => b.isOverBudget)
                  .map(budget => (
                    <div key={budget.id} className="text-red-600 dark:text-red-400 text-sm mb-1">
                      {budget.category}: Over budget by {formatCurrency(budget.spent - budget.limit)}
                    </div>
                  ))}
              </CardContent>
            </Card>
          )}

          {/* Top Spending Categories */}
          <Card className={`shadow-xl border-0 backdrop-blur-xl rounded-2xl ${
            isDarkMode ? 'bg-gray-800/80' : 'bg-white/80'
          }`}>
            <CardHeader>
              <CardTitle className={`flex items-center ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                <PieChart className="w-5 h-5 mr-2" />
                Top Spending This Month
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {categoryAnalysis.slice(0, 5).map(([category, amount]) => (
                  <div key={category} className="flex justify-between items-center">
                    <span className={`${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>{category}</span>
                    <span className="font-semibold text-red-600">{formatCurrency(amount as number)}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="budget" className="space-y-6">
          {/* Add Budget Button */}
          <Button 
            onClick={() => setShowNewBudget(true)}
            className="w-full bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600"
          >
            <Target className="w-4 h-4 mr-2" />
            Create Budget
          </Button>

          {/* Budget Progress */}
          <div className="space-y-4">
            {budgetProgress.map(budget => (
              <Card key={budget.id} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium">{budget.category}</h3>
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm ${budget.isOverBudget ? 'text-red-600' : 'text-gray-600'}`}>
                        {formatCurrency(budget.spent)} / {formatCurrency(budget.limit)}
                      </span>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => deleteBudget(budget.id)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 h-8 w-8"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div 
                      className={`h-2 rounded-full ${
                        budget.isOverBudget ? 'bg-red-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(budget.percentage, 100)}%` }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{budget.percentage.toFixed(1)}% used</span>
                    <span>{formatCurrency(budget.remaining)} remaining</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Spending Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {categoryAnalysis.map(([category, amount], index) => (
                  <div key={category} className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">{category}</span>
                      <span className="text-sm">{formatCurrency(amount as number)}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full"
                        style={{
                          width: `${((amount as number) / Math.max(...categoryAnalysis.map(([, amt]) => amt as number))) * 100}%`
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calculators" className="space-y-6">
          {/* Calculator Selection - Enhanced with 4 New Tools */}
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Vertical Navigation for Mobile/Tablet, Horizontal for Desktop */}
            <div className="lg:w-1/4">
              <div className="grid grid-cols-2 sm:grid-cols-1 gap-3 sm:gap-4">
            <Card
              className={`cursor-pointer transition-all ${calculatorType === 'basic' ? 'ring-2 ring-purple-500' : ''} shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl w-full`}
              onClick={() => setCalculatorType('basic')}
            >
              <CardContent className="p-3 sm:p-4 text-center">
                <Calculator className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-purple-600" />
                <h3 className="font-medium text-xs sm:text-sm truncate">Basic Calculator</h3>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all ${calculatorType === 'debt' ? 'ring-2 ring-purple-500' : ''} shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl w-full`}
              onClick={() => setCalculatorType('debt')}
            >
              <CardContent className="p-3 sm:p-4 text-center">
                <CreditCard className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-red-600" />
                <h3 className="font-medium text-xs sm:text-sm truncate">Debt Calculator</h3>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all ${calculatorType === 'interest' ? 'ring-2 ring-purple-500' : ''} shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl w-full`}
              onClick={() => setCalculatorType('interest')}
            >
              <CardContent className="p-3 sm:p-4 text-center">
                <Percent className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-green-600" />
                <h3 className="font-medium text-xs sm:text-sm truncate">Interest Calculator</h3>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all ${calculatorType === 'investment' ? 'ring-2 ring-purple-500' : ''} shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl w-full`}
              onClick={() => setCalculatorType('investment')}
            >
              <CardContent className="p-3 sm:p-4 text-center">
                <TrendingUp className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-blue-600" />
                <h3 className="font-medium text-xs sm:text-sm truncate">Investment ROI</h3>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all ${calculatorType === 'loan' ? 'ring-2 ring-purple-500' : ''} shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl w-full`}
              onClick={() => setCalculatorType('loan')}
            >
              <CardContent className="p-3 sm:p-4 text-center">
                <DollarSign className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-orange-600" />
                <h3 className="font-medium text-xs sm:text-sm truncate">Loan Calculator</h3>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all ${calculatorType === 'tax' ? 'ring-2 ring-purple-500' : ''} shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl w-full`}
              onClick={() => setCalculatorType('tax')}
            >
              <CardContent className="p-3 sm:p-4 text-center">
                <FileText className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-indigo-600" />
                <h3 className="font-medium text-xs sm:text-sm truncate">Tax Calculator</h3>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all ${calculatorType === 'budget' ? 'ring-2 ring-purple-500' : ''} shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl w-full`}
              onClick={() => setCalculatorType('budget')}
            >
              <CardContent className="p-3 sm:p-4 text-center">
                <Target className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-teal-600" />
                <h3 className="font-medium text-xs sm:text-sm truncate">Budget Planner</h3>
              </CardContent>
            </Card>
              </div>
            </div>

            {/* Calculator Content Area */}
            <div className="lg:w-3/4">
              {/* Basic Calculator */}
          {calculatorType === 'basic' && (
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calculator className="w-5 h-5 mr-2" />
                  Basic Calculator
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="max-w-xs mx-auto">
                  <div className="bg-gray-900 text-white p-4 rounded-lg mb-4 text-right text-2xl font-mono">
                    {basicCalc.display}
                  </div>
                  <div className="grid grid-cols-4 gap-2">
                    {['C', '/', '*', '-', '7', '8', '9', '+', '4', '5', '6', '', '1', '2', '3', '=', '0', '.'].map((btn, i) => (
                      btn && (
                        <Button
                          key={i}
                          onClick={() => handleBasicCalc(btn)}
                          className={`h-12 ${btn === '=' ? 'row-span-2' : ''} ${
                            ['C', '/', '*', '-', '+', '='].includes(btn)
                              ? 'bg-orange-500 hover:bg-orange-600'
                              : 'bg-gray-200 hover:bg-gray-300 text-black'
                          }`}
                        >
                          {btn}
                        </Button>
                      )
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Debt Calculator */}
          {calculatorType === 'debt' && (
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="w-5 h-5 mr-2" />
                  Debt Payoff Calculator
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Current Balance ($)</label>
                    <Input
                      type="number"
                      placeholder="10000"
                      value={debtCalc.balance}
                      onChange={(e) => setDebtCalc({ ...debtCalc, balance: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Interest Rate (%)</label>
                    <Input
                      type="number"
                      placeholder="18.5"
                      value={debtCalc.interestRate}
                      onChange={(e) => setDebtCalc({ ...debtCalc, interestRate: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Monthly Payment ($)</label>
                    <Input
                      type="number"
                      placeholder="300"
                      value={debtCalc.monthlyPayment}
                      onChange={(e) => setDebtCalc({ ...debtCalc, monthlyPayment: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                </div>

                <Button
                  onClick={calculateDebtPayoff}
                  className="w-full bg-red-600 hover:bg-red-700"
                >
                  Calculate Payoff Time
                </Button>

                {debtCalc.result && (
                  <div className="bg-red-50 p-4 rounded-xl border border-red-200">
                    <h4 className="font-medium text-red-800 mb-2">Payoff Results:</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Time to pay off:</span>
                        <div className="font-bold">{debtCalc.result.months} months ({debtCalc.result.years} years)</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Total amount paid:</span>
                        <div className="font-bold">${debtCalc.result.totalPaid}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Total interest paid:</span>
                        <div className="font-bold text-red-600">${debtCalc.result.totalInterest}</div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Interest Calculator */}
          {calculatorType === 'interest' && (
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Percent className="w-5 h-5 mr-2" />
                  Compound Interest Calculator
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Principal Amount ($)</label>
                    <Input
                      type="number"
                      placeholder="5000"
                      value={interestCalc.principal}
                      onChange={(e) => setInterestCalc({ ...interestCalc, principal: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Annual Interest Rate (%)</label>
                    <Input
                      type="number"
                      placeholder="7.5"
                      value={interestCalc.rate}
                      onChange={(e) => setInterestCalc({ ...interestCalc, rate: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Time Period (years)</label>
                    <Input
                      type="number"
                      placeholder="10"
                      value={interestCalc.time}
                      onChange={(e) => setInterestCalc({ ...interestCalc, time: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Compound Frequency</label>
                    <select
                      value={interestCalc.compound}
                      onChange={(e) => setInterestCalc({ ...interestCalc, compound: e.target.value })}
                      className="w-full px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:outline-none"
                    >
                      <option value="annually">Annually</option>
                      <option value="semiannually">Semi-annually</option>
                      <option value="quarterly">Quarterly</option>
                      <option value="monthly">Monthly</option>
                      <option value="daily">Daily</option>
                    </select>
                  </div>
                </div>

                <Button
                  onClick={calculateCompoundInterest}
                  className="w-full bg-green-600 hover:bg-green-700"
                >
                  Calculate Interest
                </Button>

                {interestCalc.result && (
                  <div className="bg-green-50 p-4 rounded-xl border border-green-200">
                    <h4 className="font-medium text-green-800 mb-2">Interest Results:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Final Amount:</span>
                        <div className="font-bold text-green-600">${interestCalc.result.finalAmount}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Total Interest Earned:</span>
                        <div className="font-bold">${interestCalc.result.totalInterest}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Effective Annual Rate:</span>
                        <div className="font-bold">{interestCalc.result.effectiveRate}%</div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Investment ROI Calculator */}
          {calculatorType === 'investment' && (
            <Card className={`shadow-xl border-0 backdrop-blur-xl rounded-2xl ${
              isDarkMode ? 'bg-blue-900/40' : 'bg-blue-50/80'
            }`}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2 text-blue-600" />
                  Investment ROI Calculator
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Initial Investment ($)</label>
                    <Input
                      type="number"
                      placeholder="10000"
                      value={investmentCalc.initialInvestment}
                      onChange={(e) => setInvestmentCalc({ ...investmentCalc, initialInvestment: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Monthly Contribution ($)</label>
                    <Input
                      type="number"
                      placeholder="500"
                      value={investmentCalc.monthlyContribution}
                      onChange={(e) => setInvestmentCalc({ ...investmentCalc, monthlyContribution: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Annual Return (%)</label>
                    <Input
                      type="number"
                      placeholder="7"
                      value={investmentCalc.annualReturn}
                      onChange={(e) => setInvestmentCalc({ ...investmentCalc, annualReturn: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Time Period (years)</label>
                    <Input
                      type="number"
                      placeholder="10"
                      value={investmentCalc.years}
                      onChange={(e) => setInvestmentCalc({ ...investmentCalc, years: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                </div>
                <Button
                  onClick={calculateInvestmentROI}
                  className="w-full bg-blue-600 hover:bg-blue-700 rounded-xl"
                >
                  Calculate Investment
                </Button>

                {investmentCalc.result && (
                  <div className="bg-blue-50 p-4 rounded-xl border border-blue-200">
                    <h4 className="font-medium text-blue-800 mb-2">Investment Results:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Final Amount:</span>
                        <div className="font-bold text-blue-600">${investmentCalc.result.finalAmount}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Total Invested:</span>
                        <div className="font-bold">${investmentCalc.result.totalInvested}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Total Return:</span>
                        <div className="font-bold text-green-600">${investmentCalc.result.totalReturn}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">ROI:</span>
                        <div className="font-bold text-green-600">{investmentCalc.result.roi}%</div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Loan Calculator */}
          {calculatorType === 'loan' && (
            <Card className={`shadow-xl border-0 backdrop-blur-xl rounded-2xl ${
              isDarkMode ? 'bg-orange-900/40' : 'bg-orange-50/80'
            }`}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <DollarSign className="w-5 h-5 mr-2 text-orange-600" />
                  Loan Payment Calculator
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Loan Amount ($)</label>
                    <Input
                      type="number"
                      placeholder="250000"
                      value={loanCalc.principal}
                      onChange={(e) => setLoanCalc({ ...loanCalc, principal: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Interest Rate (%)</label>
                    <Input
                      type="number"
                      placeholder="3.5"
                      value={loanCalc.interestRate}
                      onChange={(e) => setLoanCalc({ ...loanCalc, interestRate: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Loan Term (years)</label>
                    <Input
                      type="number"
                      placeholder="30"
                      value={loanCalc.loanTerm}
                      onChange={(e) => setLoanCalc({ ...loanCalc, loanTerm: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                </div>
                <Button
                  onClick={calculateLoanPayment}
                  className="w-full bg-orange-600 hover:bg-orange-700 rounded-xl"
                >
                  Calculate Payment
                </Button>

                {loanCalc.result && (
                  <div className="bg-orange-50 p-4 rounded-xl border border-orange-200">
                    <h4 className="font-medium text-orange-800 mb-2">Loan Results:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Monthly Payment:</span>
                        <div className="font-bold text-orange-600">${loanCalc.result.monthlyPayment}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Total Paid:</span>
                        <div className="font-bold">${loanCalc.result.totalPaid}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Total Interest:</span>
                        <div className="font-bold text-red-600">${loanCalc.result.totalInterest}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Interest %:</span>
                        <div className="font-bold">{loanCalc.result.interestPercentage}%</div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Tax Calculator */}
          {calculatorType === 'tax' && (
            <Card className={`shadow-xl border-0 backdrop-blur-xl rounded-2xl ${
              isDarkMode ? 'bg-indigo-900/40' : 'bg-indigo-50/80'
            }`}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="w-5 h-5 mr-2 text-indigo-600" />
                  Tax Calculator
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Annual Income ({currencies[selectedCurrency as keyof typeof currencies].symbol})</label>
                    <Input
                      type="number"
                      placeholder="75000"
                      value={taxCalc.income}
                      onChange={(e) => setTaxCalc({ ...taxCalc, income: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Filing Status</label>
                    <select
                      value={taxCalc.filingStatus}
                      onChange={(e) => setTaxCalc({ ...taxCalc, filingStatus: e.target.value })}
                      className="w-full px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-indigo-500 focus:outline-none"
                    >
                      <option value="single">Single</option>
                      <option value="marriedJoint">Married Filing Jointly</option>
                      <option value="marriedSeparate">Married Filing Separately</option>
                      <option value="headOfHousehold">Head of Household</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Deductions ({currencies[selectedCurrency as keyof typeof currencies].symbol})</label>
                    <Input
                      type="number"
                      placeholder="13850"
                      value={taxCalc.deductions}
                      onChange={(e) => setTaxCalc({ ...taxCalc, deductions: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">State Tax Rate (%)</label>
                    <Input
                      type="number"
                      placeholder="5.5"
                      value={taxCalc.stateTaxRate}
                      onChange={(e) => setTaxCalc({ ...taxCalc, stateTaxRate: e.target.value })}
                      className="rounded-xl"
                    />
                  </div>
                </div>

                {/* Custom Tax Rate Option */}
                <div className="border-t pt-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <input
                      type="checkbox"
                      id="useCustomRate"
                      checked={taxCalc.useCustomRate}
                      onChange={(e) => setTaxCalc({ ...taxCalc, useCustomRate: e.target.checked })}
                      className="rounded"
                    />
                    <label htmlFor="useCustomRate" className="text-sm font-medium">
                      Use Custom Tax Rate (Flat Rate)
                    </label>
                  </div>

                  {taxCalc.useCustomRate && (
                    <div>
                      <label className="block text-sm font-medium mb-1">Custom Tax Rate (%)</label>
                      <Input
                        type="number"
                        placeholder="15"
                        value={taxCalc.customTaxRate}
                        onChange={(e) => setTaxCalc({ ...taxCalc, customTaxRate: e.target.value })}
                        className="rounded-xl"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        This will override progressive tax brackets and use a flat rate
                      </p>
                    </div>
                  )}
                </div>

                <div className="text-center text-xs text-gray-500 bg-gray-50 p-2 rounded-lg">
                  💡 Real-time calculation updates as you type
                </div>

                {taxCalc.result && (
                  <div className="bg-indigo-50 p-4 rounded-xl border border-indigo-200">
                    <h4 className="font-medium text-indigo-800 mb-2">💰 Tax Results:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Taxable Income:</span>
                        <div className="font-bold">{taxCalc.result.taxableIncome}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Federal Tax:</span>
                        <div className="font-bold text-indigo-600">{taxCalc.result.federalTax}</div>
                      </div>
                      {taxCalc.result.stateTax && (
                        <div>
                          <span className="text-gray-600">State Tax:</span>
                          <div className="font-bold text-blue-600">{taxCalc.result.stateTax}</div>
                        </div>
                      )}
                      <div>
                        <span className="text-gray-600">Total Tax:</span>
                        <div className="font-bold text-red-600">{taxCalc.result.totalTax}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Effective Rate:</span>
                        <div className="font-bold">{taxCalc.result.effectiveRate}%</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Marginal Rate:</span>
                        <div className="font-bold">{taxCalc.result.marginalRate}%</div>
                      </div>
                      <div className="md:col-span-2">
                        <span className="text-gray-600">After-Tax Income:</span>
                        <div className="font-bold text-green-600 text-lg">{taxCalc.result.afterTaxIncome}</div>
                      </div>
                      <div className="md:col-span-2">
                        <span className="text-gray-600">Take-Home Monthly:</span>
                        <div className="font-bold text-green-500">{taxCalc.result.takeHomeMonthly}</div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Budget Planner */}
          {calculatorType === 'budget' && (
            <Card className={`shadow-xl border-0 backdrop-blur-xl rounded-2xl ${
              isDarkMode ? 'bg-teal-900/40' : 'bg-teal-50/80'
            }`}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="w-5 h-5 mr-2 text-teal-600" />
                  50/30/20 Budget Planner
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Monthly After-Tax Income ($)</label>
                  <Input
                    type="number"
                    placeholder="5000"
                    value={budgetPlanner.monthlyIncome}
                    onChange={(e) => setBudgetPlanner({ ...budgetPlanner, monthlyIncome: e.target.value })}
                    className="rounded-xl"
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Housing ($)</label>
                    <Input
                      type="number"
                      placeholder="1500"
                      value={budgetPlanner.expenses.housing}
                      onChange={(e) => setBudgetPlanner({
                        ...budgetPlanner,
                        expenses: { ...budgetPlanner.expenses, housing: e.target.value }
                      })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Transportation ($)</label>
                    <Input
                      type="number"
                      placeholder="400"
                      value={budgetPlanner.expenses.transportation}
                      onChange={(e) => setBudgetPlanner({
                        ...budgetPlanner,
                        expenses: { ...budgetPlanner.expenses, transportation: e.target.value }
                      })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Food ($)</label>
                    <Input
                      type="number"
                      placeholder="600"
                      value={budgetPlanner.expenses.food}
                      onChange={(e) => setBudgetPlanner({
                        ...budgetPlanner,
                        expenses: { ...budgetPlanner.expenses, food: e.target.value }
                      })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Utilities ($)</label>
                    <Input
                      type="number"
                      placeholder="200"
                      value={budgetPlanner.expenses.utilities}
                      onChange={(e) => setBudgetPlanner({
                        ...budgetPlanner,
                        expenses: { ...budgetPlanner.expenses, utilities: e.target.value }
                      })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Insurance ($)</label>
                    <Input
                      type="number"
                      placeholder="300"
                      value={budgetPlanner.expenses.insurance}
                      onChange={(e) => setBudgetPlanner({
                        ...budgetPlanner,
                        expenses: { ...budgetPlanner.expenses, insurance: e.target.value }
                      })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Healthcare ($)</label>
                    <Input
                      type="number"
                      placeholder="150"
                      value={budgetPlanner.expenses.healthcare}
                      onChange={(e) => setBudgetPlanner({
                        ...budgetPlanner,
                        expenses: { ...budgetPlanner.expenses, healthcare: e.target.value }
                      })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Entertainment ($)</label>
                    <Input
                      type="number"
                      placeholder="300"
                      value={budgetPlanner.expenses.entertainment}
                      onChange={(e) => setBudgetPlanner({
                        ...budgetPlanner,
                        expenses: { ...budgetPlanner.expenses, entertainment: e.target.value }
                      })}
                      className="rounded-xl"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Other ($)</label>
                    <Input
                      type="number"
                      placeholder="200"
                      value={budgetPlanner.expenses.other}
                      onChange={(e) => setBudgetPlanner({
                        ...budgetPlanner,
                        expenses: { ...budgetPlanner.expenses, other: e.target.value }
                      })}
                      className="rounded-xl"
                    />
                  </div>
                </div>

                <Button
                  onClick={calculateBudgetPlan}
                  className="w-full bg-teal-600 hover:bg-teal-700 rounded-xl"
                >
                  Calculate Budget
                </Button>

                {budgetPlanner.result && (
                  <div className="space-y-4">
                    <div className={`p-4 rounded-xl border ${
                      budgetPlanner.result.status === 'surplus'
                        ? 'bg-green-50 border-green-200'
                        : 'bg-red-50 border-red-200'
                    }`}>
                      <h4 className={`font-medium mb-2 ${
                        budgetPlanner.result.status === 'surplus' ? 'text-green-800' : 'text-red-800'
                      }`}>
                        Budget Analysis:
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Total Expenses:</span>
                          <div className="font-bold">${budgetPlanner.result.totalExpenses}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">{budgetPlanner.result.status === 'surplus' ? 'Remaining' : 'Deficit'}:</span>
                          <div className={`font-bold ${
                            budgetPlanner.result.status === 'surplus' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {formatCurrency(Math.abs(parseFloat(budgetPlanner.result.remaining)))}
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-600">Savings Rate:</span>
                          <div className="font-bold">{budgetPlanner.result.savingsRate}%</div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-teal-50 p-4 rounded-xl border border-teal-200">
                      <h4 className="font-medium text-teal-800 mb-3">50/30/20 Rule Recommendations:</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center p-3 bg-white rounded-lg">
                          <div>
                            <span className="font-medium">Needs (50%)</span>
                            <p className="text-sm text-gray-600">Housing, utilities, groceries, minimum debt payments</p>
                          </div>
                          <span className="font-bold text-teal-600">${budgetPlanner.result.recommendations.needs}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-white rounded-lg">
                          <div>
                            <span className="font-medium">Wants (30%)</span>
                            <p className="text-sm text-gray-600">Entertainment, dining out, hobbies</p>
                          </div>
                          <span className="font-bold text-blue-600">${budgetPlanner.result.recommendations.wants}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-white rounded-lg">
                          <div>
                            <span className="font-medium">Savings & Debt (20%)</span>
                            <p className="text-sm text-gray-600">Emergency fund, retirement, extra debt payments</p>
                          </div>
                          <span className="font-bold text-green-600">${budgetPlanner.result.recommendations.savings}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="transactions">
          {/* Recent Transactions */}
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
            </CardHeader>
            <CardContent>
              {transactions.length > 0 ? (
                <div className="space-y-3">
                  {transactions
                    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                    .slice(0, 20)
                    .map((transaction) => (
                      <motion.div
                        key={transaction.id}
                        layout
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                      >
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              transaction.type === 'income' 
                                ? 'bg-green-100 text-green-700' 
                                : 'bg-red-100 text-red-700'
                            }`}>
                              {transaction.type}
                            </span>
                            <span className="font-medium">{transaction.category}</span>
                          </div>
                          {transaction.description && (
                            <p className="text-sm text-gray-600 mt-1">{transaction.description}</p>
                          )}
                          <div className="flex items-center text-xs text-gray-500 mt-1">
                            <Calendar className="w-3 h-3 mr-1" />
                            {new Date(transaction.date).toLocaleDateString()}
                          </div>
                        </div>
                        
                        <div className="text-right flex items-center space-x-2">
                          <div className={`font-bold ${
                            transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => deleteTransaction(transaction.id)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 h-8 w-8"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </motion.div>
                    ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <DollarSign className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-500 mb-2">No transactions yet</h3>
                  <p className="text-gray-400 mb-4">Start tracking your finances!</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* New Transaction Modal */}
      <AnimatePresence>
        {showNewTransaction && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowNewTransaction(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-2xl p-6 w-full max-w-md"
            >
              <h3 className="text-xl font-bold mb-4">Add Transaction</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <select
                    value={newTransaction.type}
                    onChange={(e) => setNewTransaction({ ...newTransaction, type: e.target.value as 'income' | 'expense' })}
                    className="px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:outline-none"
                  >
                    <option value="expense">Expense</option>
                    <option value="income">Income</option>
                  </select>
                  
                  <Input
                    type="number"
                    placeholder="Amount"
                    value={newTransaction.amount}
                    onChange={(e) => setNewTransaction({ ...newTransaction, amount: e.target.value })}
                    className="rounded-xl border-2 border-gray-200 focus:border-purple-500"
                  />
                </div>

                <select
                  value={newTransaction.category}
                  onChange={(e) => setNewTransaction({ ...newTransaction, category: e.target.value })}
                  className="w-full px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:outline-none"
                >
                  <option value="">Select Category</option>
                  {(newTransaction.type === 'expense' ? expenseCategories : incomeCategories).map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>

                <Input
                  placeholder="Description (optional)"
                  value={newTransaction.description}
                  onChange={(e) => setNewTransaction({ ...newTransaction, description: e.target.value })}
                  className="rounded-xl border-2 border-gray-200 focus:border-purple-500"
                />

                <Input
                  type="date"
                  value={newTransaction.date}
                  onChange={(e) => setNewTransaction({ ...newTransaction, date: e.target.value })}
                  className="rounded-xl border-2 border-gray-200 focus:border-purple-500"
                />

                <div className="flex space-x-2">
                  <Button onClick={addTransaction} className="flex-1 bg-purple-600 hover:bg-purple-700">
                    Add Transaction
                  </Button>
                  <Button variant="outline" onClick={() => setShowNewTransaction(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* New Budget Modal */}
      <AnimatePresence>
        {showNewBudget && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowNewBudget(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-2xl p-6 w-full max-w-md"
            >
              <h3 className="text-xl font-bold mb-4">Create Budget</h3>
              <div className="space-y-4">
                <select
                  value={newBudget.category}
                  onChange={(e) => setNewBudget({ ...newBudget, category: e.target.value })}
                  className="w-full px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:outline-none"
                >
                  <option value="">Select Category</option>
                  {expenseCategories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>

                <Input
                  type="number"
                  placeholder="Budget Limit"
                  value={newBudget.limit}
                  onChange={(e) => setNewBudget({ ...newBudget, limit: e.target.value })}
                  className="rounded-xl border-2 border-gray-200 focus:border-purple-500"
                />

                <select
                  value={newBudget.period}
                  onChange={(e) => setNewBudget({ ...newBudget, period: e.target.value as 'monthly' | 'weekly' })}
                  className="w-full px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:outline-none"
                >
                  <option value="monthly">Monthly</option>
                  <option value="weekly">Weekly</option>
                </select>

                <div className="flex space-x-2">
                  <Button onClick={addBudget} className="flex-1 bg-green-600 hover:bg-green-700">
                    Create Budget
                  </Button>
                  <Button variant="outline" onClick={() => setShowNewBudget(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      </motion.div>
    </PageContainer>
  );
};

export default Finance;
