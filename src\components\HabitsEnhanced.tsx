import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, Star, Calendar, Heart, Target, TrendingUp, Award, Flame, 
  Clock, Bell, Brain, Zap, Moon, Sun, Coffee, Sunset, 
  SkipForward, Settings, BarChart3, Trophy, Gift, 
  MessageCircle, Palette, CheckCircle, Circle, Edit,
  Trash2, RotateCcw, Timer, MapPin, Users, Sparkles
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface HabitCompletion {
  date: string;
  completed: boolean;
  skipped: boolean;
  reflection?: string;
  mood?: 'great' | 'good' | 'okay' | 'bad';
  timeSpent?: number;
  location?: string;
}

interface Habit {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  category: 'morning' | 'evening' | 'anytime';
  frequency: 'daily' | 'weekly' | 'custom';
  customFrequency?: number;
  targetTime?: number;
  reminders: {
    enabled: boolean;
    times: string[];
    sound: boolean;
  };
  skipRules: {
    graceDays: number;
    autoAdjust: boolean;
    maxSkipsPerWeek: number;
  };
  scoring: {
    basePoints: number;
    streakMultiplier: number;
    perfectWeekBonus: number;
  };
  completions: HabitCompletion[];
  streak: number;
  longestStreak: number;
  totalXP: number;
  level: number;
  createdAt: string;
  isActive: boolean;
  nightOwlMode: boolean;
  aiOptimalTime?: string;
  milestones: {
    days: number;
    achieved: boolean;
    celebratedAt?: string;
  }[];
}

const HabitsEnhanced = () => {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const saved = localStorage.getItem('billionDollarDarkMode');
    return saved ? JSON.parse(saved) : false;
  });

  // Core State
  const [habits, setHabits] = useState<Habit[]>([]);
  const [showNewHabit, setShowNewHabit] = useState(false);
  const [selectedHabit, setSelectedHabit] = useState<Habit | null>(null);
  const [showHabitDetails, setShowHabitDetails] = useState(false);
  const [showReflection, setShowReflection] = useState(false);
  const [reflectionHabitId, setReflectionHabitId] = useState<string>('');

  // View State
  const [viewMode, setViewMode] = useState<'list' | 'calendar' | 'analytics'>('list');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'morning' | 'evening' | 'anytime'>('all');
  const [showCompleted, setShowCompleted] = useState(true);

  // Form State
  const [newHabit, setNewHabit] = useState({
    name: '',
    description: '',
    icon: '🎯',
    color: '#8B5CF6',
    category: 'anytime' as Habit['category'],
    frequency: 'daily' as Habit['frequency'],
    customFrequency: 3,
    targetTime: 30,
    reminders: {
      enabled: false,
      times: ['09:00'],
      sound: true
    },
    skipRules: {
      graceDays: 1,
      autoAdjust: true,
      maxSkipsPerWeek: 2
    },
    scoring: {
      basePoints: 10,
      streakMultiplier: 1.5,
      perfectWeekBonus: 50
    },
    nightOwlMode: false
  });

  // Reflection State
  const [reflection, setReflection] = useState({
    mood: 'good' as HabitCompletion['mood'],
    timeSpent: 30,
    location: '',
    notes: ''
  });

  useEffect(() => {
    loadHabits();
    setupNotifications();
    checkMilestones();
  }, []);

  const loadHabits = () => {
    const savedHabits = localStorage.getItem('smartHabits');
    if (savedHabits) {
      setHabits(JSON.parse(savedHabits));
    }
  };

  const saveHabits = (updatedHabits: Habit[]) => {
    setHabits(updatedHabits);
    localStorage.setItem('smartHabits', JSON.stringify(updatedHabits));
  };

  const setupNotifications = () => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  const checkMilestones = () => {
    habits.forEach(habit => {
      habit.milestones.forEach(milestone => {
        if (habit.streak >= milestone.days && !milestone.achieved) {
          milestone.achieved = true;
          milestone.celebratedAt = new Date().toISOString();
          triggerCelebration(habit.name, milestone.days);
        }
      });
    });
  };

  const triggerCelebration = (habitName: string, days: number) => {
    if (Notification.permission === 'granted') {
      new Notification(`🎉 Milestone Achieved!`, {
        body: `${habitName}: ${days} day streak!`,
        icon: '/favicon.ico'
      });
    }
  };

  // Analytics and Helper Functions
  const calculateStreak = (completions: HabitCompletion[]) => {
    if (completions.length === 0) return 0;
    
    const completedDates = completions
      .filter(c => c.completed)
      .map(c => new Date(c.date))
      .sort((a, b) => b.getTime() - a.getTime());
    
    if (completedDates.length === 0) return 0;
    
    let streak = 0;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const latestCompletion = completedDates[0];
    latestCompletion.setHours(0, 0, 0, 0);
    
    const daysDiff = Math.floor((today.getTime() - latestCompletion.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff > 1) return 0;
    
    let currentDate = new Date(latestCompletion);
    for (const completionDate of completedDates) {
      completionDate.setHours(0, 0, 0, 0);
      if (completionDate.getTime() === currentDate.getTime()) {
        streak++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else if (completionDate.getTime() < currentDate.getTime()) {
        break;
      }
    }
    
    return streak;
  };

  const getConsistencyPercentage = (habit: Habit, days: number = 30) => {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const completionsInPeriod = habit.completions.filter(c => {
      const date = new Date(c.date);
      return date >= startDate && date <= endDate && c.completed;
    });
    
    return Math.round((completionsInPeriod.length / days) * 100);
  };

  const getHabitStats = () => {
    const totalHabits = habits.filter(h => h.isActive).length;
    const completedToday = habits.filter(h => isCompletedToday(h)).length;
    const totalXP = habits.reduce((sum, h) => sum + h.totalXP, 0);
    const averageStreak = habits.length > 0 ? 
      Math.round(habits.reduce((sum, h) => sum + h.streak, 0) / habits.length) : 0;
    const longestStreak = Math.max(...habits.map(h => h.longestStreak), 0);
    const perfectDays = getPerfectDaysCount();
    
    return {
      totalHabits,
      completedToday,
      totalXP,
      averageStreak,
      longestStreak,
      perfectDays,
      completionRate: totalHabits > 0 ? Math.round((completedToday / totalHabits) * 100) : 0
    };
  };

  const getPerfectDaysCount = () => {
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    });
    
    return last30Days.filter(date => {
      const activeHabitsOnDate = habits.filter(h => new Date(h.createdAt) <= new Date(date));
      if (activeHabitsOnDate.length === 0) return false;
      
      const completedOnDate = activeHabitsOnDate.filter(h => 
        h.completions.some(c => c.date === date && c.completed)
      );
      
      return completedOnDate.length === activeHabitsOnDate.length;
    }).length;
  };

  const isCompletedToday = (habit: Habit) => {
    const today = new Date().toISOString().split('T')[0];
    return habit.completions.some(c => c.date === today && c.completed);
  };

  const isSkippedToday = (habit: Habit) => {
    const today = new Date().toISOString().split('T')[0];
    return habit.completions.some(c => c.date === today && c.skipped);
  };

  const canCompleteHabit = (habit: Habit) => {
    if (habit.nightOwlMode) return true;
    
    const now = new Date();
    const currentHour = now.getHours();
    
    if (habit.category === 'morning' && currentHour > 12) return false;
    if (habit.category === 'evening' && currentHour < 18) return false;
    
    return true;
  };

  const getFilteredHabits = () => {
    return habits.filter(habit => {
      if (!habit.isActive) return false;
      if (selectedCategory !== 'all' && habit.category !== selectedCategory) return false;
      if (!showCompleted && isCompletedToday(habit)) return false;
      return true;
    });
  };

  // Constants and Data
  const stats = getHabitStats();
  const filteredHabits = getFilteredHabits();

  const habitIcons = [
    '🎯', '💪', '📚', '🧘', '🏃', '💧', '🥗', '😴', '🎨', '🎵',
    '✍️', '🌱', '🧠', '❤️', '🔥', '⭐', '🌟', '💎', '🚀', '🎪'
  ];

  const habitColors = [
    '#8B5CF6', '#EC4899', '#10B981', '#F59E0B', '#EF4444', '#3B82F6', 
    '#8B5A00', '#06B6D4', '#84CC16', '#F97316', '#EF4444', '#8B5CF6'
  ];

  const categoryIcons = {
    morning: <Sun className="w-4 h-4" />,
    evening: <Moon className="w-4 h-4" />,
    anytime: <Clock className="w-4 h-4" />
  };

  const moodEmojis = {
    great: '😄',
    good: '😊',
    okay: '😐',
    bad: '😞'
  };

  return (
    <div className={`min-h-screen transition-all duration-700 ${
      isDarkMode
        ? 'bg-gradient-to-br from-slate-900 via-purple-900/30 to-slate-900'
        : 'bg-gradient-to-br from-purple-50 via-white to-pink-50'
    }`}>
      {/* Floating particles */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full opacity-20"
            animate={{
              y: [-20, -100],
              x: [0, Math.random() * 100 - 50],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: '100%',
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-4 pb-32">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-4 sm:mb-6"
        >
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 bg-clip-text text-transparent">
              🧠 Smart Habit Tracker
            </h1>
            <p className={`text-sm sm:text-base ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              AI-powered habit building with streaks, analytics & celebrations
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default HabitsEnhanced;
