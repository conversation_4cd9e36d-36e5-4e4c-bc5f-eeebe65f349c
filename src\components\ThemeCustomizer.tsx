import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Palette, 
  Sliders, 
  Image, 
  Volume2, 
  Vibrate, 
  Download, 
  Upload, 
  Save,
  Trash2,
  Eye,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ThemeEngine, { ThemeConfig, defaultThemes, wallpaperPatterns } from '../services/ThemeEngine';

const ThemeCustomizer: React.FC = () => {
  const [currentTheme, setCurrentTheme] = useState<ThemeConfig>(ThemeEngine.getCurrentTheme());
  const [customTheme, setCustomTheme] = useState<ThemeConfig>(currentTheme);
  const [previewMode, setPreviewMode] = useState(false);
  const [themeName, setThemeName] = useState('');

  useEffect(() => {
    if (previewMode) {
      ThemeEngine.setTheme(customTheme.id);
    } else {
      ThemeEngine.setTheme(currentTheme.id);
    }
  }, [previewMode, customTheme, currentTheme]);

  const updateCustomTheme = (updates: Partial<ThemeConfig>) => {
    const updated = { ...customTheme, ...updates };
    setCustomTheme(updated);
    
    if (previewMode) {
      // Apply changes immediately in preview mode
      const tempId = 'preview-temp';
      ThemeEngine.updateCustomTheme(tempId, updated);
    }
  };

  const updateColors = (colorKey: keyof ThemeConfig['colors'], value: string) => {
    updateCustomTheme({
      colors: { ...customTheme.colors, [colorKey]: value }
    });
  };

  const updateEffects = (effectKey: keyof ThemeConfig['effects'], value: any) => {
    updateCustomTheme({
      effects: { ...customTheme.effects, [effectKey]: value }
    });
  };

  const updateWallpaper = (wallpaperUpdates: Partial<ThemeConfig['wallpaper']>) => {
    updateCustomTheme({
      wallpaper: { ...customTheme.wallpaper, ...wallpaperUpdates }
    });
  };

  const updateSounds = (soundUpdates: Partial<ThemeConfig['sounds']>) => {
    updateCustomTheme({
      sounds: { ...customTheme.sounds, ...soundUpdates }
    });
  };

  const updateHaptics = (hapticsUpdates: Partial<ThemeConfig['haptics']>) => {
    updateCustomTheme({
      haptics: { ...customTheme.haptics, ...hapticsUpdates }
    });
  };

  const saveCustomTheme = () => {
    if (!themeName.trim()) {
      alert('Please enter a theme name');
      return;
    }

    const themeToSave = { ...customTheme, name: themeName };
    const newId = ThemeEngine.createCustomTheme(themeToSave);
    ThemeEngine.setTheme(newId);
    setCurrentTheme(ThemeEngine.getCurrentTheme());
    setThemeName('');
    ThemeEngine.playSound('taskComplete');
    alert('Theme saved successfully!');
  };

  const resetToDefault = () => {
    setCustomTheme(defaultThemes[0]);
    setPreviewMode(false);
  };

  const exportTheme = () => {
    try {
      const themeData = ThemeEngine.exportTheme(currentTheme.id);
      const blob = new Blob([themeData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${currentTheme.name.replace(/\s+/g, '-').toLowerCase()}-theme.json`;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      alert('Failed to export theme');
    }
  };

  const importTheme = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const themeData = e.target?.result as string;
        const newId = ThemeEngine.importTheme(themeData);
        ThemeEngine.setTheme(newId);
        setCurrentTheme(ThemeEngine.getCurrentTheme());
        alert('Theme imported successfully!');
      } catch (error) {
        alert('Failed to import theme: ' + (error as Error).message);
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            Theme Customizer
          </h1>
          <div className="flex space-x-2">
            <Button
              onClick={() => setPreviewMode(!previewMode)}
              variant={previewMode ? "default" : "outline"}
              className={previewMode ? "bg-gradient-to-r from-green-500 to-emerald-500" : ""}
            >
              <Eye className="w-4 h-4 mr-2" />
              {previewMode ? 'Exit Preview' : 'Preview'}
            </Button>
            <Button onClick={exportTheme} variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <label className="cursor-pointer">
              <Button variant="outline" asChild>
                <span>
                  <Upload className="w-4 h-4 mr-2" />
                  Import
                </span>
              </Button>
              <input
                type="file"
                accept=".json"
                onChange={importTheme}
                className="hidden"
              />
            </label>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Theme Selection */}
          <div className="lg:col-span-1">
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Palette className="w-5 h-5 mr-2" />
                  Preset Themes
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {ThemeEngine.getAllThemes().map((theme) => (
                  <Button
                    key={theme.id}
                    onClick={() => {
                      setCurrentTheme(theme);
                      setCustomTheme(theme);
                      ThemeEngine.setTheme(theme.id);
                    }}
                    variant={currentTheme.id === theme.id ? "default" : "outline"}
                    className="w-full justify-start"
                  >
                    <div
                      className="w-4 h-4 rounded-full mr-2"
                      style={{ background: theme.colors.gradient.start }}
                    />
                    {theme.name}
                  </Button>
                ))}
              </CardContent>
            </Card>

            {/* Save Custom Theme */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm mt-4">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Save className="w-5 h-5 mr-2" />
                  Save Custom Theme
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Input
                  placeholder="Theme name"
                  value={themeName}
                  onChange={(e) => setThemeName(e.target.value)}
                />
                <Button onClick={saveCustomTheme} className="w-full">
                  Save Theme
                </Button>
                <Button onClick={resetToDefault} variant="outline" className="w-full">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Reset to Default
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Customization Panel */}
          <div className="lg:col-span-2">
            <Tabs defaultValue="colors" className="space-y-4">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="colors">Colors</TabsTrigger>
                <TabsTrigger value="effects">Effects</TabsTrigger>
                <TabsTrigger value="wallpaper">Wallpaper</TabsTrigger>
                <TabsTrigger value="sounds">Sounds</TabsTrigger>
                <TabsTrigger value="haptics">Haptics</TabsTrigger>
              </TabsList>

              {/* Colors Tab */}
              <TabsContent value="colors">
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle>Color Palette</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      {Object.entries(customTheme.colors).map(([key, value]) => {
                        if (key === 'gradient') return null;
                        return (
                          <div key={key} className="space-y-2">
                            <label className="text-sm font-medium capitalize">
                              {key.replace(/([A-Z])/g, ' $1')}
                            </label>
                            <div className="flex space-x-2">
                              <input
                                type="color"
                                value={value}
                                onChange={(e) => updateColors(key as keyof ThemeConfig['colors'], e.target.value)}
                                className="w-12 h-10 rounded border"
                              />
                              <Input
                                value={value}
                                onChange={(e) => updateColors(key as keyof ThemeConfig['colors'], e.target.value)}
                                className="flex-1"
                              />
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    {/* Gradient Controls */}
                    <div className="space-y-4 border-t pt-4">
                      <h4 className="font-medium">Gradient Settings</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Gradient Start</label>
                          <div className="flex space-x-2">
                            <input
                              type="color"
                              value={customTheme.colors.gradient.start}
                              onChange={(e) => updateColors('gradient', { ...customTheme.colors.gradient, start: e.target.value })}
                              className="w-12 h-10 rounded border"
                            />
                            <Input
                              value={customTheme.colors.gradient.start}
                              onChange={(e) => updateColors('gradient', { ...customTheme.colors.gradient, start: e.target.value })}
                              className="flex-1"
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Gradient End</label>
                          <div className="flex space-x-2">
                            <input
                              type="color"
                              value={customTheme.colors.gradient.end}
                              onChange={(e) => updateColors('gradient', { ...customTheme.colors.gradient, end: e.target.value })}
                              className="w-12 h-10 rounded border"
                            />
                            <Input
                              value={customTheme.colors.gradient.end}
                              onChange={(e) => updateColors('gradient', { ...customTheme.colors.gradient, end: e.target.value })}
                              className="flex-1"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Direction: {customTheme.colors.gradient.direction}°</label>
                        <input
                          type="range"
                          min="0"
                          max="360"
                          value={customTheme.colors.gradient.direction}
                          onChange={(e) => updateColors('gradient', { ...customTheme.colors.gradient, direction: parseInt(e.target.value) })}
                          className="w-full"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Effects Tab */}
              <TabsContent value="effects">
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Sliders className="w-5 h-5 mr-2" />
                      Visual Effects
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Blur Amount: {customTheme.effects.blur}px</label>
                      <input
                        type="range"
                        min="0"
                        max="20"
                        value={customTheme.effects.blur}
                        onChange={(e) => updateEffects('blur', parseInt(e.target.value))}
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Border Radius: {customTheme.effects.borderRadius}px</label>
                      <input
                        type="range"
                        min="0"
                        max="30"
                        value={customTheme.effects.borderRadius}
                        onChange={(e) => updateEffects('borderRadius', parseInt(e.target.value))}
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <label className="text-sm font-medium">Glassmorphism</label>
                        <input
                          type="checkbox"
                          checked={customTheme.effects.glassmorphism}
                          onChange={(e) => updateEffects('glassmorphism', e.target.checked)}
                          className="rounded"
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <label className="text-sm font-medium">Shadows</label>
                        <input
                          type="checkbox"
                          checked={customTheme.effects.shadows}
                          onChange={(e) => updateEffects('shadows', e.target.checked)}
                          className="rounded"
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <label className="text-sm font-medium">Animations</label>
                        <input
                          type="checkbox"
                          checked={customTheme.effects.animations}
                          onChange={(e) => updateEffects('animations', e.target.checked)}
                          className="rounded"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Wallpaper Tab */}
              <TabsContent value="wallpaper">
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Image className="w-5 h-5 mr-2" />
                      Background & Wallpaper
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Wallpaper Type</label>
                      <select
                        value={customTheme.wallpaper.type}
                        onChange={(e) => updateWallpaper({ type: e.target.value as any })}
                        className="w-full px-3 py-2 rounded border"
                      >
                        <option value="none">None</option>
                        <option value="gradient">Gradient</option>
                        <option value="pattern">Pattern</option>
                        <option value="image">Custom Image</option>
                      </select>
                    </div>

                    {customTheme.wallpaper.type !== 'none' && (
                      <>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Opacity: {customTheme.wallpaper.opacity}%</label>
                          <input
                            type="range"
                            min="0"
                            max="100"
                            value={customTheme.wallpaper.opacity}
                            onChange={(e) => updateWallpaper({ opacity: parseInt(e.target.value) })}
                            className="w-full"
                          />
                        </div>

                        {(customTheme.wallpaper.type === 'gradient' || customTheme.wallpaper.type === 'pattern') && (
                          <div className="space-y-2">
                            <label className="text-sm font-medium">Preset Patterns</label>
                            <div className="grid grid-cols-2 gap-2">
                              {wallpaperPatterns.map((pattern) => (
                                <Button
                                  key={pattern.name}
                                  onClick={() => updateWallpaper({ value: pattern.value })}
                                  variant="outline"
                                  className="h-16 p-2"
                                  style={{ background: pattern.value }}
                                >
                                  <span className="text-xs text-white bg-black/50 px-2 py-1 rounded">
                                    {pattern.name}
                                  </span>
                                </Button>
                              ))}
                            </div>
                          </div>
                        )}

                        <div className="space-y-2">
                          <label className="text-sm font-medium">Custom Value</label>
                          <Input
                            value={customTheme.wallpaper.value}
                            onChange={(e) => updateWallpaper({ value: e.target.value })}
                            placeholder="CSS background value"
                          />
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Sounds Tab */}
              <TabsContent value="sounds">
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Volume2 className="w-5 h-5 mr-2" />
                      Sound Effects
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <label className="text-sm font-medium">Enable Sounds</label>
                      <input
                        type="checkbox"
                        checked={customTheme.sounds.enabled}
                        onChange={(e) => updateSounds({ enabled: e.target.checked })}
                        className="rounded"
                      />
                    </div>

                    {customTheme.sounds.enabled && (
                      <>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Volume: {customTheme.sounds.volume}%</label>
                          <input
                            type="range"
                            min="0"
                            max="100"
                            value={customTheme.sounds.volume}
                            onChange={(e) => updateSounds({ volume: parseInt(e.target.value) })}
                            className="w-full"
                          />
                        </div>

                        <div className="space-y-3">
                          {Object.entries(customTheme.sounds).map(([key, value]) => {
                            if (key === 'enabled' || key === 'volume') return null;
                            return (
                              <div key={key} className="space-y-2">
                                <label className="text-sm font-medium capitalize">
                                  {key.replace(/([A-Z])/g, ' $1')}
                                </label>
                                <select
                                  value={value}
                                  onChange={(e) => updateSounds({ [key]: e.target.value })}
                                  className="w-full px-3 py-2 rounded border"
                                >
                                  <option value="none">None</option>
                                  <option value="success">Success</option>
                                  <option value="gentle">Gentle</option>
                                  <option value="digital">Digital</option>
                                  <option value="chime">Chime</option>
                                  <option value="bubble">Bubble</option>
                                  <option value="tick">Tick</option>
                                </select>
                              </div>
                            );
                          })}
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Haptics Tab */}
              <TabsContent value="haptics">
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Vibrate className="w-5 h-5 mr-2" />
                      Haptic Feedback
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <label className="text-sm font-medium">Enable Haptics</label>
                      <input
                        type="checkbox"
                        checked={customTheme.haptics.enabled}
                        onChange={(e) => updateHaptics({ enabled: e.target.checked })}
                        className="rounded"
                      />
                    </div>

                    {customTheme.haptics.enabled && (
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Intensity</label>
                        <select
                          value={customTheme.haptics.intensity}
                          onChange={(e) => updateHaptics({ intensity: e.target.value as any })}
                          className="w-full px-3 py-2 rounded border"
                        >
                          <option value="light">Light</option>
                          <option value="medium">Medium</option>
                          <option value="heavy">Heavy</option>
                        </select>
                        <Button
                          onClick={() => ThemeEngine.triggerHaptic(customTheme.haptics.intensity)}
                          variant="outline"
                          className="w-full mt-2"
                        >
                          Test Haptic
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeCustomizer;
