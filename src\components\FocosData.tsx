
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Target, DollarSign, Heart, Brain, TrendingUp, Sun, Moon, Clock, Layers, Zap } from 'lucide-react';

interface OnboardingData {
  name: string;
  avatar: string;
  goals: string[];
  workStyle: string;
  motto: string;
  notificationsEnabled: boolean;
}

const FocosData = () => {
  const [data, setData] = useState<OnboardingData | null>(null);

  useEffect(() => {
    const savedData = localStorage.getItem('focosOnboardingData');
    if (savedData) {
      setData(JSON.parse(savedData));
    }
  }, []);

  const clearData = () => {
    localStorage.removeItem('focosOnboardingData');
    localStorage.removeItem('onboardingComplete');
    window.location.reload();
  };

  const goalIcons = {
    productivity: Target,
    health: Heart,
    finance: DollarSign,
    learning: Brain,
    mindfulness: Heart,
    career: TrendingUp
  };

  const workStyleIcons = {
    'early-bird': Sun,
    'night-owl': Moon,
    flexible: Clock,
    structured: Layers,
    'creative-bursts': Zap
  };

  if (!data) {
    return (
      <div className="min-h-screen p-6 bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 text-white">
        <div className="text-center">
          <p>No FOCOS onboarding data found.</p>
          <Button onClick={clearData} className="mt-4">
            Start FOCOS Onboarding
          </Button>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen p-6 pb-24 bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900"
    >
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            Welcome to FOCOS, {data.name}! {data.avatar}
          </h1>
          <p className="text-gray-300">Your personalized productivity journey starts here</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="bg-white/10 border-white/20 backdrop-blur-md">
            <CardHeader>
              <CardTitle className="text-white">Your Goals</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.goals.map((goalId) => {
                  const Icon = goalIcons[goalId as keyof typeof goalIcons];
                  return (
                    <div key={goalId} className="flex items-center space-x-3 text-white">
                      <Icon className="w-5 h-5 text-purple-400" />
                      <span className="capitalize">{goalId.replace('-', ' ')}</span>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 border-white/20 backdrop-blur-md">
            <CardHeader>
              <CardTitle className="text-white">Work Style</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-3 text-white">
                {React.createElement(workStyleIcons[data.workStyle as keyof typeof workStyleIcons], {
                  className: "w-6 h-6 text-purple-400"
                })}
                <span className="capitalize">{data.workStyle.replace('-', ' ')}</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 border-white/20 backdrop-blur-md md:col-span-2">
            <CardHeader>
              <CardTitle className="text-white">Your Motto</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-lg italic">"{data.motto}"</p>
            </CardContent>
          </Card>

          <Card className="bg-white/10 border-white/20 backdrop-blur-md">
            <CardHeader>
              <CardTitle className="text-white">Notifications</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300">
                {data.notificationsEnabled ? '🔔 Enabled' : '🔕 Disabled'}
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="text-center mt-8">
          <Button 
            onClick={clearData}
            variant="outline"
            className="bg-white/10 border-white/20 text-white hover:bg-white/20"
          >
            Reset Onboarding
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

export default FocosData;
