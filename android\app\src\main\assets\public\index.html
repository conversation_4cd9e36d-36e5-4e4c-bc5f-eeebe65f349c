
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <title>FOCOS - Premium Productivity Suite</title>
    <meta name="description" content="The Ultimate Productivity Experience - Premium Mobile-First Design with Billion Dollar App Quality" />
    <meta name="author" content="FOCOS Team" />

    <!-- Mobile App Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="FOCOS" />
    <meta name="theme-color" content="#8B5CF6" />
    <meta name="msapplication-TileColor" content="#8B5CF6" />

    <!-- Prevent zoom on input focus (iOS) -->
    <meta name="format-detection" content="telephone=no" />

    <meta property="og:title" content="FOCOS - Premium Productivity Suite" />
    <meta property="og:description" content="The Ultimate Productivity Experience - Premium Mobile-First Design with Billion Dollar App Quality" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@focos_app" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <!-- Preload critical fonts for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <style>
      /* Critical CSS for initial load */
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        overflow-x: hidden;
      }

      #root {
        min-height: 100vh;
        min-height: 100dvh; /* Dynamic viewport height for mobile */
      }

      /* Loading animation */
      .loading-spinner {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }

      @keyframes spin {
        to { transform: translate(-50%, -50%) rotate(360deg); }
      }
    </style>
    <script type="module" crossorigin src="/assets/index-D2A28kal.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-BSJBmlrO.css">
  </head>

  <body>
    <div id="root">
      <!-- Loading spinner for initial load -->
      <div class="loading-spinner"></div>
    </div>

    <!-- Service Worker for PWA functionality -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
