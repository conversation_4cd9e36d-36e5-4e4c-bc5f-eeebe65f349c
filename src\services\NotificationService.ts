// Enhanced Notification Service for FOCOS
export interface NotificationConfig {
  title: string;
  body: string;
  icon?: string;
  tag?: string;
  badge?: string;
  data?: any;
  requireInteraction?: boolean;
  silent?: boolean;
  vibrate?: number[];
  actions?: NotificationAction[];
}

export interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

class NotificationService {
  private static instance: NotificationService;
  private permission: NotificationPermission = 'default';
  private isSupported: boolean = false;

  constructor() {
    this.isSupported = 'Notification' in window;
    if (this.isSupported) {
      this.permission = Notification.permission;
    }
  }

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  async requestPermission(): Promise<NotificationPermission> {
    if (!this.isSupported) {
      console.warn('Notifications not supported');
      return 'denied';
    }

    if (this.permission === 'default') {
      this.permission = await Notification.requestPermission();
    }

    return this.permission;
  }

  async sendNotification(config: NotificationConfig): Promise<Notification | null> {
    if (!this.isSupported || this.permission !== 'granted') {
      console.warn('Notifications not available or permission denied');
      return null;
    }

    try {
      const notification = new Notification(config.title, {
        body: config.body,
        icon: config.icon || '/favicon.ico',
        tag: config.tag,
        badge: config.badge,
        data: config.data,
        requireInteraction: config.requireInteraction || false,
        silent: config.silent || false,
        vibrate: config.vibrate || [200, 100, 200],
        actions: config.actions || []
      });

      // Auto-close after 5 seconds unless requireInteraction is true
      if (!config.requireInteraction) {
        setTimeout(() => {
          notification.close();
        }, 5000);
      }

      return notification;
    } catch (error) {
      console.error('Failed to send notification:', error);
      return null;
    }
  }

  // Predefined notification types
  async sendTaskReminder(taskTitle: string, dueTime?: string): Promise<void> {
    await this.sendNotification({
      title: '📋 Task Reminder',
      body: `Don't forget: ${taskTitle}${dueTime ? ` (Due: ${dueTime})` : ''}`,
      tag: 'task-reminder',
      vibrate: [200, 100, 200, 100, 200],
      actions: [
        { action: 'complete', title: 'Mark Complete' },
        { action: 'snooze', title: 'Snooze 15min' }
      ]
    });
  }

  async sendHabitReminder(habitName: string, streakDays: number): Promise<void> {
    await this.sendNotification({
      title: '🎯 Habit Reminder',
      body: `Time for ${habitName}! Current streak: ${streakDays} days`,
      tag: 'habit-reminder',
      vibrate: [100, 50, 100],
      actions: [
        { action: 'complete', title: 'Mark Done' },
        { action: 'skip', title: 'Skip Today' }
      ]
    });
  }

  async sendBudgetAlert(category: string, percentage: number, type: 'warning' | 'danger' | 'info'): Promise<void> {
    const icons = { warning: '⚠️', danger: '🚨', info: '💡' };
    const vibrations = {
      warning: [200, 100, 200],
      danger: [300, 100, 300, 100, 300],
      info: [100]
    };

    await this.sendNotification({
      title: `${icons[type]} Budget Alert: ${category}`,
      body: `${percentage.toFixed(1)}% of budget used`,
      tag: `budget-${category}`,
      vibrate: vibrations[type],
      requireInteraction: type === 'danger',
      actions: [
        { action: 'view', title: 'View Budget' },
        { action: 'adjust', title: 'Adjust Budget' }
      ]
    });
  }

  async sendFinancialMilestone(message: string, amount?: number): Promise<void> {
    await this.sendNotification({
      title: '💰 Financial Milestone!',
      body: message + (amount ? ` ($${amount.toFixed(2)})` : ''),
      tag: 'financial-milestone',
      vibrate: [200, 100, 200, 100, 200, 100, 200],
      requireInteraction: true,
      actions: [
        { action: 'celebrate', title: '🎉 Celebrate!' },
        { action: 'view', title: 'View Details' }
      ]
    });
  }

  async sendGoalProgress(goalName: string, progress: number): Promise<void> {
    await this.sendNotification({
      title: '🎯 Goal Progress Update',
      body: `${goalName}: ${progress}% complete`,
      tag: 'goal-progress',
      vibrate: [150, 75, 150],
      actions: [
        { action: 'view', title: 'View Goal' },
        { action: 'update', title: 'Update Progress' }
      ]
    });
  }

  async sendPomodoroComplete(sessionType: 'work' | 'break'): Promise<void> {
    const messages = {
      work: 'Great focus session! Time for a break.',
      break: 'Break time over! Ready for another session?'
    };

    await this.sendNotification({
      title: '⏰ Pomodoro Complete!',
      body: messages[sessionType],
      tag: 'pomodoro',
      vibrate: [300, 100, 300],
      requireInteraction: true,
      actions: [
        { action: 'start', title: sessionType === 'work' ? 'Start Break' : 'Start Work' },
        { action: 'stop', title: 'Stop Timer' }
      ]
    });
  }

  async sendMoodCheckIn(): Promise<void> {
    await this.sendNotification({
      title: '😊 Daily Mood Check-in',
      body: 'How are you feeling today? Take a moment to log your mood.',
      tag: 'mood-checkin',
      vibrate: [100, 50, 100],
      actions: [
        { action: 'log', title: 'Log Mood' },
        { action: 'skip', title: 'Skip Today' }
      ]
    });
  }

  async sendJournalReminder(): Promise<void> {
    await this.sendNotification({
      title: '📝 Journal Reminder',
      body: 'Take a few minutes to reflect on your day.',
      tag: 'journal-reminder',
      vibrate: [150],
      actions: [
        { action: 'write', title: 'Start Writing' },
        { action: 'later', title: 'Remind Later' }
      ]
    });
  }

  async sendWellnessReminder(type: 'water' | 'exercise' | 'meditation'): Promise<void> {
    const messages = {
      water: 'Time to hydrate! Have you had enough water today?',
      exercise: 'Time to move! A little exercise goes a long way.',
      meditation: 'Take a mindful moment. Your mental health matters.'
    };

    const icons = {
      water: '💧',
      exercise: '🏃‍♂️',
      meditation: '🧘‍♀️'
    };

    await this.sendNotification({
      title: `${icons[type]} Wellness Reminder`,
      body: messages[type],
      tag: `wellness-${type}`,
      vibrate: [100, 50, 100],
      actions: [
        { action: 'done', title: 'Mark Done' },
        { action: 'snooze', title: 'Remind Later' }
      ]
    });
  }

  // Schedule recurring notifications
  scheduleRecurringNotifications(): void {
    // Morning motivation (8 AM)
    this.scheduleDaily(8, 0, () => {
      this.sendNotification({
        title: '🌅 Good Morning!',
        body: 'Ready to make today amazing? Check your goals and tasks!',
        tag: 'morning-motivation'
      });
    });

    // Lunch break reminder (12 PM)
    this.scheduleDaily(12, 0, () => {
      this.sendMoodCheckIn();
    });

    // Evening reflection (8 PM)
    this.scheduleDaily(20, 0, () => {
      this.sendJournalReminder();
    });

    // Hydration reminders (every 2 hours during day)
    for (let hour = 9; hour <= 17; hour += 2) {
      this.scheduleDaily(hour, 0, () => {
        this.sendWellnessReminder('water');
      });
    }
  }

  private scheduleDaily(hour: number, minute: number, callback: () => void): void {
    const now = new Date();
    const scheduledTime = new Date();
    scheduledTime.setHours(hour, minute, 0, 0);

    // If the time has passed today, schedule for tomorrow
    if (scheduledTime <= now) {
      scheduledTime.setDate(scheduledTime.getDate() + 1);
    }

    const timeUntilExecution = scheduledTime.getTime() - now.getTime();

    setTimeout(() => {
      callback();
      // Schedule the next occurrence (24 hours later)
      setInterval(callback, 24 * 60 * 60 * 1000);
    }, timeUntilExecution);
  }

  // Clear all notifications with a specific tag
  clearNotifications(tag: string): void {
    // Note: This is limited by browser APIs
    console.log(`Clearing notifications with tag: ${tag}`);
  }

  // Get notification permission status
  getPermissionStatus(): NotificationPermission {
    return this.permission;
  }

  // Check if notifications are supported
  isNotificationSupported(): boolean {
    return this.isSupported;
  }
}

export default NotificationService.getInstance();
