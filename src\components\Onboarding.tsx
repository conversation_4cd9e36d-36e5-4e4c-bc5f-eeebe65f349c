
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronRight, Users, Star, Heart, ArrowLeft, Check, Camera, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const Onboarding = ({ onComplete }: { onComplete: (profile: any) => void }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [userName, setUserName] = useState('');
  const [userPhoto, setUserPhoto] = useState<string | null>(null);
  const [selectedGoal, setSelectedGoal] = useState('');
  const [selectedModules, setSelectedModules] = useState<string[]>([]);

  const goals = [
    { id: 'productivity', label: 'Boost Productivity', icon: Star, color: 'from-blue-500 to-purple-600' },
    { id: 'health', label: 'Improve Health', icon: Heart, color: 'from-pink-500 to-red-500' },
    { id: 'organization', label: 'Stay Organized', icon: Users, color: 'from-green-500 to-teal-500' },
  ];

  const modules = [
    { id: 'notes', label: 'Notes & Journal', desc: 'Capture thoughts and ideas', emoji: '📝' },
    { id: 'tasks', label: 'Task Manager', desc: 'Organize your to-dos', emoji: '✅' },
    { id: 'habits', label: 'Habit Tracker', desc: 'Build positive routines', emoji: '🎯' },
    { id: 'finance', label: 'Finance Tracker', desc: 'Monitor expenses', emoji: '💰' },
    { id: 'mood', label: 'Mood Tracker', desc: 'Track emotional wellbeing', emoji: '😊' },
    { id: 'routines', label: 'Routine Builder', desc: 'Create daily routines', emoji: '🔄' },
  ];

  const handleModuleToggle = (moduleId: string) => {
    setSelectedModules(prev => 
      prev.includes(moduleId) 
        ? prev.filter(id => id !== moduleId)
        : [...prev, moduleId]
    );
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setUserPhoto(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleComplete = () => {
    const profile = {
      name: userName,
      avatar: userPhoto,
      goal: selectedGoal,
      modules: selectedModules,
      setupDate: new Date().toISOString(),
    };
    onComplete(profile);
  };

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const pageVariants = {
    initial: { opacity: 0, x: 50, scale: 0.95 },
    animate: { opacity: 1, x: 0, scale: 1 },
    exit: { opacity: 0, x: -50, scale: 0.95 }
  };

  const containerVariants = {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 }
  };

  const steps = [
    // Step 0: Splash Screen
    <motion.div
      key="splash"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="flex flex-col items-center justify-center min-h-screen text-center px-4 py-8 sm:px-6 lg:px-8"
    >
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ type: "spring", duration: 1, delay: 0.2 }}
        className="relative mb-6 sm:mb-8"
      >
        <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 text-white rounded-3xl p-6 sm:p-8 shadow-2xl">
          <Star className="w-12 h-12 sm:w-16 sm:h-16" />
          <motion.div
            className="absolute -top-2 -right-2 w-5 h-5 sm:w-6 sm:h-6 bg-yellow-400 rounded-full"
            animate={{ scale: [1, 1.2, 1], rotate: [0, 180, 360] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </div>
      </motion.div>

      <motion.div
        variants={containerVariants}
        initial="initial"
        animate="animate"
        className="space-y-3 sm:space-y-4 max-w-md mx-auto"
      >
        <motion.h1
          variants={itemVariants}
          className="text-3xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent"
        >
          ✨ Spark Life Compass
        </motion.h1>
        <motion.p
          variants={itemVariants}
          className="text-lg sm:text-xl text-gray-600 font-medium px-4"
        >
          Your Personal Life Management Hub
        </motion.p>
        <motion.p
          variants={itemVariants}
          className="text-sm text-gray-500"
        >
          Premium billion-dollar UI experience
        </motion.p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1, type: "spring" }}
        className="mt-8 sm:mt-12 w-full max-w-xs mx-auto"
      >
        <Button
          onClick={handleNext}
          className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 text-base sm:text-lg rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
        >
          Get Started
          <motion.div
            animate={{ x: [0, 5, 0] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          >
            <ChevronRight className="ml-2 w-5 h-5" />
          </motion.div>
        </Button>
      </motion.div>
    </motion.div>,

    // Step 1: Welcome & Name + Photo
    <motion.div
      key="welcome"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="flex flex-col items-center justify-center min-h-screen text-center px-4 py-8 sm:px-6 lg:px-8"
    >
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", delay: 0.2 }}
        className="text-4xl sm:text-6xl mb-4 sm:mb-6"
      >
        👋
      </motion.div>

      <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 mb-3 sm:mb-4">Welcome!</h2>
      <p className="text-gray-600 mb-6 sm:mb-8 text-base sm:text-lg px-4">Let's personalize your experience</p>

      <div className="w-full max-w-sm mx-auto space-y-6">
        {/* Photo Upload */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex flex-col items-center space-y-3"
        >
          <div className="relative">
            {userPhoto ? (
              <img
                src={userPhoto}
                alt="Profile"
                className="w-20 h-20 sm:w-24 sm:h-24 rounded-full object-cover border-4 border-purple-200"
              />
            ) : (
              <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center border-4 border-purple-200">
                <User className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
              </div>
            )}
            <label className="absolute bottom-0 right-0 bg-purple-500 hover:bg-purple-600 text-white rounded-full p-2 cursor-pointer transition-colors shadow-lg">
              <Camera className="w-4 h-4" />
              <input
                type="file"
                accept="image/*"
                onChange={handlePhotoUpload}
                className="hidden"
              />
            </label>
          </div>
          <p className="text-xs sm:text-sm text-gray-500">Add your photo (optional)</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Input
            type="text"
            placeholder="What's your name?"
            value={userName}
            onChange={(e) => setUserName(e.target.value)}
            className="text-center text-lg sm:text-xl py-3 sm:py-4 rounded-2xl border-2 border-gray-200 focus:border-purple-500 bg-white/80 backdrop-blur-sm"
          />
        </motion.div>

        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 pt-2">
          <Button
            onClick={handleBack}
            variant="outline"
            className="w-full sm:flex-1 py-3 rounded-xl border-2 hover:border-purple-300"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <Button
            onClick={handleNext}
            disabled={!userName.trim()}
            className="w-full sm:flex-1 bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Continue
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </motion.div>,

    // Step 2: Goal Selection
    <motion.div
      key="goals"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="flex flex-col items-center justify-center min-h-screen text-center px-4 py-8 sm:px-6 lg:px-8"
    >
      <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 mb-3 sm:mb-4">What's your main goal?</h2>
      <p className="text-gray-600 mb-6 sm:mb-8 text-base sm:text-lg px-4">Choose what matters most to you</p>

      <motion.div
        variants={containerVariants}
        initial="initial"
        animate="animate"
        className="space-y-3 sm:space-y-4 w-full max-w-sm mx-auto mb-6 sm:mb-8"
      >
        {goals.map((goal, index) => (
          <motion.button
            key={goal.id}
            variants={itemVariants}
            onClick={() => setSelectedGoal(goal.id)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={`w-full p-4 sm:p-6 rounded-2xl border-2 transition-all flex items-center space-x-3 sm:space-x-4 ${
              selectedGoal === goal.id
                ? 'border-purple-500 bg-gradient-to-r from-purple-50 to-blue-50 shadow-lg'
                : 'border-gray-200 hover:border-purple-300 bg-white/80 backdrop-blur-sm hover:shadow-md'
            }`}
          >
            <div className={`p-2 sm:p-3 rounded-xl bg-gradient-to-r ${goal.color} flex-shrink-0`}>
              <goal.icon className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
            </div>
            <span className="text-base sm:text-lg font-medium flex-1 text-left">{goal.label}</span>
            {selectedGoal === goal.id && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="text-purple-600 flex-shrink-0"
              >
                <Check className="w-5 h-5 sm:w-6 sm:h-6" />
              </motion.div>
            )}
          </motion.button>
        ))}
      </motion.div>

      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full max-w-sm mx-auto">
        <Button
          onClick={handleBack}
          variant="outline"
          className="w-full sm:flex-1 py-3 rounded-xl border-2 hover:border-purple-300"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!selectedGoal}
          className="w-full sm:flex-1 bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-xl disabled:opacity-50"
        >
          Continue
          <ChevronRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
    </motion.div>,

    // Step 3: Module Selection
    <motion.div
      key="modules"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="flex flex-col items-center justify-center min-h-screen text-center px-4 py-8 sm:px-6 lg:px-8"
    >
      <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 mb-3 sm:mb-4">Choose your tools</h2>
      <p className="text-gray-600 mb-6 sm:mb-8 text-base sm:text-lg px-4">Select the modules you want to use</p>

      <motion.div
        variants={containerVariants}
        initial="initial"
        animate="animate"
        className="space-y-2 sm:space-y-3 w-full max-w-sm mx-auto mb-6 sm:mb-8 max-h-80 sm:max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-purple-300 scrollbar-track-gray-100"
      >
        {modules.map((module, index) => (
          <motion.button
            key={module.id}
            variants={itemVariants}
            onClick={() => handleModuleToggle(module.id)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={`w-full p-3 sm:p-4 rounded-2xl border-2 transition-all text-left ${
              selectedModules.includes(module.id)
                ? 'border-purple-500 bg-gradient-to-r from-purple-50 to-blue-50 shadow-lg'
                : 'border-gray-200 hover:border-purple-300 bg-white/80 backdrop-blur-sm hover:shadow-md'
            }`}
          >
            <div className="flex items-center space-x-3">
              <span className="text-xl sm:text-2xl flex-shrink-0">{module.emoji}</span>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm sm:text-base lg:text-lg truncate">{module.label}</div>
                <div className="text-xs sm:text-sm text-gray-600 line-clamp-2">{module.desc}</div>
              </div>
              {selectedModules.includes(module.id) && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="text-purple-600 flex-shrink-0"
                >
                  <Check className="w-4 h-4 sm:w-5 sm:h-5" />
                </motion.div>
              )}
            </div>
          </motion.button>
        ))}
      </motion.div>

      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full max-w-sm mx-auto">
        <Button
          onClick={handleBack}
          variant="outline"
          className="w-full sm:flex-1 py-3 rounded-xl border-2 hover:border-purple-300"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <Button
          onClick={handleComplete}
          disabled={selectedModules.length === 0}
          className="w-full sm:flex-1 bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-xl disabled:opacity-50"
        >
          <span className="hidden sm:inline">Start Using Spark Life</span>
          <span className="sm:hidden">Get Started</span>
          <ChevronRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
    </motion.div>
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 overflow-hidden">
      {/* Progress Indicator */}
      {currentStep > 0 && (
        <div className="fixed top-0 left-0 right-0 z-50 p-3 sm:p-4">
          <div className="max-w-sm mx-auto">
            <div className="flex space-x-1 sm:space-x-2">
              {[1, 2, 3].map((step) => (
                <motion.div
                  key={step}
                  className={`flex-1 h-1 sm:h-1.5 rounded-full ${
                    step <= currentStep ? 'bg-purple-600' : 'bg-gray-200'
                  }`}
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: step <= currentStep ? 1 : 0 }}
                  transition={{ duration: 0.3 }}
                />
              ))}
            </div>
            <div className="text-center mt-2">
              <span className="text-xs text-gray-500">
                Step {currentStep} of 3
              </span>
            </div>
          </div>
        </div>
      )}

      <AnimatePresence mode="wait">
        {steps[currentStep]}
      </AnimatePresence>
    </div>
  );
};

export default Onboarding;
