import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  Calendar, Heart, Clock, Target, CalendarDays, TrendingUp, CheckCircle, Circle, Play,
  Pause, User, Quote, Zap, Brain, Coffee, Moon, Sun, Edit3, MoreHorizontal, ChevronRight,
  Timer, BookOpen, Activity, Settings, Home, BarChart3, Mic, Camera, Sparkles,
  X, Check, ArrowRight, Bell, Search, Filter, Star, Award, Crown, Diamond, Flame
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import FeatureDemo from './FeatureDemo';

interface BillionDollarDashboardProps {
  userProfile?: any;
}

const BillionDollarDashboard = ({ userProfile }: BillionDollarDashboardProps) => {
  const navigate = useNavigate();

  // Show feature demo if requested
  const [showFeatureDemo, setShowFeatureDemo] = useState(false);

  // Core UI state
  const [currentTime, setCurrentTime] = useState(new Date());
  const [pomodoroActive, setPomodoroActive] = useState(false);
  const [pomodoroTime, setPomodoroTime] = useState(25 * 60);
  const [currentTask, setCurrentTask] = useState('Deep Work Session');

  const [isDarkMode, setIsDarkMode] = useState(false);
  const [activeTab, setActiveTab] = useState('home');

  // Additional tracking states starting from zero
  const [focusTimeToday, setFocusTimeToday] = useState(() => {
    const saved = localStorage.getItem('billionDollarFocusTime');
    return saved ? parseFloat(saved) : 0;
  });

  const [weeklyStreak, setWeeklyStreak] = useState(() => {
    const saved = localStorage.getItem('billionDollarWeeklyStreak');
    return saved ? parseInt(saved) : 0;
  });

  const [totalTasksCompleted, setTotalTasksCompleted] = useState(() => {
    const saved = localStorage.getItem('billionDollarTotalTasksCompleted');
    return saved ? parseInt(saved) : 0;
  });

  // Real-time functional data with localStorage persistence - Starting from zero
  const [todayTasks, setTodayTasks] = useState(() => {
    const saved = localStorage.getItem('billionDollarTasks');
    return saved ? JSON.parse(saved) : [];
  });

  const [todayMood, setTodayMood] = useState(() => {
    const saved = localStorage.getItem('billionDollarMood');
    return saved ? JSON.parse(saved) : { emoji: '😊', mood: 'Getting Started', energy: 50, note: 'Ready to begin!' };
  });

  const [habits, setHabits] = useState(() => {
    const saved = localStorage.getItem('billionDollarHabits');
    return saved ? JSON.parse(saved) : [
      { name: 'Morning Meditation', completed: false, streak: 0, category: 'mindfulness', icon: '🧘' },
      { name: 'Drink 8 Glasses Water', completed: false, streak: 0, category: 'health', icon: '💧' },
      { name: 'Read 30 Minutes', completed: false, streak: 0, category: 'learning', icon: '📚' },
      { name: 'Exercise', completed: false, streak: 0, category: 'fitness', icon: '💪' },
      { name: 'Gratitude Journal', completed: false, streak: 0, category: 'mindfulness', icon: '🙏' },
      { name: 'No Social Media', completed: false, streak: 0, category: 'focus', icon: '📱' }
    ];
  });

  const [goals, setGoals] = useState(() => {
    const saved = localStorage.getItem('billionDollarGoals');
    return saved ? JSON.parse(saved) : [
      { name: 'Launch Product V2', progress: 0, category: 'career', color: 'from-purple-500 to-pink-500' },
      { name: 'Run Marathon', progress: 0, category: 'health', color: 'from-green-500 to-emerald-500' },
      { name: 'Learn Spanish', progress: 0, category: 'learning', color: 'from-blue-500 to-cyan-500' },
      { name: 'Save $50K', progress: 0, category: 'finance', color: 'from-yellow-500 to-orange-500' }
    ];
  });

  const dailyQuotes = [
    "The future belongs to those who believe in the beauty of their dreams.",
    "Success is not final, failure is not fatal: it is the courage to continue that counts.",
    "Innovation distinguishes between a leader and a follower.",
    "Your limitation—it's only your imagination.",
    "Great things never come from comfort zones."
  ];

  const [dailyQuote] = useState(dailyQuotes[Math.floor(Math.random() * dailyQuotes.length)]);

  // Real-time data persistence
  useEffect(() => {
    localStorage.setItem('billionDollarTasks', JSON.stringify(todayTasks));
  }, [todayTasks]);

  useEffect(() => {
    localStorage.setItem('billionDollarMood', JSON.stringify(todayMood));
  }, [todayMood]);

  useEffect(() => {
    localStorage.setItem('billionDollarHabits', JSON.stringify(habits));
  }, [habits]);

  useEffect(() => {
    localStorage.setItem('billionDollarGoals', JSON.stringify(goals));
  }, [goals]);

  useEffect(() => {
    localStorage.setItem('billionDollarFocusTime', focusTimeToday.toString());
  }, [focusTimeToday]);

  useEffect(() => {
    localStorage.setItem('billionDollarWeeklyStreak', weeklyStreak.toString());
  }, [weeklyStreak]);

  useEffect(() => {
    localStorage.setItem('billionDollarTotalTasksCompleted', totalTasksCompleted.toString());
  }, [totalTasksCompleted]);

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000); // Update every second for real-time
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (pomodoroActive && pomodoroTime > 0) {
      interval = setInterval(() => {
        setPomodoroTime(time => {
          const newTime = time - 1;
          if (newTime === 0) {
            setPomodoroActive(false);
            // Add 25 minutes (0.42 hours) to focus time when session completes
            setFocusTimeToday(prev => Math.round((prev + 0.42) * 100) / 100);
            // Show notification when timer completes
            if ('Notification' in window && Notification.permission === 'granted') {
              new Notification('Pomodoro Complete!', {
                body: 'Great job! You earned 25 minutes of focus time.',
                icon: '/favicon.ico'
              });
            }
          }
          return newTime;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [pomodoroActive, pomodoroTime]);

  // Request notification permission
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  const completedTasks = todayTasks.filter(task => task.completed).length;
  const totalTasks = todayTasks.length;
  const taskProgress = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  const completedHabits = habits.filter(habit => habit.completed).length;
  const totalHabits = habits.length;
  const habitProgress = totalHabits > 0 ? (completedHabits / totalHabits) * 100 : 0;

  // Interactive functions
  const toggleTask = (taskId: number) => {
    setTodayTasks(tasks =>
      tasks.map(task => {
        if (task.id === taskId) {
          const newCompleted = !task.completed;
          // Track total completed tasks
          if (newCompleted && !task.completed) {
            setTotalTasksCompleted(prev => prev + 1);
            // Update weekly streak if this is the first task completed today
            const completedToday = tasks.filter(t => t.completed).length;
            if (completedToday === 0) {
              setWeeklyStreak(prev => prev + 1);
            }
          } else if (!newCompleted && task.completed) {
            setTotalTasksCompleted(prev => Math.max(0, prev - 1));
          }
          return { ...task, completed: newCompleted };
        }
        return task;
      })
    );
  };

  const toggleHabit = (habitName: string) => {
    setHabits(habits =>
      habits.map(habit => {
        if (habit.name === habitName) {
          const newCompleted = !habit.completed;
          let newStreak = habit.streak;

          if (newCompleted && !habit.completed) {
            // Completing a habit increases streak
            newStreak = habit.streak + 1;
          } else if (!newCompleted && habit.completed) {
            // Uncompleting a habit decreases streak but never below 0
            newStreak = Math.max(0, habit.streak - 1);
          }

          return {
            ...habit,
            completed: newCompleted,
            streak: newStreak
          };
        }
        return habit;
      })
    );
  };

  const addQuickTask = () => {
    const taskTitle = prompt('Enter a quick task:');
    if (taskTitle && taskTitle.trim()) {
      const newTask = {
        id: Date.now(),
        title: taskTitle.trim(),
        completed: false,
        priority: 'medium' as const,
        category: 'general',
        time: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })
      };
      setTodayTasks(tasks => [...tasks, newTask]);
    }
  };

  const updateMood = () => {
    const moods = [
      { emoji: '😊', mood: 'Happy', energy: 90 },
      { emoji: '🚀', mood: 'Energized', energy: 95 },
      { emoji: '😌', mood: 'Calm', energy: 70 },
      { emoji: '💪', mood: 'Motivated', energy: 85 },
      { emoji: '🎯', mood: 'Focused', energy: 80 },
      { emoji: '😴', mood: 'Tired', energy: 40 },
      { emoji: '😰', mood: 'Stressed', energy: 60 }
    ];

    const randomMood = moods[Math.floor(Math.random() * moods.length)];
    setTodayMood(randomMood);
  };

  const resetPomodoro = () => {
    setPomodoroTime(25 * 60);
    setPomodoroActive(false);
  };

  const addNewGoal = () => {
    const goalName = prompt('Enter a new goal:');
    if (goalName && goalName.trim()) {
      const categories = ['career', 'health', 'learning', 'finance', 'personal'];
      const colors = [
        'from-purple-500 to-pink-500',
        'from-green-500 to-emerald-500',
        'from-blue-500 to-cyan-500',
        'from-yellow-500 to-orange-500',
        'from-indigo-500 to-purple-500'
      ];

      const newGoal = {
        name: goalName.trim(),
        progress: 0,
        category: categories[Math.floor(Math.random() * categories.length)],
        color: colors[Math.floor(Math.random() * colors.length)]
      };
      setGoals(goals => [...goals, newGoal]);
    }
  };

  const addNewHabit = () => {
    const habitName = prompt('Enter a new habit:');
    if (habitName && habitName.trim()) {
      const categories = ['mindfulness', 'health', 'learning', 'fitness', 'focus', 'productivity'];
      const icons = ['🧘', '💧', '📚', '💪', '🙏', '📱', '🌱', '⚡', '🎯', '✨'];

      const newHabit = {
        name: habitName.trim(),
        completed: false,
        streak: 0,
        category: categories[Math.floor(Math.random() * categories.length)],
        icon: icons[Math.floor(Math.random() * icons.length)]
      };
      setHabits(habits => [...habits, newHabit]);
    }
  };

  const clearAllData = () => {
    if (confirm('Are you sure you want to reset all data? This cannot be undone.')) {
      // Clear localStorage
      localStorage.removeItem('billionDollarTasks');
      localStorage.removeItem('billionDollarMood');
      localStorage.removeItem('billionDollarHabits');
      localStorage.removeItem('billionDollarGoals');
      localStorage.removeItem('billionDollarFocusTime');
      localStorage.removeItem('billionDollarWeeklyStreak');
      localStorage.removeItem('billionDollarTotalTasksCompleted');

      // Reset all state
      setTodayTasks([]);
      setTodayMood({ emoji: '😊', mood: 'Getting Started', energy: 50, note: 'Ready to begin!' });
      setHabits([
        { name: 'Morning Meditation', completed: false, streak: 0, category: 'mindfulness', icon: '🧘' },
        { name: 'Drink 8 Glasses Water', completed: false, streak: 0, category: 'health', icon: '💧' },
        { name: 'Read 30 Minutes', completed: false, streak: 0, category: 'learning', icon: '📚' },
        { name: 'Exercise', completed: false, streak: 0, category: 'fitness', icon: '💪' },
        { name: 'Gratitude Journal', completed: false, streak: 0, category: 'mindfulness', icon: '🙏' },
        { name: 'No Social Media', completed: false, streak: 0, category: 'focus', icon: '📱' }
      ]);
      setGoals([
        { name: 'Launch Product V2', progress: 0, category: 'career', color: 'from-purple-500 to-pink-500' },
        { name: 'Run Marathon', progress: 0, category: 'health', color: 'from-green-500 to-emerald-500' },
        { name: 'Learn Spanish', progress: 0, category: 'learning', color: 'from-blue-500 to-cyan-500' },
        { name: 'Save $50K', progress: 0, category: 'finance', color: 'from-yellow-500 to-orange-500' }
      ]);
      setFocusTimeToday(0);
      setWeeklyStreak(0);
      setTotalTasksCompleted(0);
      setPomodoroTime(25 * 60);
      setPomodoroActive(false);
    }
  };

  // Early return for feature demo after all hooks are declared
  if (showFeatureDemo) {
    return <FeatureDemo onBack={() => setShowFeatureDemo(false)} />;
  }

  return (
    <div className={`min-h-screen transition-all duration-700 ${
      isDarkMode
        ? 'bg-gradient-to-br from-slate-900 via-purple-900/50 to-slate-900'
        : 'bg-gradient-to-br from-indigo-50 via-white to-purple-50'
    }`}>
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-2000" />
      </div>

      {/* Main Content */}
      <div className="relative z-10 pb-24">
        {/* Premium Header - Mobile Optimized */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="sticky top-0 z-50 backdrop-blur-2xl bg-white/10 border-b border-white/20 shadow-xl"
        >
          <div className="flex items-center justify-between p-4 sm:p-6">
            {/* User Info */}
            <div className="flex items-center space-x-3 sm:space-x-4 flex-1 min-w-0">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="relative flex-shrink-0"
              >
                <div className="w-12 h-12 sm:w-14 sm:h-14 rounded-3xl bg-gradient-to-br from-purple-500 via-pink-500 to-blue-500 flex items-center justify-center shadow-xl">
                  <span className="text-xl sm:text-2xl">{userProfile?.avatar || '🚀'}</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-green-500 rounded-full border-2 border-white shadow-lg" />
              </motion.div>
              <div className="min-w-0 flex-1">
                <motion.h1
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="text-lg sm:text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent dark:from-white dark:to-gray-300 truncate"
                >
                  {getGreeting()}, {userProfile?.name || 'Champion'}!
                </motion.h1>
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 }}
                  className="text-xs sm:text-sm text-gray-600 dark:text-gray-400"
                >
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="truncate">{currentTime.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })}</span>
                    <span className="text-lg font-mono">{currentTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Flame className="w-3 h-3 text-orange-500 flex-shrink-0" />
                    <span className="text-orange-600 font-medium text-xs">{weeklyStreak} day streak</span>
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Header Actions - Mobile Optimized */}
            <div className="flex items-center space-x-2 flex-shrink-0">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsDarkMode(!isDarkMode)}
                className="rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 w-10 h-10 p-0"
              >
                {isDarkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllData}
                className="rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 w-10 h-10 p-0"
                title="Reset All Data"
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Daily Quote Card - Mobile Optimized */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mx-4 sm:mx-6 mt-4 sm:mt-6 p-4 sm:p-6 rounded-3xl bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 backdrop-blur-xl border border-white/20 shadow-xl"
        >
          <div className="flex items-start space-x-3 sm:space-x-4">
            <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-2xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg flex-shrink-0">
              <Quote className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-gray-800 dark:text-gray-200 font-medium leading-relaxed mb-2 text-sm sm:text-base">
                "{dailyQuote}"
              </p>
              <div className="flex items-center justify-between">
                <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">Daily Inspiration</p>
                <Button
                  onClick={() => setShowFeatureDemo(true)}
                  variant="ghost"
                  size="sm"
                  className="text-xs px-3 py-1 rounded-xl bg-white/20 hover:bg-white/30 text-purple-600 font-medium"
                >
                  🎯 Test Features
                </Button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Premium Stats Grid - Mobile Optimized */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-2 gap-3 sm:gap-4 p-4 sm:p-6"
        >
          {/* Tasks Progress - Mobile Optimized */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => navigate('/tasks')}
            className="rounded-3xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 backdrop-blur-xl border border-white/30 p-4 sm:p-6 shadow-xl cursor-pointer"
          >
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-2xl bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
                <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <div className="text-right">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">{completedTasks}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">of {totalTasks}</div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">Tasks Done</span>
                <span className="text-xs sm:text-sm font-bold text-blue-600">{Math.round(taskProgress)}%</span>
              </div>
              <div className="bg-white/30 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${taskProgress}%` }}
                  transition={{ duration: 1, delay: 0.5 }}
                />
              </div>
            </div>
          </motion.div>

          {/* Mood & Energy - Interactive */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={updateMood}
            className="rounded-3xl bg-gradient-to-br from-pink-500/20 to-rose-500/20 backdrop-blur-xl border border-white/30 p-4 sm:p-6 shadow-xl cursor-pointer"
          >
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-2xl bg-gradient-to-br from-pink-500 to-rose-500 flex items-center justify-center shadow-lg">
                <Heart className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <motion.div
                key={todayMood.emoji}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="text-3xl sm:text-4xl"
              >
                {todayMood.emoji}
              </motion.div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">Energy Level</span>
                <span className="text-xs sm:text-sm font-bold text-pink-600">{todayMood.energy}%</span>
              </div>
              <div className="bg-white/30 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-pink-500 to-rose-500 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${todayMood.energy}%` }}
                  transition={{ duration: 1, delay: 0.7 }}
                />
              </div>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-2">{todayMood.mood}</p>
            </div>
          </motion.div>

          {/* Focus Time - Interactive */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setPomodoroActive(!pomodoroActive)}
            className="rounded-3xl bg-gradient-to-br from-purple-500/20 to-indigo-500/20 backdrop-blur-xl border border-white/30 p-4 sm:p-6 shadow-xl cursor-pointer"
          >
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-2xl bg-gradient-to-br from-purple-500 to-indigo-500 flex items-center justify-center shadow-lg">
                <Timer className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <div className="text-right">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">{focusTimeToday}h</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">today</div>
              </div>
            </div>
            <div className="space-y-2">
              <span className="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">Deep Focus</span>
              <div className="flex items-center space-x-2">
                <div className="flex-1 bg-white/30 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-purple-500 to-indigo-500 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${(focusTimeToday / 8) * 100}%` }}
                    transition={{ duration: 1, delay: 0.8 }}
                  />
                </div>
                <span className="text-xs text-purple-600 font-medium">8h goal</span>
              </div>
            </div>
          </motion.div>

          {/* Habits Streak - Interactive */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => navigate('/habits')}
            className="rounded-3xl bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-xl border border-white/30 p-4 sm:p-6 shadow-xl cursor-pointer"
          >
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-2xl bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center shadow-lg">
                <Zap className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <div className="text-right">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">{completedHabits}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">of {totalHabits}</div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300">Habits Done</span>
                <span className="text-xs sm:text-sm font-bold text-green-600">{Math.round(habitProgress)}%</span>
              </div>
              <div className="bg-white/30 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${habitProgress}%` }}
                  transition={{ duration: 1, delay: 0.9 }}
                />
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Premium Pomodoro Focus Block - Mobile Optimized */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mx-4 sm:mx-6 mb-4 sm:mb-6"
        >
          <div className="rounded-3xl bg-gradient-to-br from-orange-500/20 to-red-500/20 backdrop-blur-xl border border-white/30 p-6 sm:p-8 shadow-xl">
            <div className="flex items-center justify-between mb-4 sm:mb-6">
              <div className="flex-1 min-w-0">
                <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-1 truncate">Focus Session</h3>
                <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 truncate">{currentTask}</p>
              </div>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setPomodoroActive(!pomodoroActive)}
                className={`w-14 h-14 sm:w-16 sm:h-16 rounded-3xl shadow-xl flex items-center justify-center flex-shrink-0 ml-4 ${
                  pomodoroActive
                    ? 'bg-gradient-to-br from-red-500 to-pink-500'
                    : 'bg-gradient-to-br from-green-500 to-emerald-500'
                }`}
              >
                {pomodoroActive ? <Pause className="w-6 h-6 sm:w-8 sm:h-8 text-white" /> : <Play className="w-6 h-6 sm:w-8 sm:h-8 text-white" />}
              </motion.button>
            </div>

            <div className="text-center">
              <motion.div
                key={pomodoroTime}
                initial={{ scale: 1.1 }}
                animate={{ scale: 1 }}
                className="text-4xl sm:text-6xl font-bold text-gray-900 dark:text-white mb-4 font-mono"
              >
                {formatTime(pomodoroTime)}
              </motion.div>

              <div className="bg-white/30 rounded-full h-3 sm:h-4 mb-4 overflow-hidden">
                <motion.div
                  className="bg-gradient-to-r from-orange-500 to-red-500 h-3 sm:h-4 rounded-full"
                  style={{ width: `${((25 * 60 - pomodoroTime) / (25 * 60)) * 100}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>

              <div className="flex items-center justify-center space-x-4 mb-4">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={resetPomodoro}
                  className="px-4 py-2 bg-white/20 backdrop-blur-sm rounded-2xl text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-white/30 transition-all"
                >
                  Reset
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setCurrentTask(prompt('Enter task name:') || currentTask)}
                  className="px-4 py-2 bg-white/20 backdrop-blur-sm rounded-2xl text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-white/30 transition-all"
                >
                  Change Task
                </motion.button>
              </div>

              <p className="text-sm text-gray-600 dark:text-gray-400">
                {pomodoroActive ? '🔥 Deep focus mode active' : '⚡ Ready to focus'}
              </p>
            </div>
          </div>
        </motion.div>

        {/* Today's Tasks - Interactive & Mobile Optimized */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mx-4 sm:mx-6 mb-4 sm:mb-6"
        >
          <div className="rounded-3xl bg-white/20 backdrop-blur-xl border border-white/30 p-4 sm:p-6 shadow-xl">
            <div className="flex items-center justify-between mb-4 sm:mb-6">
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">Today's Mission</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/tasks')}
                className="rounded-2xl text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/30 w-8 h-8 p-0"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-3">
              {todayTasks.slice(0, 5).map((task, index) => (
                <motion.div
                  key={task.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 * index }}
                  whileHover={{ scale: 1.01 }}
                  className="flex items-center space-x-3 sm:space-x-4 p-3 sm:p-4 rounded-2xl bg-white/30 backdrop-blur-sm border border-white/20 shadow-lg"
                >
                  <motion.button
                    whileTap={{ scale: 0.9 }}
                    onClick={() => toggleTask(task.id)}
                    className="flex-shrink-0"
                  >
                    {task.completed ? (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center shadow-lg"
                      >
                        <Check className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                      </motion.div>
                    ) : (
                      <div className="w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 border-gray-300 dark:border-gray-600 hover:border-green-400 transition-colors" />
                    )}
                  </motion.button>

                  <div className="flex-1 min-w-0">
                    <p className={`text-sm sm:text-base font-medium transition-all ${
                      task.completed
                        ? 'text-gray-500 line-through'
                        : 'text-gray-900 dark:text-white'
                    }`}>
                      {task.title}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                        task.priority === 'high'
                          ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
                          : task.priority === 'medium'
                          ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
                          : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'
                      }`}>
                        {task.priority}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">{task.time}</span>
                    </div>
                  </div>

                  <div className={`w-3 h-3 rounded-full flex-shrink-0 ${
                    task.category === 'work' ? 'bg-blue-500' :
                    task.category === 'health' ? 'bg-green-500' :
                    task.category === 'learning' ? 'bg-purple-500' : 'bg-gray-500'
                  }`} />
                </motion.div>
              ))}

              {todayTasks.length === 0 && (
                <div className="text-center py-8">
                  <div className="text-4xl mb-2">🎯</div>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">No tasks yet today</p>
                  <Button
                    onClick={addQuickTask}
                    className="bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-2xl px-6 py-2"
                  >
                    Add Your First Task
                  </Button>
                </div>
              )}
            </div>
          </div>
        </motion.div>

        {/* Habits Grid - Interactive & Mobile Optimized */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mx-4 sm:mx-6 mb-4 sm:mb-6"
        >
          <div className="rounded-3xl bg-white/20 backdrop-blur-xl border border-white/30 p-4 sm:p-6 shadow-xl">
            <div className="flex items-center justify-between mb-4 sm:mb-6">
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">Power Habits</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/habits')}
                className="rounded-2xl text-green-600 hover:bg-green-100 dark:hover:bg-green-900/30 w-8 h-8 p-0"
              >
                <Sparkles className="w-4 h-4" />
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-3">
              {habits.map((habit, index) => (
                <motion.div
                  key={habit.name}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.1 * index }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => toggleHabit(habit.name)}
                  className="p-3 sm:p-4 rounded-2xl bg-white/30 backdrop-blur-sm border border-white/20 cursor-pointer hover:bg-white/40 transition-all shadow-lg"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="text-xl sm:text-2xl">{habit.icon}</div>
                    <motion.div
                      className={`w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 flex items-center justify-center transition-all ${
                        habit.completed
                          ? 'bg-gradient-to-br from-green-500 to-emerald-500 border-green-500'
                          : 'border-gray-300 dark:border-gray-600 hover:border-green-400'
                      }`}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      {habit.completed && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                        >
                          <Check className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                        </motion.div>
                      )}
                    </motion.div>
                  </div>

                  <h4 className="font-semibold text-gray-900 dark:text-white text-xs sm:text-sm mb-2 leading-tight">
                    {habit.name}
                  </h4>

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-600 dark:text-gray-400 capitalize">
                      {habit.category}
                    </span>
                    <motion.div
                      className="flex items-center space-x-1"
                      whileHover={{ scale: 1.05 }}
                    >
                      <span className="text-xs font-bold text-orange-600">{habit.streak}</span>
                      <Flame className="w-3 h-3 text-orange-500" />
                    </motion.div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Habit Progress Summary */}
            <div className="mt-4 p-3 rounded-2xl bg-white/20 backdrop-blur-sm">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Today's Progress</span>
                <span className="text-sm font-bold text-green-600">{completedHabits}/{totalHabits}</span>
              </div>
              <div className="bg-white/30 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${habitProgress}%` }}
                  transition={{ duration: 1, delay: 0.5 }}
                />
              </div>
            </div>
          </div>
        </motion.div>

        {/* Goals Progress - Interactive & Mobile Optimized */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mx-4 sm:mx-6 mb-20 sm:mb-24"
        >
          <div className="rounded-3xl bg-gradient-to-br from-emerald-500/20 to-teal-500/20 backdrop-blur-xl border border-white/30 p-4 sm:p-6 shadow-xl">
            <div className="flex items-center justify-between mb-4 sm:mb-6">
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">Goal Progress</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/goals')}
                className="rounded-2xl text-emerald-600 hover:bg-emerald-100 dark:hover:bg-emerald-900/30 w-8 h-8 p-0"
              >
                <Target className="w-4 h-4" />
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-3 sm:gap-4">
              {goals.map((goal, index) => (
                <motion.div
                  key={goal.name}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.1 * index }}
                  whileHover={{ scale: 1.02 }}
                  className="text-center p-3 rounded-2xl bg-white/20 backdrop-blur-sm hover:bg-white/30 transition-all cursor-pointer"
                  onClick={() => {
                    const newProgress = prompt(`Update progress for ${goal.name} (0-100):`, goal.progress.toString());
                    if (newProgress && !isNaN(Number(newProgress))) {
                      const progress = Math.max(0, Math.min(100, Number(newProgress)));
                      setGoals(goals => goals.map(g => g.name === goal.name ? { ...g, progress } : g));
                    }
                  }}
                >
                  <div className="relative w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-3">
                    <svg className="w-16 h-16 sm:w-20 sm:h-20 transform -rotate-90" viewBox="0 0 80 80">
                      <circle
                        cx="40"
                        cy="40"
                        r="30"
                        stroke="currentColor"
                        strokeWidth="4"
                        fill="none"
                        className="text-white/20"
                      />
                      <motion.circle
                        cx="40"
                        cy="40"
                        r="30"
                        stroke="url(#gradient)"
                        strokeWidth="4"
                        fill="none"
                        strokeLinecap="round"
                        strokeDasharray={`${2 * Math.PI * 30}`}
                        initial={{ strokeDashoffset: 2 * Math.PI * 30 }}
                        animate={{ strokeDashoffset: 2 * Math.PI * 30 * (1 - goal.progress / 100) }}
                        transition={{ duration: 1.5, delay: 0.5 + index * 0.2 }}
                      />
                      <defs>
                        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#10b981" />
                          <stop offset="100%" stopColor="#14b8a6" />
                        </linearGradient>
                      </defs>
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-xs sm:text-sm font-bold text-gray-900 dark:text-white">
                        {goal.progress}%
                      </span>
                    </div>
                  </div>
                  <h4 className="font-semibold text-gray-900 dark:text-white text-xs sm:text-sm mb-1 leading-tight">
                    {goal.name}
                  </h4>
                  <p className="text-xs text-gray-600 dark:text-gray-400 capitalize">
                    {goal.category}
                  </p>
                </motion.div>
              ))}
            </div>

            {/* Overall Progress */}
            <div className="mt-4 p-3 rounded-2xl bg-white/20 backdrop-blur-sm">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Overall Progress</span>
                <span className="text-sm font-bold text-emerald-600">
                  {Math.round(goals.reduce((acc, goal) => acc + goal.progress, 0) / goals.length)}%
                </span>
              </div>
              <div className="bg-white/30 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-emerald-500 to-teal-500 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${goals.reduce((acc, goal) => acc + goal.progress, 0) / goals.length}%` }}
                  transition={{ duration: 1, delay: 1 }}
                />
              </div>
            </div>
          </div>
        </motion.div>

        {/* Professional Tools Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl p-4 sm:p-6 mx-4 mb-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold text-gray-800 dark:text-white">
              🚀 Professional Tools
            </h3>
            <Button
              onClick={() => navigate('/enhanced-kanban')}
              variant="ghost"
              size="sm"
              className="text-xs px-3 py-1 rounded-xl bg-white/20 hover:bg-white/30 text-purple-600 font-medium"
            >
              View All
            </Button>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
            {[
              {
                name: 'Enhanced Kanban',
                icon: '📋',
                description: 'Professional project management',
                path: '/enhanced-kanban',
                color: 'from-blue-500 to-cyan-500'
              },
              {
                name: 'Advanced Goals',
                icon: '🎯',
                description: 'OKRs & SMART goals',
                path: '/advanced-goals',
                color: 'from-purple-500 to-pink-500'
              },
              {
                name: 'Productivity Analytics',
                icon: '📊',
                description: 'Performance insights',
                path: '/productivity-dashboard',
                color: 'from-emerald-500 to-teal-500'
              },
              {
                name: 'Automation Engine',
                icon: '🤖',
                description: 'Smart workflows',
                path: '/automation',
                color: 'from-orange-500 to-red-500'
              }
            ].map((tool, index) => (
              <motion.div
                key={tool.name}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.7 + index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate(tool.path)}
                className="bg-white/20 backdrop-blur-sm rounded-2xl p-3 cursor-pointer hover:bg-white/30 transition-all border border-white/10"
              >
                <div className={`w-10 h-10 rounded-xl bg-gradient-to-r ${tool.color} flex items-center justify-center mb-2`}>
                  <span className="text-lg">{tool.icon}</span>
                </div>
                <h4 className="font-semibold text-sm text-gray-800 dark:text-white mb-1">
                  {tool.name}
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-300">
                  {tool.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Premium Bottom Navigation - Mobile Optimized */}
      <motion.div
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="fixed bottom-0 left-0 right-0 z-40 backdrop-blur-2xl bg-white/10 border-t border-white/20 shadow-2xl safe-area-inset-bottom"
      >
        <div className="flex items-center justify-around py-2 sm:py-4 px-2 sm:px-6">
          {[
            { icon: Home, label: 'Home', id: 'home', path: '/dashboard' },
            { icon: CheckCircle, label: 'Tasks', id: 'tasks', path: '/tasks' },
            { icon: BarChart3, label: 'Analytics', id: 'analytics', path: '/analytics' },
            { icon: Heart, label: 'Wellness', id: 'wellness', path: '/wellness' },
            { icon: Settings, label: 'Settings', id: 'settings', path: '/settings' }
          ].map((tab) => (
            <motion.button
              key={tab.id}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                setActiveTab(tab.id);
                navigate(tab.path);
              }}
              className={`flex flex-col items-center space-y-1 p-2 sm:p-3 rounded-2xl transition-all min-w-0 flex-1 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-600 dark:text-purple-400'
                  : 'text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              <tab.icon className={`w-5 h-5 sm:w-6 sm:h-6 ${activeTab === tab.id ? 'text-purple-600 dark:text-purple-400' : ''}`} />
              <span className="text-xs font-medium truncate">{tab.label}</span>
              {activeTab === tab.id && (
                <motion.div
                  layoutId="activeTab"
                  className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-purple-600 dark:bg-purple-400 rounded-full"
                />
              )}
            </motion.button>
          ))}
        </div>
        {/* Safe area for iOS devices */}
        <div className="h-safe-area-inset-bottom bg-white/5" />
      </motion.div>
    </div>
  );
};

export default BillionDollarDashboard;
