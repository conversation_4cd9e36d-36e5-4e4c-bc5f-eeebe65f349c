import { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Smartphone, Monitor, Palette, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import PremiumDashboard from './PremiumDashboard';

const PremiumDashboardDemo = () => {
  const [showDemo, setShowDemo] = useState(false);
  const [deviceView, setDeviceView] = useState<'mobile' | 'desktop'>('mobile');

  const features = [
    {
      icon: Sparkles,
      title: 'Glassmorphism Design',
      description: 'Beautiful frosted glass effects with backdrop blur and transparency',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Palette,
      title: 'Premium Color Palette',
      description: 'Soft emotional colors: Frost Blue, Lavender Fog, Blush Pink, Midnight Indigo',
      color: 'from-blue-500 to-indigo-500'
    },
    {
      icon: Smartphone,
      title: 'Mobile-First Design',
      description: 'Optimized for mobile with responsive grid layout and touch interactions',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Monitor,
      title: 'Cross-Platform',
      description: 'Seamless experience across mobile, tablet, and desktop devices',
      color: 'from-orange-500 to-red-500'
    }
  ];

  const mockUserProfile = {
    name: 'Alex Chen',
    avatar: '👨‍💻',
    goals: ['productivity', 'health', 'learning'],
    workStyle: 'creative-bursts',
    motto: 'Progress over perfection'
  };

  if (showDemo) {
    return (
      <div className="relative">
        {/* Demo Controls */}
        <div className="fixed top-4 left-4 z-50 flex items-center space-x-2">
          <Button
            onClick={() => setShowDemo(false)}
            variant="outline"
            size="sm"
            className="rounded-2xl bg-white/90 backdrop-blur-sm border border-white/30"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Overview
          </Button>
          <div className="flex items-center space-x-1 bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-1">
            <Button
              onClick={() => setDeviceView('mobile')}
              variant={deviceView === 'mobile' ? 'default' : 'ghost'}
              size="sm"
              className="rounded-xl"
            >
              <Smartphone className="w-4 h-4" />
            </Button>
            <Button
              onClick={() => setDeviceView('desktop')}
              variant={deviceView === 'desktop' ? 'default' : 'ghost'}
              size="sm"
              className="rounded-xl"
            >
              <Monitor className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Device Frame */}
        <div className="min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center p-4">
          <div className={`${
            deviceView === 'mobile' 
              ? 'w-full max-w-sm mx-auto' 
              : 'w-full max-w-6xl mx-auto'
          } transition-all duration-500`}>
            {deviceView === 'mobile' && (
              <div className="bg-black rounded-[3rem] p-2 shadow-2xl">
                <div className="bg-white rounded-[2.5rem] overflow-hidden">
                  <div className="h-6 bg-black rounded-t-[2.5rem] flex items-center justify-center">
                    <div className="w-16 h-1 bg-gray-800 rounded-full"></div>
                  </div>
                  <div className="h-[700px] overflow-hidden">
                    <PremiumDashboard userProfile={mockUserProfile} />
                  </div>
                </div>
              </div>
            )}
            
            {deviceView === 'desktop' && (
              <div className="bg-gray-800 rounded-2xl p-4 shadow-2xl">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
                <div className="bg-white rounded-xl overflow-hidden h-[800px]">
                  <PremiumDashboard userProfile={mockUserProfile} />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 p-4">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-12"
      >
        <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 bg-clip-text text-transparent mb-4">
          Premium Dashboard
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
          Experience the future of productivity with our glassmorphism-inspired, mobile-first dashboard design
        </p>
        <Button
          onClick={() => setShowDemo(true)}
          className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 rounded-2xl text-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <Sparkles className="w-5 h-5 mr-2" />
          View Live Demo
        </Button>
      </motion.div>

      {/* Features Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto mb-12"
      >
        {features.map((feature, index) => (
          <motion.div
            key={feature.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 * index }}
            whileHover={{ scale: 1.02 }}
            className="group"
          >
            <Card className="h-full border-0 shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
              <CardHeader>
                <div className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-800">
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {/* Color Palette Showcase */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="max-w-4xl mx-auto"
      >
        <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-gray-800 text-center">
              Premium Color Palette
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {[
                { name: 'Frost Blue', code: '#DDF2FD', usage: 'Backgrounds, glass blur' },
                { name: 'Lavender Fog', code: '#E5D9F2', usage: 'Highlights, mood journal' },
                { name: 'Blush Pink', code: '#F8C8DC', usage: 'Button gradients, wellness' },
                { name: 'Midnight Indigo', code: '#1D1A39', usage: 'Dark mode primary' },
                { name: 'Soft Gold', code: '#F4E869', usage: 'XP, progress, success badge' },
                { name: 'Cloud Gray', code: '#F1F2F6', usage: 'Light mode neutral' }
              ].map((color) => (
                <div key={color.name} className="text-center">
                  <div 
                    className="w-full h-20 rounded-2xl mb-3 shadow-lg"
                    style={{ backgroundColor: color.code }}
                  />
                  <h4 className="font-semibold text-gray-800">{color.name}</h4>
                  <p className="text-sm text-gray-500 font-mono">{color.code}</p>
                  <p className="text-xs text-gray-400 mt-1">{color.usage}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Component Features */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="max-w-4xl mx-auto mt-12"
      >
        <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-gray-800 text-center">
              Dashboard Components
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-800">Core Features</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Top nav with date, avatar, and daily quote</li>
                  <li>• Quick stats grid (Tasks, Mood, Focus Time, Habits)</li>
                  <li>• Pomodoro Focus block with timer</li>
                  <li>• Daily task list with swipe actions</li>
                  <li>• Routine tracker with AM/PM progress</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-800">Interactive Elements</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Mood & Journal quick entry area</li>
                  <li>• Goal progress ring with animations</li>
                  <li>• Floating Action Button with radial expand</li>
                  <li>• iOS-style bottom tab bar</li>
                  <li>• Dark/Light mode toggle</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default PremiumDashboardDemo;
