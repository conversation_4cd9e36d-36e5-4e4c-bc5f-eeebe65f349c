
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, Pause, Square, Clock, BarChart3, Calendar, Plus, Edit2, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

interface TimeEntry {
  id: string;
  projectName: string;
  taskName: string;
  startTime: string;
  endTime?: string;
  duration: number;
  isRunning: boolean;
  date: string;
  category: string;
}

const TimeTracker = () => {
  const [entries, setEntries] = useState<TimeEntry[]>([]);
  const [currentEntry, setCurrentEntry] = useState<TimeEntry | null>(null);
  const [newProject, setNewProject] = useState('');
  const [newTask, setNewTask] = useState('');
  const [newCategory, setNewCategory] = useState('');
  const [timer, setTimer] = useState(0);

  useEffect(() => {
    const savedEntries = localStorage.getItem('timeEntries');
    if (savedEntries) {
      const parsed = JSON.parse(savedEntries);
      setEntries(parsed);
      
      // Check if there's a running timer
      const running = parsed.find((entry: TimeEntry) => entry.isRunning);
      if (running) {
        setCurrentEntry(running);
        const elapsed = Math.floor((Date.now() - new Date(running.startTime).getTime()) / 1000);
        setTimer(elapsed);
      }
    }
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (currentEntry?.isRunning) {
      interval = setInterval(() => {
        setTimer(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [currentEntry?.isRunning]);

  const saveEntries = (updatedEntries: TimeEntry[]) => {
    setEntries(updatedEntries);
    localStorage.setItem('timeEntries', JSON.stringify(updatedEntries));
  };

  const startTimer = () => {
    if (!newProject.trim() || !newTask.trim()) return;

    const entry: TimeEntry = {
      id: Date.now().toString(),
      projectName: newProject,
      taskName: newTask,
      startTime: new Date().toISOString(),
      duration: 0,
      isRunning: true,
      date: new Date().toDateString(),
      category: newCategory || 'General'
    };

    setCurrentEntry(entry);
    setTimer(0);
    const updatedEntries = [...entries, entry];
    saveEntries(updatedEntries);
  };

  const stopTimer = () => {
    if (!currentEntry) return;

    const endTime = new Date().toISOString();
    const duration = Math.floor((new Date(endTime).getTime() - new Date(currentEntry.startTime).getTime()) / 1000);

    const updatedEntry = {
      ...currentEntry,
      endTime,
      duration,
      isRunning: false
    };

    const updatedEntries = entries.map(entry =>
      entry.id === currentEntry.id ? updatedEntry : entry
    );

    saveEntries(updatedEntries);
    setCurrentEntry(null);
    setTimer(0);
    setNewProject('');
    setNewTask('');
    setNewCategory('');
  };

  const deleteEntry = (id: string) => {
    const updatedEntries = entries.filter(entry => entry.id !== id);
    saveEntries(updatedEntries);
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTotalTimeToday = () => {
    const today = new Date().toDateString();
    return entries
      .filter(entry => entry.date === today && !entry.isRunning)
      .reduce((total, entry) => total + entry.duration, 0);
  };

  const getTotalTimeThisWeek = () => {
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    return entries
      .filter(entry => new Date(entry.date) >= weekStart && !entry.isRunning)
      .reduce((total, entry) => total + entry.duration, 0);
  };

  const containerVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-4 pb-32">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-4 sm:mb-6"
      >
        <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-indigo-600 to-cyan-600 bg-clip-text text-transparent">
          Time Tracker ⏱️
        </h1>
        <p className="text-sm sm:text-base text-gray-600">Track your time and boost productivity</p>
      </motion.div>

      {/* Stats Cards - Mobile Responsive */}
      <motion.div
        variants={containerVariants}
        initial="initial"
        animate="animate"
        className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 mb-4 sm:mb-6"
      >
        <motion.div variants={itemVariants}>
          <Card className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Today</p>
                  <p className="text-xl font-bold">{formatTime(getTotalTimeToday())}</p>
                </div>
                <Clock className="w-8 h-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={itemVariants}>
          <Card className="bg-gradient-to-r from-cyan-500 to-teal-600 text-white border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-cyan-100">This Week</p>
                  <p className="text-xl font-bold">{formatTime(getTotalTimeThisWeek())}</p>
                </div>
                <Calendar className="w-8 h-8 text-cyan-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={itemVariants}>
          <Card className="bg-gradient-to-r from-purple-500 to-pink-600 text-white border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Total Sessions</p>
                  <p className="text-xl font-bold">{entries.length}</p>
                </div>
                <BarChart3 className="w-8 h-8 text-purple-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>

      {/* Timer Interface */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="mb-6"
      >
        <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm rounded-2xl">
          <CardContent className="p-4 sm:p-6">
            <div className="text-center mb-4 sm:mb-6">
              <div className="text-4xl sm:text-6xl font-mono font-bold bg-gradient-to-r from-indigo-600 to-cyan-600 bg-clip-text text-transparent mb-2 sm:mb-4">
                {formatTime(timer)}
              </div>
              {currentEntry && (
                <div className="text-sm sm:text-lg text-gray-600 px-2">
                  {currentEntry.projectName} - {currentEntry.taskName}
                </div>
              )}
            </div>

            {!currentEntry ? (
              <div className="space-y-3 sm:space-y-4">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 sm:gap-4">
                  <Input
                    placeholder="Project name"
                    value={newProject}
                    onChange={(e) => setNewProject(e.target.value)}
                    className="rounded-xl border-2 border-gray-200 focus:border-indigo-500"
                  />
                  <Input
                    placeholder="Task name"
                    value={newTask}
                    onChange={(e) => setNewTask(e.target.value)}
                    className="rounded-xl border-2 border-gray-200 focus:border-indigo-500"
                  />
                  <Input
                    placeholder="Category (optional)"
                    value={newCategory}
                    onChange={(e) => setNewCategory(e.target.value)}
                    className="rounded-xl border-2 border-gray-200 focus:border-indigo-500"
                  />
                </div>
                <Button
                  onClick={startTimer}
                  disabled={!newProject.trim() || !newTask.trim()}
                  className="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-2xl py-3 sm:py-4 text-base sm:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Play className="w-5 h-5 sm:w-6 sm:h-6 mr-2" />
                  Start Timer
                </Button>
              </div>
            ) : (
              <Button
                onClick={stopTimer}
                className="w-full bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-2xl py-3 sm:py-4 text-base sm:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Square className="w-5 h-5 sm:w-6 sm:h-6 mr-2" />
                Stop Timer
              </Button>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Recent Entries */}
      <motion.div
        variants={containerVariants}
        initial="initial"
        animate="animate"
        className="space-y-4"
      >
        <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-3 sm:mb-4">Recent Sessions</h2>
        
        {entries.slice().reverse().map((entry, index) => (
          <motion.div
            key={entry.id}
            variants={itemVariants}
            whileHover={{ scale: 1.02 }}
            className="group"
          >
            <Card className="border-0 shadow-lg bg-white/90 backdrop-blur-sm hover:shadow-xl transition-all duration-300 rounded-2xl">
              <CardContent className="p-3 sm:p-4">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 sm:gap-0">
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                      <h3 className="font-bold text-base sm:text-lg text-gray-800 truncate">{entry.projectName}</h3>
                      <div className="flex flex-wrap gap-2">
                        <span className="px-2 sm:px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-xs sm:text-sm font-medium">
                          {entry.category}
                        </span>
                        {entry.isRunning && (
                          <span className="px-2 sm:px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs sm:text-sm font-medium flex items-center">
                            <div className="w-2 h-2 bg-green-500 rounded-full mr-1 sm:mr-2 animate-pulse"></div>
                            Running
                          </span>
                        )}
                      </div>
                    </div>
                    <p className="text-gray-600 mb-2 text-sm sm:text-base">{entry.taskName}</p>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 text-xs sm:text-sm text-gray-500">
                      <span>{new Date(entry.startTime).toLocaleDateString()}</span>
                      <span>{new Date(entry.startTime).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}</span>
                      {entry.endTime && (
                        <span className="font-semibold text-indigo-600">
                          Duration: {formatTime(entry.duration)}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex justify-end sm:opacity-0 sm:group-hover:opacity-100 transition-opacity">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => deleteEntry(entry.id)}
                      className="rounded-xl text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {entries.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <Clock className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">No Time Entries Yet</h3>
          <p className="text-gray-500">Start tracking your time to see your productivity insights</p>
        </motion.div>
      )}
      </div>
    </div>
  );
};

export default TimeTracker;
