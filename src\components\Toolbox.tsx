
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  StickyNote, Timer, Focus, Zap, Brain, Trash2,
  Camera, Archive, Package, Palette, Settings,
  Download, Upload, RotateCcw, Play, Pause, Square,
  Scan, FileText, Layers, BarChart3, Clock, Bell
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { GlassmorphCard } from '@/components/ui/glassmorphcard';

const Toolbox = () => {
  const [activeTab, setActiveTab] = useState('utilities');
  const [stickyNotes, setStickyNotes] = useState([]);
  const [timerRunning, setTimerRunning] = useState(false);
  const [timerTime, setTimerTime] = useState(1500); // 25 minutes default
  const [customTimer, setCustomTimer] = useState(25);
  const [distractionMode, setDistractionMode] = useState(false);

  useEffect(() => {
    const savedNotes = JSON.parse(localStorage.getItem('stickyNotes') || '[]');
    setStickyNotes(savedNotes);
  }, []);

  useEffect(() => {
    let interval;
    if (timerRunning && timerTime > 0) {
      interval = setInterval(() => {
        setTimerTime(time => time - 1);
      }, 1000);
    } else if (timerTime === 0) {
      setTimerRunning(false);
      // Timer completed notification
      if (Notification.permission === 'granted') {
        new Notification('Timer Complete!', {
          body: 'Your focus session is finished.',
          icon: '/favicon.ico'
        });
      }
    }
    return () => clearInterval(interval);
  }, [timerRunning, timerTime]);

  const saveStickyNotes = (notes) => {
    setStickyNotes(notes);
    localStorage.setItem('stickyNotes', JSON.stringify(notes));
  };

  const addStickyNote = () => {
    const newNote = {
      id: Date.now(),
      content: 'New sticky note',
      color: 'bg-yellow-200',
      position: { x: Math.random() * 300, y: Math.random() * 200 },
      pinned: false,
      createdAt: new Date()
    };
    const updatedNotes = [...stickyNotes, newNote];
    saveStickyNotes(updatedNotes);
  };

  const updateStickyNote = (id, updates) => {
    const updatedNotes = stickyNotes.map(note => 
      note.id === id ? { ...note, ...updates } : note
    );
    saveStickyNotes(updatedNotes);
  };

  const deleteStickyNote = (id) => {
    const updatedNotes = stickyNotes.filter(note => note.id !== id);
    saveStickyNotes(updatedNotes);
  };

  const startTimer = () => {
    setTimerTime(customTimer * 60);
    setTimerRunning(true);
  };

  const pauseTimer = () => {
    setTimerRunning(false);
  };

  const resetTimer = () => {
    setTimerRunning(false);
    setTimerTime(customTimer * 60);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const declutterData = () => {
    const confirm = window.confirm('This will archive old data and clean up your interface. Continue?');
    if (confirm) {
      // Archive logic here
      localStorage.setItem('lastDeclutter', new Date().toISOString());
    }
  };

  const resetAllData = () => {
    const confirm = window.confirm('This will permanently delete ALL your data. Are you sure?');
    if (confirm) {
      const doubleConfirm = window.confirm('This action cannot be undone. Really delete everything?');
      if (doubleConfirm) {
        localStorage.clear();
        window.location.reload();
      }
    }
  };

  const exportData = () => {
    const allData = {
      stickyNotes: JSON.parse(localStorage.getItem('stickyNotes') || '[]'),
      tasks: JSON.parse(localStorage.getItem('tasks') || '[]'),
      habits: JSON.parse(localStorage.getItem('smartHabits') || '[]'),
      goals: JSON.parse(localStorage.getItem('goals') || '[]'),
      notes: JSON.parse(localStorage.getItem('notes') || '[]'),
      journalEntries: JSON.parse(localStorage.getItem('journalEntries') || '[]'),
      timeBlocks: JSON.parse(localStorage.getItem('timeBlocks') || '[]'),
      wellnessEntries: JSON.parse(localStorage.getItem('wellnessEntries') || '[]'),
      exportDate: new Date().toISOString()
    };

    const dataStr = JSON.stringify(allData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `spark-life-compass-backup-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const importData = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target.result);

        // Restore all data
        Object.keys(data).forEach(key => {
          if (key !== 'exportDate' && Array.isArray(data[key])) {
            localStorage.setItem(key, JSON.stringify(data[key]));
          }
        });

        alert('Data imported successfully! Please refresh the page.');
      } catch (error) {
        alert('Error importing data. Please check the file format.');
      }
    };
    reader.readAsText(file);
  };

  const openDocumentScanner = () => {
    // In a real app, this would open camera for document scanning
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      navigator.mediaDevices.getUserMedia({ video: true })
        .then(stream => {
          alert('Document scanner feature coming soon! Camera access granted.');
          stream.getTracks().forEach(track => track.stop());
        })
        .catch(error => {
          alert('Camera access denied. Please enable camera permissions for document scanning.');
        });
    } else {
      alert('Camera not available on this device.');
    }
  };

  const batchTasks = () => {
    const tasks = JSON.parse(localStorage.getItem('tasks') || '[]');

    // Group tasks by category or priority
    const batches = tasks.reduce((acc, task) => {
      const key = task.category || 'uncategorized';
      if (!acc[key]) acc[key] = [];
      acc[key].push(task);
      return acc;
    }, {});

    console.log('Task batches:', batches);
    alert(`Found ${Object.keys(batches).length} task categories. Check console for details.`);
  };

  const quickCapture = () => {
    const idea = prompt('Quick capture - What\'s on your mind?');
    if (idea && idea.trim()) {
      const capture = {
        id: Date.now(),
        content: idea,
        timestamp: new Date().toISOString(),
        type: 'quick-capture'
      };

      const captures = JSON.parse(localStorage.getItem('quickCaptures') || '[]');
      captures.unshift(capture);
      localStorage.setItem('quickCaptures', JSON.stringify(captures));

      alert('Idea captured! You can find it in your quick captures.');
    }
  };

  const tabs = [
    { id: 'utilities', label: 'Utilities', icon: Zap },
    { id: 'timer', label: 'Focus Timer', icon: Timer },
    { id: 'notes', label: 'Sticky Notes', icon: StickyNote },
    { id: 'ai', label: 'AI Assistant', icon: Brain },
    { id: 'settings', label: 'Tools', icon: Settings }
  ];

  return (
    <div className="min-h-screen p-4 pb-24 bg-gradient-to-br from-orange-50 via-white to-red-50">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <h1 className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-2">
          Toolbox 🧰
        </h1>
        <p className="text-gray-600">Utility tools to boost your productivity</p>
      </motion.div>

      {/* Distraction-Free Mode Toggle */}
      {distractionMode && (
        <div className="fixed inset-0 bg-black/90 z-40 flex items-center justify-center">
          <div className="bg-white rounded-2xl p-8 text-center max-w-md">
            <Focus className="w-16 h-16 text-orange-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-4">Focus Mode Active</h2>
            <p className="text-gray-600 mb-6">Minimize distractions and stay focused</p>
            <Button onClick={() => setDistractionMode(false)} className="bg-orange-500 hover:bg-orange-600">
              Exit Focus Mode
            </Button>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex overflow-x-auto mb-6 space-x-2 pb-2">
        {tabs.map((tab) => (
          <Button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            variant={activeTab === tab.id ? 'default' : 'outline'}
            className={`flex items-center space-x-2 whitespace-nowrap ${
              activeTab === tab.id 
                ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white' 
                : 'bg-white/80'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span>{tab.label}</span>
          </Button>
        ))}
      </div>

      <AnimatePresence mode="wait">
        {activeTab === 'utilities' && (
          <motion.div
            key="utilities"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            <GlassmorphCard className="p-6">
              <div className="text-center space-y-4">
                <Focus className="w-12 h-12 text-blue-500 mx-auto" />
                <h3 className="text-lg font-semibold">Distraction-Free Mode</h3>
                <p className="text-sm text-gray-600">Block distractions and focus deeply</p>
                <Button 
                  onClick={() => setDistractionMode(true)}
                  className="w-full bg-blue-500 hover:bg-blue-600"
                >
                  Activate Focus Mode
                </Button>
              </div>
            </GlassmorphCard>

            <GlassmorphCard className="p-6">
              <div className="text-center space-y-4">
                <Camera className="w-12 h-12 text-green-500 mx-auto" />
                <h3 className="text-lg font-semibold">Quick Capture</h3>
                <p className="text-sm text-gray-600">Instantly save ideas and thoughts</p>
                <Button
                  onClick={quickCapture}
                  className="w-full bg-green-500 hover:bg-green-600"
                >
                  Open Quick Capture
                </Button>
              </div>
            </GlassmorphCard>

            <GlassmorphCard className="p-6">
              <div className="text-center space-y-4">
                <Package className="w-12 h-12 text-purple-500 mx-auto" />
                <h3 className="text-lg font-semibold">Task Batching</h3>
                <p className="text-sm text-gray-600">Group similar tasks together</p>
                <Button
                  onClick={batchTasks}
                  className="w-full bg-purple-500 hover:bg-purple-600"
                >
                  Batch Tasks
                </Button>
              </div>
            </GlassmorphCard>

            <GlassmorphCard className="p-6">
              <div className="text-center space-y-4">
                <Archive className="w-12 h-12 text-yellow-500 mx-auto" />
                <h3 className="text-lg font-semibold">Declutter Tool</h3>
                <p className="text-sm text-gray-600">Clean up old data and reset interface</p>
                <Button 
                  onClick={declutterData}
                  className="w-full bg-yellow-500 hover:bg-yellow-600"
                >
                  Declutter Now
                </Button>
              </div>
            </GlassmorphCard>

            <GlassmorphCard className="p-6">
              <div className="text-center space-y-4">
                <Download className="w-12 h-12 text-indigo-500 mx-auto" />
                <h3 className="text-lg font-semibold">Export Data</h3>
                <p className="text-sm text-gray-600">Backup all your data</p>
                <Button 
                  onClick={exportData}
                  className="w-full bg-indigo-500 hover:bg-indigo-600"
                >
                  Export Backup
                </Button>
              </div>
            </GlassmorphCard>

            <GlassmorphCard className="p-6">
              <div className="text-center space-y-4">
                <Upload className="w-12 h-12 text-teal-500 mx-auto" />
                <h3 className="text-lg font-semibold">Import Data</h3>
                <p className="text-sm text-gray-600">Restore from backup file</p>
                <label className="w-full">
                  <input
                    type="file"
                    accept=".json"
                    onChange={importData}
                    className="hidden"
                  />
                  <Button className="w-full bg-teal-500 hover:bg-teal-600">
                    Import Backup
                  </Button>
                </label>
              </div>
            </GlassmorphCard>

            <GlassmorphCard className="p-6">
              <div className="text-center space-y-4">
                <Scan className="w-12 h-12 text-cyan-500 mx-auto" />
                <h3 className="text-lg font-semibold">Document Scanner</h3>
                <p className="text-sm text-gray-600">Scan documents with camera</p>
                <Button
                  onClick={openDocumentScanner}
                  className="w-full bg-cyan-500 hover:bg-cyan-600"
                >
                  Open Scanner
                </Button>
              </div>
            </GlassmorphCard>

            <GlassmorphCard className="p-6">
              <div className="text-center space-y-4">
                <Trash2 className="w-12 h-12 text-red-500 mx-auto" />
                <h3 className="text-lg font-semibold">Reset All Data</h3>
                <p className="text-sm text-gray-600">Start fresh (irreversible)</p>
                <Button
                  onClick={resetAllData}
                  variant="destructive"
                  className="w-full"
                >
                  Reset Everything
                </Button>
              </div>
            </GlassmorphCard>
          </motion.div>
        )}

        {activeTab === 'timer' && (
          <motion.div
            key="timer"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-md mx-auto"
          >
            <GlassmorphCard className="p-8 text-center">
              <h2 className="text-2xl font-bold mb-6">Focus Timer</h2>
              
              <div className="mb-6">
                <div className="text-6xl font-mono font-bold text-orange-600 mb-4">
                  {formatTime(timerTime)}
                </div>
                
                <div className="flex justify-center space-x-4 mb-6">
                  {!timerRunning ? (
                    <Button onClick={startTimer} className="bg-green-500 hover:bg-green-600">
                      <Play className="w-4 h-4 mr-2" />
                      Start
                    </Button>
                  ) : (
                    <Button onClick={pauseTimer} className="bg-yellow-500 hover:bg-yellow-600">
                      <Pause className="w-4 h-4 mr-2" />
                      Pause
                    </Button>
                  )}
                  
                  <Button onClick={resetTimer} variant="outline">
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Reset
                  </Button>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium">Timer Duration (minutes)</label>
                  <Input
                    type="number"
                    value={customTimer}
                    onChange={(e) => setCustomTimer(parseInt(e.target.value) || 25)}
                    min="1"
                    max="60"
                    className="text-center"
                  />
                </div>

                <div className="grid grid-cols-3 gap-2 mt-4">
                  {[15, 25, 45].map(duration => (
                    <Button
                      key={duration}
                      onClick={() => setCustomTimer(duration)}
                      variant={customTimer === duration ? 'default' : 'outline'}
                      size="sm"
                    >
                      {duration}m
                    </Button>
                  ))}
                </div>
              </div>
            </GlassmorphCard>
          </motion.div>
        )}

        {activeTab === 'notes' && (
          <motion.div
            key="notes"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Sticky Notes</h2>
              <Button onClick={addStickyNote} className="bg-yellow-500 hover:bg-yellow-600">
                <StickyNote className="w-4 h-4 mr-2" />
                Add Sticky Note
              </Button>
            </div>

            <div className="relative min-h-96 bg-gradient-to-br from-gray-50 to-white rounded-2xl p-4 border-2 border-dashed border-gray-200">
              {stickyNotes.map(note => (
                <StickyNoteComponent
                  key={note.id}
                  note={note}
                  onUpdate={updateStickyNote}
                  onDelete={deleteStickyNote}
                />
              ))}
              
              {stickyNotes.length === 0 && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-gray-400">
                    <StickyNote className="w-16 h-16 mx-auto mb-4" />
                    <p>No sticky notes yet. Add one to get started!</p>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}

        {activeTab === 'ai' && (
          <motion.div
            key="ai"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <GlassmorphCard className="p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Brain className="w-5 h-5 mr-2 text-purple-500" />
                AI Assistant
              </h2>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button className="p-4 h-auto bg-blue-500 hover:bg-blue-600">
                    <div className="text-left">
                      <div className="font-semibold">Smart Planner</div>
                      <div className="text-sm opacity-80">Plan your day intelligently</div>
                    </div>
                  </Button>
                  
                  <Button className="p-4 h-auto bg-green-500 hover:bg-green-600">
                    <div className="text-left">
                      <div className="font-semibold">Writing Assistant</div>
                      <div className="text-sm opacity-80">Improve your writing</div>
                    </div>
                  </Button>
                  
                  <Button className="p-4 h-auto bg-purple-500 hover:bg-purple-600">
                    <div className="text-left">
                      <div className="font-semibold">Goal Summarizer</div>
                      <div className="text-sm opacity-80">Analyze your progress</div>
                    </div>
                  </Button>
                  
                  <Button className="p-4 h-auto bg-orange-500 hover:bg-orange-600">
                    <div className="text-left">
                      <div className="font-semibold">Habit Coach</div>
                      <div className="text-sm opacity-80">Get personalized tips</div>
                    </div>
                  </Button>
                </div>
                
                <div className="border-t pt-4">
                  <textarea
                    placeholder="Ask me anything about productivity, planning, or personal development..."
                    className="w-full h-24 p-3 border rounded-lg resize-none"
                  />
                  <Button className="mt-2 bg-gradient-to-r from-purple-500 to-blue-500">
                    Ask AI Assistant
                  </Button>
                </div>
              </div>
            </GlassmorphCard>
          </motion.div>
        )}

        {activeTab === 'settings' && (
          <motion.div
            key="settings"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <GlassmorphCard className="p-6">
              <h2 className="text-xl font-semibold mb-4">Tool Settings</h2>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Enable timer notifications</span>
                  <Button variant="outline" size="sm">Toggle</Button>
                </div>
                
                <div className="flex items-center justify-between">
                  <span>Auto-save sticky notes</span>
                  <Button variant="outline" size="sm">On</Button>
                </div>
                
                <div className="flex items-center justify-between">
                  <span>Focus mode shortcuts</span>
                  <Button variant="outline" size="sm">Configure</Button>
                </div>
              </div>
            </GlassmorphCard>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Sticky Note Component
const StickyNoteComponent = ({ note, onUpdate, onDelete }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [content, setContent] = useState(note.content);

  const handleSave = () => {
    onUpdate(note.id, { content });
    setIsEditing(false);
  };

  const colors = [
    'bg-yellow-200', 'bg-pink-200', 'bg-blue-200', 
    'bg-green-200', 'bg-purple-200', 'bg-orange-200'
  ];

  return (
    <div
      className={`absolute w-48 h-32 ${note.color} rounded-lg shadow-lg p-3 cursor-move transform hover:scale-105 transition-transform`}
      style={{ 
        left: note.position.x, 
        top: note.position.y,
        transform: `rotate(${Math.random() * 6 - 3}deg)`
      }}
    >
      <div className="flex justify-between items-start mb-2">
        <div className="flex space-x-1">
          {colors.map(color => (
            <button
              key={color}
              className={`w-3 h-3 ${color} rounded-full border border-gray-300`}
              onClick={() => onUpdate(note.id, { color })}
            />
          ))}
        </div>
        <Button
          size="sm"
          variant="ghost"
          onClick={() => onDelete(note.id)}
          className="p-0 h-auto text-red-500 hover:text-red-700"
        >
          ×
        </Button>
      </div>
      
      {isEditing ? (
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          onBlur={handleSave}
          onKeyDown={(e) => e.key === 'Enter' && e.ctrlKey && handleSave()}
          className="w-full h-16 bg-transparent resize-none border-none outline-none text-sm"
          autoFocus
        />
      ) : (
        <div
          onClick={() => setIsEditing(true)}
          className="text-sm h-16 overflow-hidden cursor-text"
        >
          {note.content}
        </div>
      )}
    </div>
  );
};

export default Toolbox;
