import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Calculator, Timer, Stopwatch, Ruler, Palette, Lock, FileText, Search,
  Plus, Minus, X, Divide, Equal, Play, Pause, Square, RotateCcw,
  Copy, Eye, EyeOff, StickyNote, Trash2, Hash, DollarSign
} from 'lucide-react';

const Toolbox: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string>('calculator');
  const [searchQuery, setSearchQuery] = useState('');

  // Calculator State
  const [calcDisplay, setCalcDisplay] = useState('0');
  const [calcPrevious, setCalcPrevious] = useState<number | null>(null);
  const [calcOperation, setCalcOperation] = useState<string | null>(null);
  const [calcWaitingForOperand, setCalcWaitingForOperand] = useState(false);

  // Timer State
  const [timerMinutes, setTimerMinutes] = useState(5);
  const [timerActive, setTimerActive] = useState(false);
  const [timerTime, setTimerTime] = useState(0);

  // Stopwatch State
  const [stopwatchTime, setStopwatchTime] = useState(0);
  const [stopwatchActive, setStopwatchActive] = useState(false);

  // Unit Converter State
  const [convertFrom, setConvertFrom] = useState('meters');
  const [convertTo, setConvertTo] = useState('feet');
  const [convertValue, setConvertValue] = useState('');
  const [convertResult, setConvertResult] = useState('');

  // Color Generator State
  const [generatedColor, setGeneratedColor] = useState('#3B82F6');

  // Password Generator State
  const [passwordLength, setPasswordLength] = useState(12);
  const [generatedPassword, setGeneratedPassword] = useState('');

  // Text Tools State
  const [textInput, setTextInput] = useState('');
  const [textResult, setTextResult] = useState('');

  // Sticky Notes State
  const [stickyNotes, setStickyNotes] = useState<Array<{id: string, text: string, color: string}>>([]);
  const [newNoteText, setNewNoteText] = useState('');

  // Calculator Functions
  const inputDigit = (digit: string) => {
    if (calcWaitingForOperand) {
      setCalcDisplay(String(digit));
      setCalcWaitingForOperand(false);
    } else {
      setCalcDisplay(calcDisplay === '0' ? String(digit) : calcDisplay + digit);
    }
  };

  const inputOperation = (nextOperation: string) => {
    const inputValue = parseFloat(calcDisplay);
    if (calcPrevious === null) {
      setCalcPrevious(inputValue);
    } else if (calcOperation) {
      const currentValue = calcPrevious || 0;
      const newValue = calculate(currentValue, inputValue, calcOperation);
      setCalcDisplay(String(newValue));
      setCalcPrevious(newValue);
    }
    setCalcWaitingForOperand(true);
    setCalcOperation(nextOperation);
  };

  const calculate = (firstOperand: number, secondOperand: number, operation: string): number => {
    switch (operation) {
      case '+': return firstOperand + secondOperand;
      case '-': return firstOperand - secondOperand;
      case '*': return firstOperand * secondOperand;
      case '/': return firstOperand / secondOperand;
      default: return secondOperand;
    }
  };

  const performCalculation = () => {
    const inputValue = parseFloat(calcDisplay);
    if (calcPrevious !== null && calcOperation) {
      const newValue = calculate(calcPrevious, inputValue, calcOperation);
      setCalcDisplay(String(newValue));
      setCalcPrevious(null);
      setCalcOperation(null);
      setCalcWaitingForOperand(true);
    }
  };

  const clearCalculator = () => {
    setCalcDisplay('0');
    setCalcPrevious(null);
    setCalcOperation(null);
    setCalcWaitingForOperand(false);
  };

  // Timer Functions
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (timerActive && timerTime > 0) {
      interval = setInterval(() => {
        setTimerTime(time => time - 1);
      }, 1000);
    } else if (timerTime === 0 && timerActive) {
      setTimerActive(false);
      alert('Timer finished!');
    }
    return () => clearInterval(interval);
  }, [timerActive, timerTime]);

  const startTimer = () => {
    setTimerTime(timerMinutes * 60);
    setTimerActive(true);
  };

  const stopTimer = () => setTimerActive(false);
  const resetTimer = () => {
    setTimerActive(false);
    setTimerTime(0);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Stopwatch Functions
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (stopwatchActive) {
      interval = setInterval(() => {
        setStopwatchTime(time => time + 1);
      }, 10);
    }
    return () => clearInterval(interval);
  }, [stopwatchActive]);

  const startStopwatch = () => setStopwatchActive(true);
  const stopStopwatch = () => setStopwatchActive(false);
  const resetStopwatch = () => {
    setStopwatchActive(false);
    setStopwatchTime(0);
  };

  const formatStopwatchTime = (time: number) => {
    const minutes = Math.floor(time / 6000);
    const seconds = Math.floor((time % 6000) / 100);
    const centiseconds = time % 100;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${centiseconds.toString().padStart(2, '0')}`;
  };

  // Unit Converter Functions
  const conversionRates: Record<string, Record<string, number>> = {
    meters: { feet: 3.28084, inches: 39.3701, yards: 1.09361 },
    feet: { meters: 0.3048, inches: 12, yards: 0.333333 },
    inches: { meters: 0.0254, feet: 0.0833333, yards: 0.0277778 },
    yards: { meters: 0.9144, feet: 3, inches: 36 }
  };

  const convertUnits = () => {
    const value = parseFloat(convertValue);
    if (isNaN(value)) return;
    if (convertFrom === convertTo) {
      setConvertResult(convertValue);
      return;
    }
    const rate = conversionRates[convertFrom]?.[convertTo];
    if (rate) {
      setConvertResult((value * rate).toFixed(6));
    }
  };

  // Color Generator Functions
  const generateRandomColor = () => {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    setGeneratedColor(colors[Math.floor(Math.random() * colors.length)]);
  };

  // Password Generator Functions
  const generatePassword = () => {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
    let password = '';
    for (let i = 0; i < passwordLength; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    setGeneratedPassword(password);
  };

  // Text Tools Functions
  const convertToUpperCase = () => setTextResult(textInput.toUpperCase());
  const convertToLowerCase = () => setTextResult(textInput.toLowerCase());
  const reverseText = () => setTextResult(textInput.split('').reverse().join(''));
  const countWords = () => setTextResult(`Words: ${textInput.trim().split(/\s+/).filter(word => word.length > 0).length}`);

  // Sticky Notes Functions
  const addStickyNote = () => {
    if (newNoteText.trim()) {
      const colors = ['#FFE4B5', '#E6E6FA', '#F0FFF0', '#FFE4E1', '#E0FFFF'];
      const newNote = {
        id: Date.now().toString(),
        text: newNoteText,
        color: colors[Math.floor(Math.random() * colors.length)]
      };
      const updatedNotes = [...stickyNotes, newNote];
      setStickyNotes(updatedNotes);
      localStorage.setItem('stickyNotes', JSON.stringify(updatedNotes));
      setNewNoteText('');
    }
  };

  const deleteStickyNote = (id: string) => {
    const updatedNotes = stickyNotes.filter(note => note.id !== id);
    setStickyNotes(updatedNotes);
    localStorage.setItem('stickyNotes', JSON.stringify(updatedNotes));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('Copied to clipboard!');
  };

  // Load saved data
  useEffect(() => {
    const savedNotes = JSON.parse(localStorage.getItem('stickyNotes') || '[]');
    setStickyNotes(savedNotes);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen p-4 pb-24 bg-gradient-to-br from-blue-50 via-white to-purple-50"
    >
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">🧰 Toolbox</h1>
          <p className="text-gray-600">20+ Essential productivity tools</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Search tools..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-64"
          />
          <Button variant="outline">
            <Search className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Tool Categories */}
      <Tabs value={activeCategory} onValueChange={setActiveCategory} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="calculator">Calculator</TabsTrigger>
          <TabsTrigger value="converter">Converter</TabsTrigger>
          <TabsTrigger value="timer">Timer</TabsTrigger>
          <TabsTrigger value="utility">Utility</TabsTrigger>
          <TabsTrigger value="generator">Generator</TabsTrigger>
          <TabsTrigger value="notes">Notes</TabsTrigger>
        </TabsList>

        {/* Calculator Tab */}
        <TabsContent value="calculator" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calculator className="w-5 h-5" />
                <span>Calculator</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="max-w-sm mx-auto">
                <div className="bg-gray-900 text-white p-4 rounded-lg mb-4">
                  <div className="text-right text-2xl font-mono">{calcDisplay}</div>
                </div>

                <div className="grid grid-cols-4 gap-2">
                  <Button onClick={clearCalculator} variant="outline" className="col-span-2">Clear</Button>
                  <Button onClick={() => inputOperation('/')} variant="outline"><Divide className="w-4 h-4" /></Button>
                  <Button onClick={() => inputOperation('*')} variant="outline"><X className="w-4 h-4" /></Button>

                  <Button onClick={() => inputDigit('7')} variant="outline">7</Button>
                  <Button onClick={() => inputDigit('8')} variant="outline">8</Button>
                  <Button onClick={() => inputDigit('9')} variant="outline">9</Button>
                  <Button onClick={() => inputOperation('-')} variant="outline"><Minus className="w-4 h-4" /></Button>

                  <Button onClick={() => inputDigit('4')} variant="outline">4</Button>
                  <Button onClick={() => inputDigit('5')} variant="outline">5</Button>
                  <Button onClick={() => inputDigit('6')} variant="outline">6</Button>
                  <Button onClick={() => inputOperation('+')} variant="outline"><Plus className="w-4 h-4" /></Button>

                  <Button onClick={() => inputDigit('1')} variant="outline">1</Button>
                  <Button onClick={() => inputDigit('2')} variant="outline">2</Button>
                  <Button onClick={() => inputDigit('3')} variant="outline">3</Button>
                  <Button onClick={performCalculation} variant="default" className="row-span-2 bg-blue-500 hover:bg-blue-600">
                    <Equal className="w-4 h-4" />
                  </Button>

                  <Button onClick={() => inputDigit('0')} variant="outline" className="col-span-2">0</Button>
                  <Button onClick={() => inputDigit('.')} variant="outline">.</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Timer Tab */}
        <TabsContent value="timer" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Timer className="w-5 h-5" />
                  <span>Timer</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-4xl font-mono font-bold text-blue-600 mb-4">
                  {formatTime(timerTime)}
                </div>

                <div className="flex justify-center space-x-2 mb-4">
                  <Button onClick={startTimer} disabled={timerActive} className="bg-green-500 hover:bg-green-600">
                    <Play className="w-4 h-4 mr-2" />
                    Start
                  </Button>
                  <Button onClick={stopTimer} disabled={!timerActive} className="bg-red-500 hover:bg-red-600">
                    <Pause className="w-4 h-4 mr-2" />
                    Stop
                  </Button>
                  <Button onClick={resetTimer} variant="outline">
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Reset
                  </Button>
                </div>

                <div className="space-y-2">
                  <Label>Timer Duration (minutes)</Label>
                  <Input
                    type="number"
                    value={timerMinutes}
                    onChange={(e) => setTimerMinutes(parseInt(e.target.value) || 5)}
                    min="1"
                    max="120"
                    className="text-center"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Stopwatch className="w-5 h-5" />
                  <span>Stopwatch</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-4xl font-mono font-bold text-green-600 mb-4">
                  {formatStopwatchTime(stopwatchTime)}
                </div>

                <div className="flex justify-center space-x-2">
                  <Button onClick={startStopwatch} disabled={stopwatchActive} className="bg-green-500 hover:bg-green-600">
                    <Play className="w-4 h-4 mr-2" />
                    Start
                  </Button>
                  <Button onClick={stopStopwatch} disabled={!stopwatchActive} className="bg-red-500 hover:bg-red-600">
                    <Square className="w-4 h-4 mr-2" />
                    Stop
                  </Button>
                  <Button onClick={resetStopwatch} variant="outline">
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Reset
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Converter Tab */}
        <TabsContent value="converter" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Ruler className="w-5 h-5" />
                <span>Unit Converter</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>From</Label>
                  <Select value={convertFrom} onValueChange={setConvertFrom}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="meters">Meters</SelectItem>
                      <SelectItem value="feet">Feet</SelectItem>
                      <SelectItem value="inches">Inches</SelectItem>
                      <SelectItem value="yards">Yards</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    type="number"
                    placeholder="Enter value"
                    value={convertValue}
                    onChange={(e) => setConvertValue(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>To</Label>
                  <Select value={convertTo} onValueChange={setConvertTo}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="meters">Meters</SelectItem>
                      <SelectItem value="feet">Feet</SelectItem>
                      <SelectItem value="inches">Inches</SelectItem>
                      <SelectItem value="yards">Yards</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    type="text"
                    placeholder="Result"
                    value={convertResult}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
              </div>

              <Button onClick={convertUnits} className="w-full mt-4">
                Convert
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Generator Tab */}
        <TabsContent value="generator" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Palette className="w-5 h-5" />
                  <span>Color Generator</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <div
                  className="w-32 h-32 mx-auto rounded-lg mb-4 border-2 border-gray-200"
                  style={{ backgroundColor: generatedColor }}
                ></div>
                <div className="text-lg font-mono mb-4">{generatedColor}</div>
                <div className="flex justify-center space-x-2">
                  <Button onClick={generateRandomColor} className="bg-purple-500 hover:bg-purple-600">
                    Generate Color
                  </Button>
                  <Button onClick={() => copyToClipboard(generatedColor)} variant="outline">
                    <Copy className="w-4 h-4 mr-2" />
                    Copy
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Lock className="w-5 h-5" />
                  <span>Password Generator</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label>Password Length: {passwordLength}</Label>
                    <Input
                      type="range"
                      min="4"
                      max="50"
                      value={passwordLength}
                      onChange={(e) => setPasswordLength(parseInt(e.target.value))}
                      className="mt-2"
                    />
                  </div>

                  <div className="p-3 bg-gray-50 rounded border font-mono text-sm break-all">
                    {generatedPassword || 'Click generate to create password'}
                  </div>

                  <div className="flex space-x-2">
                    <Button onClick={generatePassword} className="flex-1">
                      Generate Password
                    </Button>
                    <Button onClick={() => copyToClipboard(generatedPassword)} variant="outline" disabled={!generatedPassword}>
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Utility Tab */}
        <TabsContent value="utility" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="w-5 h-5" />
                <span>Text Tools</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label>Input Text</Label>
                  <Input
                    placeholder="Enter text to transform..."
                    value={textInput}
                    onChange={(e) => setTextInput(e.target.value)}
                    className="mt-2"
                  />
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  <Button onClick={convertToUpperCase} variant="outline" size="sm">
                    UPPERCASE
                  </Button>
                  <Button onClick={convertToLowerCase} variant="outline" size="sm">
                    lowercase
                  </Button>
                  <Button onClick={reverseText} variant="outline" size="sm">
                    Reverse
                  </Button>
                  <Button onClick={countWords} variant="outline" size="sm">
                    Count Words
                  </Button>
                </div>

                <div>
                  <Label>Result</Label>
                  <div className="p-3 bg-gray-50 rounded border mt-2 min-h-[60px]">
                    {textResult || 'Result will appear here...'}
                  </div>
                  {textResult && (
                    <Button onClick={() => copyToClipboard(textResult)} variant="outline" size="sm" className="mt-2">
                      <Copy className="w-4 h-4 mr-2" />
                      Copy Result
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notes Tab */}
        <TabsContent value="notes" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <StickyNote className="w-5 h-5" />
                <span>Sticky Notes</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex space-x-2">
                  <Input
                    placeholder="Add a new note..."
                    value={newNoteText}
                    onChange={(e) => setNewNoteText(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addStickyNote()}
                    className="flex-1"
                  />
                  <Button onClick={addStickyNote} disabled={!newNoteText.trim()}>
                    Add Note
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {stickyNotes.map((note) => (
                    <div
                      key={note.id}
                      className="p-4 rounded-lg shadow-sm border relative"
                      style={{ backgroundColor: note.color }}
                    >
                      <p className="text-sm mb-2 pr-6">{note.text}</p>
                      <Button
                        onClick={() => deleteStickyNote(note.id)}
                        variant="ghost"
                        size="sm"
                        className="absolute top-2 right-2 h-6 w-6 p-0 hover:bg-red-100"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>

                {stickyNotes.length === 0 && (
                  <div className="text-center text-gray-500 py-8">
                    No sticky notes yet. Add one above!
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </motion.div>
  );
};

export default Toolbox;
