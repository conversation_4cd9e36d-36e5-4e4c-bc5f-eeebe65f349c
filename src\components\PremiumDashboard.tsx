import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Plus, Calendar, Heart, Clock, Target, CalendarDays, TrendingUp, CheckCircle, Circle, Play, 
  Pause, User, Quote, Zap, Brain, Coffee, Moon, Sun, Edit3, MoreHorizontal, ChevronRight,
  Timer, BookOpen, Activity, Settings, Home, BarChart3, PlusCircle, Mic, Camera, Sparkles,
  X, Check, ArrowRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface PremiumDashboardProps {
  userProfile?: any;
}

const PremiumDashboard = ({ userProfile }: PremiumDashboardProps) => {
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [pomodoroActive, setPomodoroActive] = useState(false);
  const [pomodoroTime, setPomodoroTime] = useState(25 * 60);
  const [currentTask, setCurrentTask] = useState('Focus Session');
  const [fabOpen, setFabOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [todayTasks, setTodayTasks] = useState([
    { id: 1, title: 'Review project proposal', completed: false, priority: 'high' },
    { id: 2, title: 'Team standup meeting', completed: true, priority: 'medium' },
    { id: 3, title: 'Update documentation', completed: false, priority: 'low' },
    { id: 4, title: 'Code review session', completed: false, priority: 'high' }
  ]);
  const [todayMood, setTodayMood] = useState({ emoji: '😊', mood: 'Good', energy: 75 });
  const [habits, setHabits] = useState([
    { name: 'Morning Exercise', completed: true, streak: 12 },
    { name: 'Read 30min', completed: false, streak: 8 },
    { name: 'Meditate', completed: true, streak: 15 },
    { name: 'Drink Water', completed: false, streak: 5 }
  ]);

  const dailyQuotes = [
    "The way to get started is to quit talking and begin doing.",
    "Innovation distinguishes between a leader and a follower.",
    "Your limitation—it's only your imagination.",
    "Great things never come from comfort zones.",
    "Dream it. Wish it. Do it."
  ];

  const [dailyQuote] = useState(dailyQuotes[Math.floor(Math.random() * dailyQuotes.length)]);

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (pomodoroActive && pomodoroTime > 0) {
      interval = setInterval(() => {
        setPomodoroTime(time => time - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [pomodoroActive, pomodoroTime]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  const completedTasks = todayTasks.filter(task => task.completed).length;
  const totalTasks = todayTasks.length;
  const taskProgress = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  const completedHabits = habits.filter(habit => habit.completed).length;
  const totalHabits = habits.length;
  const habitProgress = totalHabits > 0 ? (completedHabits / totalHabits) * 100 : 0;

  const focusTimeToday = 4.5; // hours
  const goalProgress = 68; // percentage

  const fabActions = [
    { icon: PlusCircle, label: 'Add Task', color: 'from-blue-500 to-cyan-500', action: () => navigate('/tasks') },
    { icon: Edit3, label: 'Journal', color: 'from-purple-500 to-pink-500', action: () => navigate('/journal') },
    { icon: Target, label: 'Add Goal', color: 'from-orange-500 to-red-500', action: () => navigate('/goals') },
    { icon: Coffee, label: 'Log Habit', color: 'from-green-500 to-emerald-500', action: () => navigate('/habits') },
    { icon: Mic, label: 'Voice Note', color: 'from-indigo-500 to-purple-500', action: () => {} },
    { icon: Camera, label: 'Quick Capture', color: 'from-pink-500 to-rose-500', action: () => {} }
  ];

  const toggleTask = (taskId: number) => {
    setTodayTasks(tasks => 
      tasks.map(task => 
        task.id === taskId ? { ...task, completed: !task.completed } : task
      )
    );
  };

  const toggleHabit = (habitName: string) => {
    setHabits(habits => 
      habits.map(habit => 
        habit.name === habitName ? { ...habit, completed: !habit.completed } : habit
      )
    );
  };

  return (
    <div className={`min-h-screen transition-all duration-500 ${
      isDarkMode 
        ? 'bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900' 
        : 'bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50'
    }`}>
      {/* Background Blur Overlay */}
      <div className="fixed inset-0 bg-gradient-to-br from-white/20 via-purple-500/10 to-blue-500/20 backdrop-blur-3xl" />
      
      <div className="relative z-10 pb-24">
        {/* Top Navigation */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="sticky top-0 z-50 backdrop-blur-xl bg-white/10 border-b border-white/20"
        >
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-2xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                <User className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {currentTime.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' })}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {getGreeting()}, {userProfile?.name || 'User'}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsDarkMode(!isDarkMode)}
              className="rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20"
            >
              {isDarkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
            </Button>
          </div>
        </motion.div>

        {/* Daily Quote */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mx-4 mt-4 p-4 rounded-3xl bg-white/20 backdrop-blur-xl border border-white/30"
        >
          <div className="flex items-start space-x-3">
            <Quote className="w-5 h-5 text-purple-500 mt-1 flex-shrink-0" />
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 leading-relaxed">
              {dailyQuote}
            </p>
          </div>
        </motion.div>

        {/* Quick Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-2 gap-3 p-4"
        >
          <div className="rounded-3xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 backdrop-blur-xl border border-white/30 p-4">
            <div className="flex items-center justify-between mb-2">
              <CheckCircle className="w-5 h-5 text-blue-500" />
              <span className="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                {completedTasks}/{totalTasks}
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Tasks Done</p>
            <div className="mt-2 bg-white/20 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${taskProgress}%` }}
              />
            </div>
          </div>

          <div className="rounded-3xl bg-gradient-to-br from-pink-500/20 to-rose-500/20 backdrop-blur-xl border border-white/30 p-4">
            <div className="flex items-center justify-between mb-2">
              <Heart className="w-5 h-5 text-pink-500" />
              <span className="text-2xl">{todayMood.emoji}</span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Mood</p>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{todayMood.mood}</p>
          </div>

          <div className="rounded-3xl bg-gradient-to-br from-purple-500/20 to-indigo-500/20 backdrop-blur-xl border border-white/30 p-4">
            <div className="flex items-center justify-between mb-2">
              <Timer className="w-5 h-5 text-purple-500" />
              <span className="text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-full">
                {focusTimeToday}h
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Focus Time</p>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Today</p>
          </div>

          <div className="rounded-3xl bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-xl border border-white/30 p-4">
            <div className="flex items-center justify-between mb-2">
              <Zap className="w-5 h-5 text-green-500" />
              <span className="text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full">
                {completedHabits}/{totalHabits}
              </span>
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Habits</p>
            <div className="mt-2 bg-white/20 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${habitProgress}%` }}
              />
            </div>
          </div>
        </motion.div>

        {/* Pomodoro Focus Block */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mx-4 mb-4"
        >
          <div className="rounded-3xl bg-gradient-to-br from-orange-500/20 to-red-500/20 backdrop-blur-xl border border-white/30 p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-bold text-gray-800 dark:text-white">Focus Session</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">{currentTask}</p>
              </div>
              <Button
                onClick={() => setPomodoroActive(!pomodoroActive)}
                className={`rounded-2xl w-12 h-12 ${
                  pomodoroActive
                    ? 'bg-gradient-to-r from-red-500 to-pink-500'
                    : 'bg-gradient-to-r from-green-500 to-emerald-500'
                }`}
              >
                {pomodoroActive ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
              </Button>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-gray-800 dark:text-white mb-2">
                {formatTime(pomodoroTime)}
              </div>
              <div className="bg-white/20 rounded-full h-3 mb-4">
                <div
                  className="bg-gradient-to-r from-orange-500 to-red-500 h-3 rounded-full transition-all duration-1000"
                  style={{ width: `${((25 * 60 - pomodoroTime) / (25 * 60)) * 100}%` }}
                />
              </div>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {pomodoroActive ? 'Focus time active' : 'Ready to focus'}
              </p>
            </div>
          </div>
        </motion.div>

        {/* Daily Tasks */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mx-4 mb-4"
        >
          <div className="rounded-3xl bg-white/20 backdrop-blur-xl border border-white/30 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white">Today's Tasks</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/tasks')}
                className="rounded-2xl text-purple-600 hover:bg-purple-100"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
            <div className="space-y-3">
              {todayTasks.slice(0, 4).map((task, index) => (
                <motion.div
                  key={task.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 * index }}
                  className="flex items-center space-x-3 p-3 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20"
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleTask(task.id)}
                    className="p-0 h-auto"
                  >
                    {task.completed ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <Circle className="w-5 h-5 text-gray-400" />
                    )}
                  </Button>
                  <div className="flex-1">
                    <p className={`text-sm font-medium ${
                      task.completed
                        ? 'text-gray-500 line-through'
                        : 'text-gray-700 dark:text-gray-300'
                    }`}>
                      {task.title}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        task.priority === 'high'
                          ? 'bg-red-100 text-red-600'
                          : task.priority === 'medium'
                          ? 'bg-yellow-100 text-yellow-600'
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        {task.priority}
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Routine Tracker */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mx-4 mb-4"
        >
          <div className="rounded-3xl bg-gradient-to-br from-indigo-500/20 to-purple-500/20 backdrop-blur-xl border border-white/30 p-6">
            <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4">Daily Routines</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-2 rounded-2xl bg-gradient-to-br from-yellow-400 to-orange-500 flex items-center justify-center">
                  <Sun className="w-8 h-8 text-white" />
                </div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Morning</p>
                <div className="mt-2 bg-white/20 rounded-full h-2">
                  <div className="bg-gradient-to-r from-yellow-400 to-orange-500 h-2 rounded-full w-3/4" />
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">3/4 completed</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-2 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                  <Moon className="w-8 h-8 text-white" />
                </div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Evening</p>
                <div className="mt-2 bg-white/20 rounded-full h-2">
                  <div className="bg-gradient-to-r from-indigo-500 to-purple-600 h-2 rounded-full w-1/2" />
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">2/4 completed</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Mood & Journal Quick Entry */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mx-4 mb-4"
        >
          <div className="rounded-3xl bg-gradient-to-br from-pink-500/20 to-rose-500/20 backdrop-blur-xl border border-white/30 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white">Mood & Journal</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/journal')}
                className="rounded-2xl text-pink-600 hover:bg-pink-100"
              >
                <Edit3 className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex items-center space-x-4 mb-4">
              <div className="text-4xl">{todayMood.emoji}</div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Feeling {todayMood.mood}
                </p>
                <div className="mt-2 bg-white/20 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-pink-500 to-rose-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${todayMood.energy}%` }}
                  />
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Energy: {todayMood.energy}%
                </p>
              </div>
            </div>
            <Button
              onClick={() => navigate('/journal')}
              className="w-full rounded-2xl bg-gradient-to-r from-pink-500 to-rose-500 text-white"
            >
              <BookOpen className="w-4 h-4 mr-2" />
              Quick Journal Entry
            </Button>
          </div>
        </motion.div>

        {/* Goal Progress Ring */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="mx-4 mb-4"
        >
          <div className="rounded-3xl bg-gradient-to-br from-emerald-500/20 to-teal-500/20 backdrop-blur-xl border border-white/30 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white">Goal Progress</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/goals')}
                className="rounded-2xl text-emerald-600 hover:bg-emerald-100"
              >
                <Target className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex items-center justify-center">
              <div className="relative w-32 h-32">
                <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                  <circle
                    cx="60"
                    cy="60"
                    r="50"
                    stroke="currentColor"
                    strokeWidth="8"
                    fill="none"
                    className="text-white/20"
                  />
                  <circle
                    cx="60"
                    cy="60"
                    r="50"
                    stroke="url(#goalGradient)"
                    strokeWidth="8"
                    fill="none"
                    strokeLinecap="round"
                    strokeDasharray={`${2 * Math.PI * 50}`}
                    strokeDashoffset={`${2 * Math.PI * 50 * (1 - goalProgress / 100)}`}
                    className="transition-all duration-1000"
                  />
                  <defs>
                    <linearGradient id="goalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#10b981" />
                      <stop offset="100%" stopColor="#14b8a6" />
                    </linearGradient>
                  </defs>
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-800 dark:text-white">
                      {goalProgress}%
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      Monthly Goals
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Fitness Goal</span>
                <span className="font-medium text-gray-800 dark:text-white">85%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Learning Goal</span>
                <span className="font-medium text-gray-800 dark:text-white">72%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Career Goal</span>
                <span className="font-medium text-gray-800 dark:text-white">45%</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Habits Quick View */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mx-4 mb-20"
        >
          <div className="rounded-3xl bg-white/20 backdrop-blur-xl border border-white/30 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white">Today's Habits</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/habits')}
                className="rounded-2xl text-green-600 hover:bg-green-100"
              >
                <Sparkles className="w-4 h-4" />
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-3">
              {habits.map((habit, index) => (
                <motion.div
                  key={habit.name}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.1 * index }}
                  onClick={() => toggleHabit(habit.name)}
                  className="p-3 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 cursor-pointer hover:bg-white/20 transition-all"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      habit.completed
                        ? 'bg-green-500 border-green-500'
                        : 'border-gray-300'
                    }`}>
                      {habit.completed && <Check className="w-3 h-3 text-white" />}
                    </div>
                    <span className="text-xs font-medium text-orange-600 bg-orange-100 px-2 py-1 rounded-full">
                      {habit.streak}🔥
                    </span>
                  </div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {habit.name}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Floating Action Button */}
      <motion.div
        className="fixed bottom-24 right-6 z-50"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 1, type: "spring", stiffness: 260, damping: 20 }}
      >
        <AnimatePresence>
          {fabOpen && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute bottom-16 right-0 space-y-3"
            >
              {fabActions.map((action, index) => (
                <motion.div
                  key={action.label}
                  initial={{ opacity: 0, scale: 0, x: 20 }}
                  animate={{ opacity: 1, scale: 1, x: 0 }}
                  exit={{ opacity: 0, scale: 0, x: 20 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center space-x-3"
                >
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full border border-white/30">
                    {action.label}
                  </span>
                  <Button
                    onClick={() => {
                      action.action();
                      setFabOpen(false);
                    }}
                    className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${action.color} shadow-lg`}
                  >
                    <action.icon className="w-5 h-5" />
                  </Button>
                </motion.div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        <Button
          onClick={() => setFabOpen(!fabOpen)}
          className={`w-14 h-14 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 shadow-xl transition-transform ${
            fabOpen ? 'rotate-45' : 'rotate-0'
          }`}
        >
          <Plus className="w-6 h-6" />
        </Button>
      </motion.div>

      {/* Bottom Navigation */}
      <motion.div
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="fixed bottom-0 left-0 right-0 z-40 backdrop-blur-xl bg-white/10 border-t border-white/20"
      >
        <div className="flex items-center justify-around py-3 px-6">
          {[
            { icon: Home, label: 'Home', active: true, path: '/dashboard' },
            { icon: CheckCircle, label: 'Tasks', active: false, path: '/tasks' },
            { icon: BarChart3, label: 'Analytics', active: false, path: '/analytics' },
            { icon: Heart, label: 'Wellness', active: false, path: '/wellness' },
            { icon: Settings, label: 'Settings', active: false, path: '/settings' }
          ].map((tab) => (
            <Button
              key={tab.label}
              variant="ghost"
              onClick={() => navigate(tab.path)}
              className={`flex flex-col items-center space-y-1 p-2 rounded-2xl transition-all ${
                tab.active
                  ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <tab.icon className={`w-5 h-5 ${tab.active ? 'text-purple-600' : ''}`} />
              <span className="text-xs font-medium">{tab.label}</span>
              {tab.active && (
                <motion.div
                  layoutId="activeTab"
                  className="w-1 h-1 bg-purple-600 rounded-full"
                />
              )}
            </Button>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default PremiumDashboard;
