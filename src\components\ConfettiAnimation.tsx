import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface ConfettiProps {
  trigger: boolean;
  onComplete?: () => void;
  colors?: string[];
  particleCount?: number;
  duration?: number;
}

interface Particle {
  id: number;
  x: number;
  y: number;
  color: string;
  size: number;
  rotation: number;
  velocityX: number;
  velocityY: number;
  gravity: number;
  shape: 'circle' | 'square' | 'triangle';
}

const ConfettiAnimation: React.FC<ConfettiProps> = ({
  trigger,
  onComplete,
  colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'],
  particleCount = 50,
  duration = 3000
}) => {
  const [particles, setParticles] = useState<Particle[]>([]);
  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    if (trigger) {
      createConfetti();
    }
  }, [trigger]);

  const createConfetti = () => {
    setIsActive(true);
    const newParticles: Particle[] = [];

    for (let i = 0; i < particleCount; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * window.innerWidth,
        y: -10,
        color: colors[Math.floor(Math.random() * colors.length)],
        size: Math.random() * 8 + 4,
        rotation: Math.random() * 360,
        velocityX: (Math.random() - 0.5) * 10,
        velocityY: Math.random() * 5 + 2,
        gravity: Math.random() * 0.3 + 0.1,
        shape: ['circle', 'square', 'triangle'][Math.floor(Math.random() * 3)] as 'circle' | 'square' | 'triangle'
      });
    }

    setParticles(newParticles);

    // Clean up after animation
    setTimeout(() => {
      setIsActive(false);
      setParticles([]);
      onComplete?.();
    }, duration);
  };

  const getShapeComponent = (particle: Particle) => {
    const style = {
      width: particle.size,
      height: particle.size,
      backgroundColor: particle.color,
      position: 'absolute' as const,
      left: particle.x,
      top: particle.y,
      transform: `rotate(${particle.rotation}deg)`,
    };

    switch (particle.shape) {
      case 'circle':
        return <div style={{ ...style, borderRadius: '50%' }} />;
      case 'square':
        return <div style={style} />;
      case 'triangle':
        return (
          <div
            style={{
              ...style,
              width: 0,
              height: 0,
              backgroundColor: 'transparent',
              borderLeft: `${particle.size / 2}px solid transparent`,
              borderRight: `${particle.size / 2}px solid transparent`,
              borderBottom: `${particle.size}px solid ${particle.color}`,
            }}
          />
        );
      default:
        return <div style={style} />;
    }
  };

  return (
    <AnimatePresence>
      {isActive && (
        <div className="fixed inset-0 pointer-events-none z-50 overflow-hidden">
          {particles.map((particle) => (
            <motion.div
              key={particle.id}
              initial={{
                x: particle.x,
                y: particle.y,
                rotate: particle.rotation,
                opacity: 1,
              }}
              animate={{
                x: particle.x + particle.velocityX * 100,
                y: window.innerHeight + 100,
                rotate: particle.rotation + 720,
                opacity: 0,
              }}
              transition={{
                duration: duration / 1000,
                ease: 'easeOut',
              }}
              className="absolute"
            >
              {getShapeComponent(particle)}
            </motion.div>
          ))}
        </div>
      )}
    </AnimatePresence>
  );
};

// Success Animation Component
export const SuccessAnimation: React.FC<{ trigger: boolean; onComplete?: () => void }> = ({
  trigger,
  onComplete
}) => {
  return (
    <AnimatePresence>
      {trigger && (
        <>
          {/* Confetti */}
          <ConfettiAnimation
            trigger={trigger}
            onComplete={onComplete}
            colors={['#10B981', '#34D399', '#6EE7B7', '#A7F3D0']}
            particleCount={30}
          />
          
          {/* Success Checkmark */}
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ duration: 0.5, type: 'spring', stiffness: 200 }}
            className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none"
          >
            <div className="bg-green-500 rounded-full p-6 shadow-2xl">
              <motion.svg
                width="60"
                height="60"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                strokeWidth="3"
                strokeLinecap="round"
                strokeLinejoin="round"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <motion.path d="M20 6L9 17l-5-5" />
              </motion.svg>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

// Level Up Animation
export const LevelUpAnimation: React.FC<{ trigger: boolean; level?: number; onComplete?: () => void }> = ({
  trigger,
  level = 1,
  onComplete
}) => {
  return (
    <AnimatePresence>
      {trigger && (
        <>
          <ConfettiAnimation
            trigger={trigger}
            onComplete={onComplete}
            colors={['#8B5CF6', '#A855F7', '#C084FC', '#DDD6FE']}
            particleCount={60}
            duration={4000}
          />
          
          <motion.div
            initial={{ scale: 0, opacity: 0, y: 50 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0, opacity: 0, y: -50 }}
            transition={{ duration: 0.8, type: 'spring', stiffness: 150 }}
            className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none"
          >
            <div className="text-center">
              <motion.div
                initial={{ rotate: -10 }}
                animate={{ rotate: 10 }}
                transition={{ duration: 0.5, repeat: 3, repeatType: 'reverse' }}
                className="text-6xl mb-4"
              >
                🎉
              </motion.div>
              <motion.h2
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2"
              >
                LEVEL UP!
              </motion.h2>
              <motion.p
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="text-xl text-gray-600"
              >
                You've reached level {level}!
              </motion.p>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

// Streak Animation
export const StreakAnimation: React.FC<{ trigger: boolean; streak?: number; onComplete?: () => void }> = ({
  trigger,
  streak = 1,
  onComplete
}) => {
  return (
    <AnimatePresence>
      {trigger && (
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0, opacity: 0 }}
          transition={{ duration: 0.6, type: 'spring' }}
          className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none"
          onAnimationComplete={() => {
            setTimeout(onComplete, 2000);
          }}
        >
          <div className="text-center">
            <motion.div
              animate={{ 
                scale: [1, 1.2, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ duration: 1, repeat: 1 }}
              className="text-6xl mb-4"
            >
              🔥
            </motion.div>
            <motion.h3
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="text-3xl font-bold text-orange-500 mb-2"
            >
              {streak} Day Streak!
            </motion.h3>
            <motion.p
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="text-lg text-gray-600"
            >
              Keep it up! 🚀
            </motion.p>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Goal Achievement Animation
export const GoalAchievementAnimation: React.FC<{ 
  trigger: boolean; 
  goalName?: string; 
  onComplete?: () => void 
}> = ({
  trigger,
  goalName = 'Goal',
  onComplete
}) => {
  return (
    <AnimatePresence>
      {trigger && (
        <>
          <ConfettiAnimation
            trigger={trigger}
            onComplete={onComplete}
            colors={['#F59E0B', '#FBBF24', '#FCD34D', '#FDE68A']}
            particleCount={80}
            duration={5000}
          />
          
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ duration: 0.8, type: 'spring', stiffness: 120 }}
            className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none"
          >
            <div className="text-center max-w-md mx-auto p-8">
              <motion.div
                animate={{ 
                  scale: [1, 1.3, 1],
                  rotate: [0, 360]
                }}
                transition={{ duration: 2, repeat: 1 }}
                className="text-8xl mb-6"
              >
                🏆
              </motion.div>
              <motion.h2
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="text-4xl font-bold bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent mb-4"
              >
                GOAL ACHIEVED!
              </motion.h2>
              <motion.p
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="text-xl text-gray-700 mb-2"
              >
                🎯 {goalName}
              </motion.p>
              <motion.p
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.8 }}
                className="text-lg text-gray-600"
              >
                Congratulations on your achievement! 🌟
              </motion.p>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default ConfettiAnimation;
