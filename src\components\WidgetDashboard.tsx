import React, { useState, useEffect } from 'react';
import { motion, Reorder, AnimatePresence } from 'framer-motion';
import { 
  CheckSquare, 
  Target, 
  Clock, 
  TrendingUp, 
  Calendar, 
  Heart, 
  DollarSign, 
  BookOpen,
  Settings,
  Plus,
  Grip,
  Eye,
  EyeOff
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import DataSyncService from '../services/DataSyncService';
import ThemeEngine from '../services/ThemeEngine';

export interface Widget {
  id: string;
  type: string;
  title: string;
  icon: React.ReactNode;
  size: 'small' | 'medium' | 'large';
  visible: boolean;
  position: number;
  data?: any;
}

const availableWidgets: Omit<Widget, 'id' | 'position' | 'visible'>[] = [
  {
    type: 'tasks',
    title: 'Quick Tasks',
    icon: <CheckSquare className="w-5 h-5" />,
    size: 'medium'
  },
  {
    type: 'goals',
    title: 'Goal Progress',
    icon: <Target className="w-5 h-5" />,
    size: 'large'
  },
  {
    type: 'timer',
    title: 'Focus Timer',
    icon: <Clock className="w-5 h-5" />,
    size: 'small'
  },
  {
    type: 'analytics',
    title: 'Productivity Stats',
    icon: <TrendingUp className="w-5 h-5" />,
    size: 'medium'
  },
  {
    type: 'calendar',
    title: 'Today\'s Events',
    icon: <Calendar className="w-5 h-5" />,
    size: 'medium'
  },
  {
    type: 'mood',
    title: 'Mood Tracker',
    icon: <Heart className="w-5 h-5" />,
    size: 'small'
  },
  {
    type: 'finance',
    title: 'Budget Overview',
    icon: <DollarSign className="w-5 h-5" />,
    size: 'medium'
  },
  {
    type: 'journal',
    title: 'Quick Journal',
    icon: <BookOpen className="w-5 h-5" />,
    size: 'large'
  }
];

const WidgetDashboard: React.FC = () => {
  const [widgets, setWidgets] = useState<Widget[]>([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [showAddWidget, setShowAddWidget] = useState(false);

  useEffect(() => {
    loadWidgets();
  }, []);

  const loadWidgets = () => {
    const savedWidgets = DataSyncService.getData('dashboardWidgets');
    if (savedWidgets.length > 0) {
      setWidgets(savedWidgets);
    } else {
      // Initialize with default widgets
      const defaultWidgets: Widget[] = [
        { ...availableWidgets[0], id: '1', position: 0, visible: true },
        { ...availableWidgets[1], id: '2', position: 1, visible: true },
        { ...availableWidgets[2], id: '3', position: 2, visible: true },
        { ...availableWidgets[3], id: '4', position: 3, visible: true }
      ];
      setWidgets(defaultWidgets);
      saveWidgets(defaultWidgets);
    }
  };

  const saveWidgets = (updatedWidgets: Widget[]) => {
    DataSyncService.saveData('dashboardWidgets', updatedWidgets);
  };

  const handleReorder = (newOrder: Widget[]) => {
    const updatedWidgets = newOrder.map((widget, index) => ({
      ...widget,
      position: index
    }));
    setWidgets(updatedWidgets);
    saveWidgets(updatedWidgets);
    ThemeEngine.triggerHaptic('light');
  };

  const toggleWidgetVisibility = (widgetId: string) => {
    const updatedWidgets = widgets.map(widget =>
      widget.id === widgetId ? { ...widget, visible: !widget.visible } : widget
    );
    setWidgets(updatedWidgets);
    saveWidgets(updatedWidgets);
    ThemeEngine.triggerHaptic('medium');
  };

  const addWidget = (widgetType: string) => {
    const template = availableWidgets.find(w => w.type === widgetType);
    if (!template) return;

    const newWidget: Widget = {
      ...template,
      id: `${widgetType}-${Date.now()}`,
      position: widgets.length,
      visible: true
    };

    const updatedWidgets = [...widgets, newWidget];
    setWidgets(updatedWidgets);
    saveWidgets(updatedWidgets);
    setShowAddWidget(false);
    ThemeEngine.triggerHaptic('heavy');
    ThemeEngine.playSound('click');
  };

  const removeWidget = (widgetId: string) => {
    const updatedWidgets = widgets.filter(widget => widget.id !== widgetId);
    setWidgets(updatedWidgets);
    saveWidgets(updatedWidgets);
    ThemeEngine.triggerHaptic('medium');
  };

  const getWidgetContent = (widget: Widget) => {
    switch (widget.type) {
      case 'tasks':
        return <TasksWidget />;
      case 'goals':
        return <GoalsWidget />;
      case 'timer':
        return <TimerWidget />;
      case 'analytics':
        return <AnalyticsWidget />;
      case 'calendar':
        return <CalendarWidget />;
      case 'mood':
        return <MoodWidget />;
      case 'finance':
        return <FinanceWidget />;
      case 'journal':
        return <JournalWidget />;
      default:
        return <div className="p-4 text-center text-gray-500">Widget content</div>;
    }
  };

  const getWidgetSize = (size: string) => {
    switch (size) {
      case 'small':
        return 'col-span-1 row-span-1';
      case 'medium':
        return 'col-span-2 row-span-1';
      case 'large':
        return 'col-span-2 row-span-2';
      default:
        return 'col-span-1 row-span-1';
    }
  };

  const visibleWidgets = widgets.filter(widget => widget.visible);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 p-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
          Dashboard
        </h1>
        <div className="flex space-x-2">
          <Button
            onClick={() => setShowAddWidget(true)}
            className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Widget
          </Button>
          <Button
            onClick={() => setIsEditMode(!isEditMode)}
            variant={isEditMode ? "default" : "outline"}
            className={isEditMode ? "bg-gradient-to-r from-orange-500 to-red-500" : ""}
          >
            <Settings className="w-4 h-4 mr-2" />
            {isEditMode ? 'Done' : 'Edit'}
          </Button>
        </div>
      </div>

      {/* Widget Grid */}
      <Reorder.Group
        axis="y"
        values={visibleWidgets}
        onReorder={handleReorder}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 auto-rows-min"
      >
        <AnimatePresence>
          {visibleWidgets.map((widget) => (
            <Reorder.Item
              key={widget.id}
              value={widget}
              className={`${getWidgetSize(widget.size)} relative`}
              whileDrag={{ scale: 1.05, zIndex: 50 }}
              dragListener={isEditMode}
            >
              <motion.div
                layout
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="h-full"
              >
                <Card className="h-full shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="flex items-center text-sm font-medium">
                        {widget.icon}
                        <span className="ml-2">{widget.title}</span>
                      </CardTitle>
                      {isEditMode && (
                        <div className="flex space-x-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => toggleWidgetVisibility(widget.id)}
                            className="h-6 w-6 p-0"
                          >
                            {widget.visible ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => removeWidget(widget.id)}
                            className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                          >
                            ×
                          </Button>
                          <div className="cursor-grab active:cursor-grabbing">
                            <Grip className="w-3 h-3 text-gray-400" />
                          </div>
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {getWidgetContent(widget)}
                  </CardContent>
                </Card>
              </motion.div>
            </Reorder.Item>
          ))}
        </AnimatePresence>
      </Reorder.Group>

      {/* Add Widget Modal */}
      <AnimatePresence>
        {showAddWidget && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowAddWidget(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-2xl p-6 w-full max-w-md max-h-[80vh] overflow-y-auto"
            >
              <h3 className="text-xl font-bold mb-4">Add Widget</h3>
              <div className="grid grid-cols-1 gap-3">
                {availableWidgets.map((widget) => {
                  const isAlreadyAdded = widgets.some(w => w.type === widget.type);
                  return (
                    <Button
                      key={widget.type}
                      onClick={() => addWidget(widget.type)}
                      disabled={isAlreadyAdded}
                      variant="outline"
                      className="flex items-center justify-start p-4 h-auto"
                    >
                      {widget.icon}
                      <div className="ml-3 text-left">
                        <div className="font-medium">{widget.title}</div>
                        <div className="text-xs text-gray-500 capitalize">{widget.size} widget</div>
                      </div>
                      {isAlreadyAdded && (
                        <span className="ml-auto text-xs text-gray-400">Added</span>
                      )}
                    </Button>
                  );
                })}
              </div>
              <Button
                onClick={() => setShowAddWidget(false)}
                variant="outline"
                className="w-full mt-4"
              >
                Cancel
              </Button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Widget Components (simplified versions)
const TasksWidget = () => (
  <div className="space-y-2">
    <div className="flex items-center space-x-2 text-sm">
      <input type="checkbox" className="rounded" />
      <span>Review project proposal</span>
    </div>
    <div className="flex items-center space-x-2 text-sm">
      <input type="checkbox" className="rounded" />
      <span>Call client meeting</span>
    </div>
    <div className="text-xs text-gray-500 mt-2">2 of 5 tasks completed</div>
  </div>
);

const GoalsWidget = () => (
  <div className="space-y-3">
    <div>
      <div className="flex justify-between text-sm mb-1">
        <span>Fitness Goal</span>
        <span>75%</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div className="bg-green-500 h-2 rounded-full" style={{ width: '75%' }}></div>
      </div>
    </div>
    <div>
      <div className="flex justify-between text-sm mb-1">
        <span>Learning Goal</span>
        <span>45%</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div className="bg-blue-500 h-2 rounded-full" style={{ width: '45%' }}></div>
      </div>
    </div>
  </div>
);

const TimerWidget = () => (
  <div className="text-center">
    <div className="text-2xl font-bold text-purple-600">25:00</div>
    <div className="text-xs text-gray-500 mb-2">Focus Session</div>
    <Button size="sm" className="w-full">Start</Button>
  </div>
);

const AnalyticsWidget = () => (
  <div className="space-y-2">
    <div className="flex justify-between text-sm">
      <span>Today's Focus</span>
      <span className="font-bold text-green-600">4.5h</span>
    </div>
    <div className="flex justify-between text-sm">
      <span>Tasks Done</span>
      <span className="font-bold text-blue-600">12</span>
    </div>
    <div className="flex justify-between text-sm">
      <span>Streak</span>
      <span className="font-bold text-orange-600">7 days</span>
    </div>
  </div>
);

const CalendarWidget = () => (
  <div className="space-y-2">
    <div className="text-sm font-medium">Today's Events</div>
    <div className="space-y-1">
      <div className="text-xs bg-blue-100 text-blue-800 p-1 rounded">
        9:00 AM - Team Meeting
      </div>
      <div className="text-xs bg-green-100 text-green-800 p-1 rounded">
        2:00 PM - Client Call
      </div>
    </div>
  </div>
);

const MoodWidget = () => (
  <div className="text-center">
    <div className="text-2xl mb-2">😊</div>
    <div className="text-xs text-gray-500">Feeling Good</div>
    <Button size="sm" className="w-full mt-2">Log Mood</Button>
  </div>
);

const FinanceWidget = () => (
  <div className="space-y-2">
    <div className="flex justify-between text-sm">
      <span>Monthly Budget</span>
      <span className="font-bold">$2,340 / $3,000</span>
    </div>
    <div className="w-full bg-gray-200 rounded-full h-2">
      <div className="bg-green-500 h-2 rounded-full" style={{ width: '78%' }}></div>
    </div>
    <div className="text-xs text-gray-500">$660 remaining</div>
  </div>
);

const JournalWidget = () => (
  <div>
    <div className="text-sm font-medium mb-2">Quick Reflection</div>
    <textarea 
      className="w-full text-xs p-2 border rounded resize-none" 
      rows={3}
      placeholder="How was your day?"
    />
    <Button size="sm" className="w-full mt-2">Save Entry</Button>
  </div>
);

export default WidgetDashboard;
