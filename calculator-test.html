<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FOCOS Financial Calculator Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .calculator {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .input-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 16px;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        .result {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        h1, h2 {
            text-align: center;
            margin-bottom: 20px;
        }
        .success {
            color: #4ade80;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🚀 FOCOS Financial Calculator Test Suite</h1>
    <p style="text-align: center;">Test all the financial calculators to ensure they're working perfectly!</p>

    <!-- Investment ROI Calculator -->
    <div class="calculator">
        <h2>📈 Investment ROI Calculator</h2>
        <div class="grid">
            <div class="input-group">
                <label>Initial Investment ($)</label>
                <input type="number" id="investInitial" placeholder="10000" value="10000">
            </div>
            <div class="input-group">
                <label>Monthly Contribution ($)</label>
                <input type="number" id="investMonthly" placeholder="500" value="500">
            </div>
            <div class="input-group">
                <label>Annual Return (%)</label>
                <input type="number" id="investReturn" placeholder="7" value="7">
            </div>
            <div class="input-group">
                <label>Time Period (years)</label>
                <input type="number" id="investYears" placeholder="10" value="10">
            </div>
        </div>
        <button onclick="calculateInvestment()">Calculate Investment ROI</button>
        <div id="investResult"></div>
    </div>

    <!-- Loan Calculator -->
    <div class="calculator">
        <h2>🏠 Loan Payment Calculator</h2>
        <div class="grid">
            <div class="input-group">
                <label>Loan Amount ($)</label>
                <input type="number" id="loanPrincipal" placeholder="250000" value="250000">
            </div>
            <div class="input-group">
                <label>Interest Rate (%)</label>
                <input type="number" id="loanRate" placeholder="3.5" value="3.5">
            </div>
            <div class="input-group">
                <label>Loan Term (years)</label>
                <input type="number" id="loanTerm" placeholder="30" value="30">
            </div>
        </div>
        <button onclick="calculateLoan()">Calculate Loan Payment</button>
        <div id="loanResult"></div>
    </div>

    <!-- Tax Calculator -->
    <div class="calculator">
        <h2>📊 Tax Calculator</h2>
        <div class="grid">
            <div class="input-group">
                <label>Annual Income ($)</label>
                <input type="number" id="taxIncome" placeholder="75000" value="75000">
            </div>
            <div class="input-group">
                <label>Filing Status</label>
                <select id="taxStatus">
                    <option value="single">Single</option>
                    <option value="marriedJoint">Married Filing Jointly</option>
                    <option value="marriedSeparate">Married Filing Separately</option>
                    <option value="headOfHousehold">Head of Household</option>
                </select>
            </div>
            <div class="input-group">
                <label>Deductions ($)</label>
                <input type="number" id="taxDeductions" placeholder="13850" value="13850">
            </div>
        </div>
        <button onclick="calculateTax()">Calculate Tax</button>
        <div id="taxResult"></div>
    </div>

    <!-- Budget Planner -->
    <div class="calculator">
        <h2>💰 50/30/20 Budget Planner</h2>
        <div class="input-group">
            <label>Monthly After-Tax Income ($)</label>
            <input type="number" id="budgetIncome" placeholder="5000" value="5000">
        </div>
        <button onclick="calculateBudget()">Calculate Budget Plan</button>
        <div id="budgetResult"></div>
    </div>

    <script>
        // Investment ROI Calculator
        function calculateInvestment() {
            const initial = parseFloat(document.getElementById('investInitial').value);
            const monthly = parseFloat(document.getElementById('investMonthly').value);
            const annualReturn = parseFloat(document.getElementById('investReturn').value) / 100;
            const years = parseFloat(document.getElementById('investYears').value);

            if (initial >= 0 && monthly >= 0 && annualReturn >= 0 && years > 0) {
                const monthlyReturn = annualReturn / 12;
                const totalMonths = years * 12;
                
                const futureValueInitial = initial * Math.pow(1 + annualReturn, years);
                const futureValueMonthly = monthly * (Math.pow(1 + monthlyReturn, totalMonths) - 1) / monthlyReturn;
                
                const totalInvested = initial + (monthly * totalMonths);
                const finalAmount = futureValueInitial + futureValueMonthly;
                const totalReturn = finalAmount - totalInvested;
                const roi = ((finalAmount - totalInvested) / totalInvested) * 100;

                document.getElementById('investResult').innerHTML = `
                    <div class="result">
                        <h3 class="success">✅ Investment Results:</h3>
                        <p><strong>Final Amount:</strong> $${finalAmount.toFixed(2)}</p>
                        <p><strong>Total Invested:</strong> $${totalInvested.toFixed(2)}</p>
                        <p><strong>Total Return:</strong> $${totalReturn.toFixed(2)}</p>
                        <p><strong>ROI:</strong> ${roi.toFixed(2)}%</p>
                    </div>
                `;
            } else {
                alert('Please enter valid numbers for all fields!');
            }
        }

        // Loan Payment Calculator
        function calculateLoan() {
            const principal = parseFloat(document.getElementById('loanPrincipal').value);
            const annualRate = parseFloat(document.getElementById('loanRate').value) / 100;
            const years = parseFloat(document.getElementById('loanTerm').value);

            if (principal > 0 && annualRate >= 0 && years > 0) {
                const monthlyRate = annualRate / 12;
                const totalPayments = years * 12;
                
                let monthlyPayment;
                if (monthlyRate === 0) {
                    monthlyPayment = principal / totalPayments;
                } else {
                    monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, totalPayments)) / 
                                    (Math.pow(1 + monthlyRate, totalPayments) - 1);
                }
                
                const totalPaid = monthlyPayment * totalPayments;
                const totalInterest = totalPaid - principal;

                document.getElementById('loanResult').innerHTML = `
                    <div class="result">
                        <h3 class="success">✅ Loan Results:</h3>
                        <p><strong>Monthly Payment:</strong> $${monthlyPayment.toFixed(2)}</p>
                        <p><strong>Total Paid:</strong> $${totalPaid.toFixed(2)}</p>
                        <p><strong>Total Interest:</strong> $${totalInterest.toFixed(2)}</p>
                        <p><strong>Interest %:</strong> ${((totalInterest / principal) * 100).toFixed(2)}%</p>
                    </div>
                `;
            } else {
                alert('Please enter valid positive numbers for all fields!');
            }
        }

        // Tax Calculator
        function calculateTax() {
            const income = parseFloat(document.getElementById('taxIncome').value);
            const deductions = parseFloat(document.getElementById('taxDeductions').value) || 0;
            
            if (income > 0) {
                const taxableIncome = Math.max(0, income - deductions);
                
                const brackets = [
                    { min: 0, max: 11000, rate: 0.10 },
                    { min: 11000, max: 44725, rate: 0.12 },
                    { min: 44725, max: 95375, rate: 0.22 },
                    { min: 95375, max: 182050, rate: 0.24 },
                    { min: 182050, max: 231250, rate: 0.32 },
                    { min: 231250, max: 578125, rate: 0.35 },
                    { min: 578125, max: Infinity, rate: 0.37 }
                ];

                let tax = 0;
                let remainingIncome = taxableIncome;

                for (const bracket of brackets) {
                    if (remainingIncome <= 0) break;
                    
                    const taxableAtThisBracket = Math.min(remainingIncome, bracket.max - bracket.min);
                    tax += taxableAtThisBracket * bracket.rate;
                    remainingIncome -= taxableAtThisBracket;
                }

                const effectiveRate = (tax / income) * 100;
                const marginalRate = brackets.find(b => taxableIncome > b.min && taxableIncome <= b.max)?.rate * 100 || 0;
                const afterTaxIncome = income - tax;

                document.getElementById('taxResult').innerHTML = `
                    <div class="result">
                        <h3 class="success">✅ Tax Results:</h3>
                        <p><strong>Taxable Income:</strong> $${taxableIncome.toFixed(2)}</p>
                        <p><strong>Federal Tax:</strong> $${tax.toFixed(2)}</p>
                        <p><strong>Effective Rate:</strong> ${effectiveRate.toFixed(2)}%</p>
                        <p><strong>Marginal Rate:</strong> ${marginalRate.toFixed(2)}%</p>
                        <p><strong>After-Tax Income:</strong> $${afterTaxIncome.toFixed(2)}</p>
                    </div>
                `;
            } else {
                alert('Please enter a valid income amount!');
            }
        }

        // Budget Planner
        function calculateBudget() {
            const income = parseFloat(document.getElementById('budgetIncome').value);
            
            if (income > 0) {
                const needs = income * 0.5;
                const wants = income * 0.3;
                const savings = income * 0.2;
                
                document.getElementById('budgetResult').innerHTML = `
                    <div class="result">
                        <h3 class="success">✅ 50/30/20 Budget Breakdown:</h3>
                        <p><strong>Needs (50%):</strong> $${needs.toFixed(2)}</p>
                        <p style="font-size: 14px; color: #ccc;">Housing, utilities, groceries, minimum debt payments</p>
                        <p><strong>Wants (30%):</strong> $${wants.toFixed(2)}</p>
                        <p style="font-size: 14px; color: #ccc;">Entertainment, dining out, hobbies</p>
                        <p><strong>Savings & Debt (20%):</strong> $${savings.toFixed(2)}</p>
                        <p style="font-size: 14px; color: #ccc;">Emergency fund, retirement, extra debt payments</p>
                    </div>
                `;
            } else {
                alert('Please enter a valid monthly income!');
            }
        }

        // Auto-calculate on page load to show examples
        window.onload = function() {
            calculateInvestment();
            calculateLoan();
            calculateTax();
            calculateBudget();
        };
    </script>
</body>
</html>
