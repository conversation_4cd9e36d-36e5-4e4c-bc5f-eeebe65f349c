import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Moon, Sun, Clock, TrendingUp, Calendar, Star,
  Bed, Coffee, Phone, Book, Music, Zap, Brain,
  Heart, Activity, BarChart3, Target, Award
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { GlassmorphCard } from '@/components/ui/glassmorphcard';

interface SleepSession {
  id: string;
  date: string;
  bedtime: string;
  wakeTime: string;
  duration: number; // in hours
  quality: number; // 1-10 scale
  mood: 'excellent' | 'good' | 'fair' | 'poor';
  factors: string[];
  notes?: string;
  deepSleep?: number;
  remSleep?: number;
  interruptions?: number;
}

interface SleepGoal {
  targetBedtime: string;
  targetWakeTime: string;
  targetDuration: number;
  qualityGoal: number;
}

const SleepTracker = () => {
  const [sleepSessions, setSleepSessions] = useState<SleepSession[]>([
    {
      id: '1',
      date: '2024-01-20',
      bedtime: '22:30',
      wakeTime: '06:30',
      duration: 8,
      quality: 8,
      mood: 'good',
      factors: ['consistent routine', 'no caffeine'],
      notes: 'Felt refreshed and energetic',
      deepSleep: 2.5,
      remSleep: 1.8,
      interruptions: 1
    },
    {
      id: '2',
      date: '2024-01-19',
      bedtime: '23:15',
      wakeTime: '07:00',
      duration: 7.75,
      quality: 6,
      mood: 'fair',
      factors: ['late screen time', 'stress'],
      notes: 'Had trouble falling asleep',
      deepSleep: 2.1,
      remSleep: 1.5,
      interruptions: 3
    }
  ]);

  const [sleepGoals, setSleepGoals] = useState<SleepGoal>({
    targetBedtime: '22:30',
    targetWakeTime: '06:30',
    targetDuration: 8,
    qualityGoal: 8
  });

  const [isTracking, setIsTracking] = useState(false);
  const [currentSession, setCurrentSession] = useState<Partial<SleepSession>>({});
  const [selectedFactors, setSelectedFactors] = useState<string[]>([]);

  const sleepFactors = [
    { id: 'caffeine', label: 'Caffeine', icon: Coffee, type: 'negative' },
    { id: 'exercise', label: 'Exercise', icon: Activity, type: 'positive' },
    { id: 'screen-time', label: 'Late Screen Time', icon: Phone, type: 'negative' },
    { id: 'reading', label: 'Reading', icon: Book, type: 'positive' },
    { id: 'music', label: 'Relaxing Music', icon: Music, type: 'positive' },
    { id: 'stress', label: 'Stress', icon: Brain, type: 'negative' },
    { id: 'routine', label: 'Consistent Routine', icon: Clock, type: 'positive' },
    { id: 'temperature', label: 'Room Temperature', icon: Zap, type: 'neutral' }
  ];

  const startSleepTracking = () => {
    const now = new Date();
    setCurrentSession({
      id: Date.now().toString(),
      date: now.toISOString().split('T')[0],
      bedtime: now.toTimeString().slice(0, 5)
    });
    setIsTracking(true);
  };

  const endSleepTracking = () => {
    const now = new Date();
    const wakeTime = now.toTimeString().slice(0, 5);
    
    if (currentSession.bedtime) {
      const bedtime = new Date(`2000-01-01T${currentSession.bedtime}`);
      const wake = new Date(`2000-01-01T${wakeTime}`);
      let duration = (wake.getTime() - bedtime.getTime()) / (1000 * 60 * 60);
      
      if (duration < 0) duration += 24; // Handle overnight sleep
      
      const newSession: SleepSession = {
        ...currentSession as SleepSession,
        wakeTime,
        duration: Math.round(duration * 100) / 100,
        quality: 7, // Default, user can edit
        mood: 'good',
        factors: selectedFactors,
        deepSleep: duration * 0.3,
        remSleep: duration * 0.2,
        interruptions: Math.floor(Math.random() * 3)
      };
      
      setSleepSessions(prev => [newSession, ...prev]);
      setCurrentSession({});
      setSelectedFactors([]);
      setIsTracking(false);
    }
  };

  const calculateAverages = () => {
    if (sleepSessions.length === 0) return { duration: 0, quality: 0, consistency: 0 };
    
    const avgDuration = sleepSessions.reduce((sum, session) => sum + session.duration, 0) / sleepSessions.length;
    const avgQuality = sleepSessions.reduce((sum, session) => sum + session.quality, 0) / sleepSessions.length;
    
    // Calculate consistency based on bedtime variance
    const bedtimes = sleepSessions.map(s => {
      const [hours, minutes] = s.bedtime.split(':').map(Number);
      return hours * 60 + minutes;
    });
    const avgBedtime = bedtimes.reduce((sum, time) => sum + time, 0) / bedtimes.length;
    const variance = bedtimes.reduce((sum, time) => sum + Math.pow(time - avgBedtime, 2), 0) / bedtimes.length;
    const consistency = Math.max(0, 100 - Math.sqrt(variance) / 2);
    
    return {
      duration: Math.round(avgDuration * 100) / 100,
      quality: Math.round(avgQuality * 100) / 100,
      consistency: Math.round(consistency)
    };
  };

  const averages = calculateAverages();

  const SleepChart = () => (
    <div className="h-64 flex items-end justify-between space-x-2">
      {sleepSessions.slice(0, 7).reverse().map((session, index) => (
        <div key={session.id} className="flex flex-col items-center">
          <div className="flex flex-col space-y-1">
            <div 
              className="w-6 bg-blue-500 rounded-t"
              style={{ height: `${(session.duration / 12) * 150}px` }}
              title={`Duration: ${session.duration}h`}
            />
            <div 
              className="w-6 bg-purple-500 rounded-b"
              style={{ height: `${(session.quality / 10) * 50}px` }}
              title={`Quality: ${session.quality}/10`}
            />
          </div>
          <span className="text-xs mt-2">
            {new Date(session.date).toLocaleDateString('en', { weekday: 'short' })}
          </span>
        </div>
      ))}
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-blue-50 dark:from-gray-900 dark:via-indigo-900 dark:to-purple-900 p-6">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            Sleep Tracker
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor and optimize your sleep patterns for better health and productivity
          </p>
        </motion.div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <GlassmorphCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Duration</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{averages.duration}h</p>
              </div>
              <Clock className="w-8 h-8 text-blue-500" />
            </div>
          </GlassmorphCard>

          <GlassmorphCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Quality</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{averages.quality}/10</p>
              </div>
              <Star className="w-8 h-8 text-yellow-500" />
            </div>
          </GlassmorphCard>

          <GlassmorphCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Consistency</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{averages.consistency}%</p>
              </div>
              <Target className="w-8 h-8 text-green-500" />
            </div>
          </GlassmorphCard>

          <GlassmorphCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Sleep Debt</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">-0.5h</p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-500" />
            </div>
          </GlassmorphCard>
        </div>

        {/* Sleep Tracking Controls */}
        <GlassmorphCard className="p-6 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Sleep Tracking
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                {isTracking ? 'Currently tracking your sleep...' : 'Start tracking your sleep session'}
              </p>
            </div>
            <div className="flex space-x-4">
              {!isTracking ? (
                <Button onClick={startSleepTracking} className="flex items-center">
                  <Moon className="w-4 h-4 mr-2" />
                  Start Sleep
                </Button>
              ) : (
                <Button onClick={endSleepTracking} variant="outline" className="flex items-center">
                  <Sun className="w-4 h-4 mr-2" />
                  Wake Up
                </Button>
              )}
            </div>
          </div>
          
          {isTracking && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="mt-6 pt-6 border-t"
            >
              <h3 className="font-medium mb-4">Sleep Factors (Optional)</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {sleepFactors.map((factor) => (
                  <Button
                    key={factor.id}
                    variant={selectedFactors.includes(factor.id) ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => {
                      setSelectedFactors(prev => 
                        prev.includes(factor.id) 
                          ? prev.filter(f => f !== factor.id)
                          : [...prev, factor.id]
                      );
                    }}
                    className="flex items-center justify-start"
                  >
                    <factor.icon className="w-4 h-4 mr-2" />
                    {factor.label}
                  </Button>
                ))}
              </div>
            </motion.div>
          )}
        </GlassmorphCard>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="analysis">Analysis</TabsTrigger>
            <TabsTrigger value="goals">Goals</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <GlassmorphCard className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center">
                    <BarChart3 className="w-5 h-5 mr-2" />
                    Sleep Trends (7 Days)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <SleepChart />
                  <div className="flex justify-center space-x-6 mt-4 text-sm">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-blue-500 rounded mr-2" />
                      Duration
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-purple-500 rounded mr-2" />
                      Quality
                    </div>
                  </div>
                </CardContent>
              </GlassmorphCard>

              <GlassmorphCard className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center">
                    <Target className="w-5 h-5 mr-2" />
                    Goal Progress
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Duration Goal</span>
                        <span>{averages.duration}h / {sleepGoals.targetDuration}h</span>
                      </div>
                      <Progress value={(averages.duration / sleepGoals.targetDuration) * 100} />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Quality Goal</span>
                        <span>{averages.quality} / {sleepGoals.qualityGoal}</span>
                      </div>
                      <Progress value={(averages.quality / sleepGoals.qualityGoal) * 100} />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Consistency</span>
                        <span>{averages.consistency}%</span>
                      </div>
                      <Progress value={averages.consistency} />
                    </div>
                  </div>
                </CardContent>
              </GlassmorphCard>
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <div className="space-y-4">
              {sleepSessions.map((session) => (
                <motion.div
                  key={session.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  <GlassmorphCard className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="text-center">
                          <p className="text-sm text-gray-500">
                            {new Date(session.date).toLocaleDateString()}
                          </p>
                          <p className="font-semibold">
                            {session.bedtime} - {session.wakeTime}
                          </p>
                        </div>
                        <div className="text-center">
                          <p className="text-sm text-gray-500">Duration</p>
                          <p className="font-semibold">{session.duration}h</p>
                        </div>
                        <div className="text-center">
                          <p className="text-sm text-gray-500">Quality</p>
                          <div className="flex items-center">
                            <Star className="w-4 h-4 text-yellow-500 mr-1" />
                            <span className="font-semibold">{session.quality}/10</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`px-3 py-1 rounded-full text-sm ${
                          session.mood === 'excellent' ? 'bg-green-100 text-green-800' :
                          session.mood === 'good' ? 'bg-blue-100 text-blue-800' :
                          session.mood === 'fair' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {session.mood}
                        </p>
                      </div>
                    </div>
                    {session.notes && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-3">
                        {session.notes}
                      </p>
                    )}
                    <div className="flex flex-wrap gap-2 mt-3">
                      {session.factors.map((factor) => (
                        <span
                          key={factor}
                          className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs"
                        >
                          {factor}
                        </span>
                      ))}
                    </div>
                  </GlassmorphCard>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analysis" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <GlassmorphCard className="p-6">
                <CardHeader>
                  <CardTitle>Sleep Stages</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Deep Sleep</span>
                      <span className="font-semibold">2.3h (29%)</span>
                    </div>
                    <Progress value={29} className="h-2" />
                    
                    <div className="flex justify-between items-center">
                      <span>REM Sleep</span>
                      <span className="font-semibold">1.7h (21%)</span>
                    </div>
                    <Progress value={21} className="h-2" />
                    
                    <div className="flex justify-between items-center">
                      <span>Light Sleep</span>
                      <span className="font-semibold">4.0h (50%)</span>
                    </div>
                    <Progress value={50} className="h-2" />
                  </div>
                </CardContent>
              </GlassmorphCard>

              <GlassmorphCard className="p-6">
                <CardHeader>
                  <CardTitle>Sleep Insights</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <h4 className="font-medium text-green-800 dark:text-green-400">
                        Consistent Bedtime
                      </h4>
                      <p className="text-sm text-green-600 dark:text-green-300">
                        You've maintained a consistent bedtime for 5 days!
                      </p>
                    </div>
                    
                    <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                      <h4 className="font-medium text-yellow-800 dark:text-yellow-400">
                        Screen Time Impact
                      </h4>
                      <p className="text-sm text-yellow-600 dark:text-yellow-300">
                        Late screen time correlates with lower sleep quality.
                      </p>
                    </div>
                    
                    <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <h4 className="font-medium text-blue-800 dark:text-blue-400">
                        Optimal Duration
                      </h4>
                      <p className="text-sm text-blue-600 dark:text-blue-300">
                        Your best quality sleep occurs with 7.5-8.5 hour duration.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </GlassmorphCard>
            </div>
          </TabsContent>

          <TabsContent value="goals" className="space-y-6">
            <GlassmorphCard className="p-6">
              <CardHeader>
                <CardTitle>Sleep Goals</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="target-bedtime">Target Bedtime</Label>
                      <Input
                        id="target-bedtime"
                        type="time"
                        value={sleepGoals.targetBedtime}
                        onChange={(e) => setSleepGoals(prev => ({ ...prev, targetBedtime: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="target-wake">Target Wake Time</Label>
                      <Input
                        id="target-wake"
                        type="time"
                        value={sleepGoals.targetWakeTime}
                        onChange={(e) => setSleepGoals(prev => ({ ...prev, targetWakeTime: e.target.value }))}
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="target-duration">Target Duration (hours)</Label>
                      <Input
                        id="target-duration"
                        type="number"
                        min="6"
                        max="12"
                        step="0.5"
                        value={sleepGoals.targetDuration}
                        onChange={(e) => setSleepGoals(prev => ({ ...prev, targetDuration: parseFloat(e.target.value) }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="quality-goal">Quality Goal (1-10)</Label>
                      <Input
                        id="quality-goal"
                        type="number"
                        min="1"
                        max="10"
                        value={sleepGoals.qualityGoal}
                        onChange={(e) => setSleepGoals(prev => ({ ...prev, qualityGoal: parseInt(e.target.value) }))}
                      />
                    </div>
                  </div>
                </div>
                <Button className="mt-6">Save Goals</Button>
              </CardContent>
            </GlassmorphCard>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SleepTracker;
