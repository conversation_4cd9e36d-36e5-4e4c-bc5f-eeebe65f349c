import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>, CardContent, CardHeader, CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue 
} from '@/components/ui/select';
import {
  Target, TrendingUp, Calendar, Clock, Star, Award, 
  Plus, Edit, Trash2, CheckCircle, Circle, Flag,
  BarChart3, PieChart, Activity, Zap, Users, 
  ArrowUp, ArrowDown, Minus, Eye, EyeOff,
  Lightbulb, Rocket, Trophy, Crown, Diamond
} from 'lucide-react';

// Advanced Goal Management Interfaces
interface OKR {
  id: string;
  title: string;
  description: string;
  type: 'objective' | 'key-result';
  parentId?: string; // For key results linked to objectives
  quarter: string;
  year: number;
  owner: string;
  collaborators: string[];
  progress: number;
  targetValue?: number;
  currentValue?: number;
  unit?: string;
  status: 'not-started' | 'on-track' | 'at-risk' | 'behind' | 'completed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  tags: string[];
  startDate: string;
  endDate: string;
  checkIns: Array<{
    id: string;
    date: string;
    progress: number;
    notes: string;
    confidence: number; // 1-10 scale
    blockers: string[];
  }>;
  milestones: Array<{
    id: string;
    title: string;
    description: string;
    dueDate: string;
    completed: boolean;
    completedAt?: string;
  }>;
  metrics: Array<{
    id: string;
    name: string;
    value: number;
    target: number;
    unit: string;
    trend: 'up' | 'down' | 'stable';
  }>;
  linkedGoals: string[];
  createdAt: string;
  updatedAt: string;
}

interface SMARTGoal {
  id: string;
  title: string;
  description: string;
  specific: string;
  measurable: string;
  achievable: string;
  relevant: string;
  timeBound: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'planning' | 'active' | 'paused' | 'completed' | 'cancelled';
  progress: number;
  owner: string;
  collaborators: string[];
  startDate: string;
  endDate: string;
  estimatedHours: number;
  actualHours: number;
  budget?: number;
  spentBudget?: number;
  tags: string[];
  subGoals: Array<{
    id: string;
    title: string;
    description: string;
    completed: boolean;
    dueDate?: string;
  }>;
  actionItems: Array<{
    id: string;
    title: string;
    description: string;
    assignee: string;
    dueDate: string;
    completed: boolean;
    priority: 'low' | 'medium' | 'high';
  }>;
  resources: Array<{
    id: string;
    name: string;
    type: 'document' | 'link' | 'tool' | 'person';
    url?: string;
    description: string;
  }>;
  successCriteria: Array<{
    id: string;
    criteria: string;
    met: boolean;
    evidence?: string;
  }>;
  risks: Array<{
    id: string;
    risk: string;
    impact: 'low' | 'medium' | 'high';
    probability: 'low' | 'medium' | 'high';
    mitigation: string;
    status: 'identified' | 'mitigated' | 'occurred';
  }>;
  createdAt: string;
  updatedAt: string;
}

interface GoalTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  type: 'okr' | 'smart' | 'habit';
  template: Partial<OKR | SMARTGoal>;
  tags: string[];
  popularity: number;
  createdBy: string;
}

const AdvancedGoalManager: React.FC = () => {
  // State Management
  const [okrs, setOKRs] = useState<OKR[]>([]);
  const [smartGoals, setSMARTGoals] = useState<SMARTGoal[]>([]);
  const [goalTemplates, setGoalTemplates] = useState<GoalTemplate[]>([]);
  const [activeTab, setActiveTab] = useState<'okrs' | 'smart' | 'templates' | 'analytics'>('okrs');
  
  // UI State
  const [selectedQuarter, setSelectedQuarter] = useState('Q1');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedOwner, setSelectedOwner] = useState('all');
  const [showGoalForm, setShowGoalForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState<OKR | SMARTGoal | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'hierarchy'>('grid');

  // Form State
  const [newOKR, setNewOKR] = useState({
    title: '',
    description: '',
    type: 'objective' as 'objective' | 'key-result',
    parentId: '',
    quarter: 'Q1',
    year: new Date().getFullYear(),
    owner: '',
    category: '',
    priority: 'medium' as OKR['priority'],
    targetValue: 0,
    unit: '',
    endDate: ''
  });

  const [newSMARTGoal, setNewSMARTGoal] = useState({
    title: '',
    description: '',
    specific: '',
    measurable: '',
    achievable: '',
    relevant: '',
    timeBound: '',
    category: '',
    priority: 'medium' as SMARTGoal['priority'],
    owner: '',
    endDate: '',
    estimatedHours: 0,
    budget: 0
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const savedOKRs = JSON.parse(localStorage.getItem('advanced-okrs') || '[]');
    const savedSMARTGoals = JSON.parse(localStorage.getItem('advanced-smart-goals') || '[]');
    const savedTemplates = JSON.parse(localStorage.getItem('goal-templates') || '[]');
    
    setOKRs(savedOKRs);
    setSMARTGoals(savedSMARTGoals);
    setGoalTemplates(savedTemplates);

    // Initialize with sample data if empty
    if (savedOKRs.length === 0 && savedSMARTGoals.length === 0) {
      initializeSampleData();
    }
  };

  const saveData = () => {
    localStorage.setItem('advanced-okrs', JSON.stringify(okrs));
    localStorage.setItem('advanced-smart-goals', JSON.stringify(smartGoals));
    localStorage.setItem('goal-templates', JSON.stringify(goalTemplates));
  };

  const initializeSampleData = () => {
    const sampleOKRs: OKR[] = [
      {
        id: 'okr-1',
        title: 'Improve Personal Productivity',
        description: 'Enhance daily productivity and time management skills',
        type: 'objective',
        quarter: 'Q1',
        year: 2024,
        owner: 'user-1',
        collaborators: [],
        progress: 65,
        status: 'on-track',
        priority: 'high',
        category: 'Personal Development',
        tags: ['productivity', 'time-management'],
        startDate: '2024-01-01',
        endDate: '2024-03-31',
        checkIns: [],
        milestones: [],
        metrics: [],
        linkedGoals: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    const sampleSMARTGoals: SMARTGoal[] = [
      {
        id: 'smart-1',
        title: 'Learn React and TypeScript',
        description: 'Master modern web development technologies',
        specific: 'Complete a comprehensive React and TypeScript course',
        measurable: 'Build 3 projects and pass certification exam',
        achievable: 'Dedicate 2 hours daily for 3 months',
        relevant: 'Essential for career advancement in web development',
        timeBound: 'Complete by end of Q2 2024',
        category: 'Skill Development',
        priority: 'high',
        status: 'active',
        progress: 40,
        owner: 'user-1',
        collaborators: [],
        startDate: '2024-01-01',
        endDate: '2024-06-30',
        estimatedHours: 180,
        actualHours: 72,
        tags: ['programming', 'web-development', 'react'],
        subGoals: [],
        actionItems: [],
        resources: [],
        successCriteria: [],
        risks: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    setOKRs(sampleOKRs);
    setSMARTGoals(sampleSMARTGoals);
  };

  const createOKR = () => {
    const okr: OKR = {
      id: `okr-${Date.now()}`,
      title: newOKR.title,
      description: newOKR.description,
      type: newOKR.type,
      parentId: newOKR.parentId || undefined,
      quarter: newOKR.quarter,
      year: newOKR.year,
      owner: newOKR.owner,
      collaborators: [],
      progress: 0,
      targetValue: newOKR.targetValue,
      currentValue: 0,
      unit: newOKR.unit,
      status: 'not-started',
      priority: newOKR.priority,
      category: newOKR.category,
      tags: [],
      startDate: new Date().toISOString(),
      endDate: newOKR.endDate,
      checkIns: [],
      milestones: [],
      metrics: [],
      linkedGoals: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setOKRs([...okrs, okr]);
    setShowGoalForm(false);
    resetOKRForm();
  };

  const createSMARTGoal = () => {
    const goal: SMARTGoal = {
      id: `smart-${Date.now()}`,
      title: newSMARTGoal.title,
      description: newSMARTGoal.description,
      specific: newSMARTGoal.specific,
      measurable: newSMARTGoal.measurable,
      achievable: newSMARTGoal.achievable,
      relevant: newSMARTGoal.relevant,
      timeBound: newSMARTGoal.timeBound,
      category: newSMARTGoal.category,
      priority: newSMARTGoal.priority,
      status: 'planning',
      progress: 0,
      owner: newSMARTGoal.owner,
      collaborators: [],
      startDate: new Date().toISOString(),
      endDate: newSMARTGoal.endDate,
      estimatedHours: newSMARTGoal.estimatedHours,
      actualHours: 0,
      budget: newSMARTGoal.budget,
      spentBudget: 0,
      tags: [],
      subGoals: [],
      actionItems: [],
      resources: [],
      successCriteria: [],
      risks: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setSMARTGoals([...smartGoals, goal]);
    setShowGoalForm(false);
    resetSMARTForm();
  };

  const resetOKRForm = () => {
    setNewOKR({
      title: '',
      description: '',
      type: 'objective',
      parentId: '',
      quarter: 'Q1',
      year: new Date().getFullYear(),
      owner: '',
      category: '',
      priority: 'medium',
      targetValue: 0,
      unit: '',
      endDate: ''
    });
  };

  const resetSMARTForm = () => {
    setNewSMARTGoal({
      title: '',
      description: '',
      specific: '',
      measurable: '',
      achievable: '',
      relevant: '',
      timeBound: '',
      category: '',
      priority: 'medium',
      owner: '',
      endDate: '',
      estimatedHours: 0,
      budget: 0
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen p-4 pb-24 bg-gradient-to-br from-purple-50 via-white to-blue-50"
    >
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Advanced Goal Manager</h1>
          <p className="text-gray-600">OKRs, SMART Goals, and Professional Goal Tracking</p>
        </div>
        
        <div className="flex flex-wrap items-center space-x-2">
          <Button 
            onClick={() => setShowGoalForm(true)}
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Goal
          </Button>
          <Button variant="outline">
            <BarChart3 className="w-4 h-4 mr-2" />
            Analytics
          </Button>
        </div>
      </div>

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="okrs" className="flex items-center">
            <Target className="w-4 h-4 mr-2" />
            OKRs
          </TabsTrigger>
          <TabsTrigger value="smart" className="flex items-center">
            <Star className="w-4 h-4 mr-2" />
            SMART Goals
          </TabsTrigger>
          <TabsTrigger value="templates" className="flex items-center">
            <Lightbulb className="w-4 h-4 mr-2" />
            Templates
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center">
            <TrendingUp className="w-4 h-4 mr-2" />
            Analytics
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </motion.div>
  );
};

export default AdvancedGoalManager;
