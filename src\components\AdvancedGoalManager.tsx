import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>, CardContent, CardHeader, CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue 
} from '@/components/ui/select';
import {
  Target, TrendingUp, Calendar, Clock, Star, Award,
  Plus, Edit, Trash2, CheckCircle, Circle, Flag,
  BarChart3, PieChart, Activity, Zap, Users,
  ArrowUp, ArrowDown, Minus, Eye, EyeOff,
  Lightbulb, Rocket, Trophy, Crown, Diamond, FileText
} from 'lucide-react';

// Advanced Goal Management Interfaces
interface OKR {
  id: string;
  title: string;
  description: string;
  type: 'objective' | 'key-result';
  parentId?: string; // For key results linked to objectives
  quarter: string;
  year: number;
  owner: string;
  collaborators: string[];
  progress: number;
  targetValue?: number;
  currentValue?: number;
  unit?: string;
  status: 'not-started' | 'on-track' | 'at-risk' | 'behind' | 'completed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  tags: string[];
  startDate: string;
  endDate: string;
  checkIns: Array<{
    id: string;
    date: string;
    progress: number;
    notes: string;
    confidence: number; // 1-10 scale
    blockers: string[];
  }>;
  milestones: Array<{
    id: string;
    title: string;
    description: string;
    dueDate: string;
    completed: boolean;
    completedAt?: string;
  }>;
  metrics: Array<{
    id: string;
    name: string;
    value: number;
    target: number;
    unit: string;
    trend: 'up' | 'down' | 'stable';
  }>;
  linkedGoals: string[];
  createdAt: string;
  updatedAt: string;
}

interface SMARTGoal {
  id: string;
  title: string;
  description: string;
  specific: string;
  measurable: string;
  achievable: string;
  relevant: string;
  timeBound: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'planning' | 'active' | 'paused' | 'completed' | 'cancelled';
  progress: number;
  owner: string;
  collaborators: string[];
  startDate: string;
  endDate: string;
  estimatedHours: number;
  actualHours: number;
  budget?: number;
  spentBudget?: number;
  tags: string[];
  subGoals: Array<{
    id: string;
    title: string;
    description: string;
    completed: boolean;
    dueDate?: string;
  }>;
  actionItems: Array<{
    id: string;
    title: string;
    description: string;
    assignee: string;
    dueDate: string;
    completed: boolean;
    priority: 'low' | 'medium' | 'high';
  }>;
  resources: Array<{
    id: string;
    name: string;
    type: 'document' | 'link' | 'tool' | 'person';
    url?: string;
    description: string;
  }>;
  successCriteria: Array<{
    id: string;
    criteria: string;
    met: boolean;
    evidence?: string;
  }>;
  risks: Array<{
    id: string;
    risk: string;
    impact: 'low' | 'medium' | 'high';
    probability: 'low' | 'medium' | 'high';
    mitigation: string;
    status: 'identified' | 'mitigated' | 'occurred';
  }>;
  createdAt: string;
  updatedAt: string;
}

interface GoalTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  type: 'okr' | 'smart' | 'habit';
  template: Partial<OKR | SMARTGoal>;
  tags: string[];
  popularity: number;
  createdBy: string;
}

const AdvancedGoalManager: React.FC = () => {
  // State Management
  const [okrs, setOKRs] = useState<OKR[]>([]);
  const [smartGoals, setSMARTGoals] = useState<SMARTGoal[]>([]);
  const [goalTemplates, setGoalTemplates] = useState<GoalTemplate[]>([]);
  const [activeTab, setActiveTab] = useState<'okrs' | 'smart' | 'templates' | 'analytics'>('okrs');
  
  // UI State
  const [selectedQuarter, setSelectedQuarter] = useState('Q1');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedOwner, setSelectedOwner] = useState('all');
  const [showGoalForm, setShowGoalForm] = useState(false);
  const [showOKRForm, setShowOKRForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState<OKR | SMARTGoal | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'hierarchy'>('grid');

  // Form State
  const [newOKR, setNewOKR] = useState({
    title: '',
    description: '',
    type: 'objective' as 'objective' | 'key-result',
    parentId: '',
    quarter: 'Q1',
    year: new Date().getFullYear(),
    owner: '',
    category: '',
    priority: 'medium' as OKR['priority'],
    targetValue: 0,
    unit: '',
    endDate: ''
  });

  const [newSMARTGoal, setNewSMARTGoal] = useState({
    title: '',
    description: '',
    specific: '',
    measurable: '',
    achievable: '',
    relevant: '',
    timeBound: '',
    category: '',
    priority: 'medium' as SMARTGoal['priority'],
    owner: '',
    endDate: '',
    estimatedHours: 0,
    budget: 0
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const savedOKRs = JSON.parse(localStorage.getItem('advanced-okrs') || '[]');
    const savedSMARTGoals = JSON.parse(localStorage.getItem('advanced-smart-goals') || '[]');
    const savedTemplates = JSON.parse(localStorage.getItem('goal-templates') || '[]');
    
    setOKRs(savedOKRs);
    setSMARTGoals(savedSMARTGoals);
    setGoalTemplates(savedTemplates);

    // Initialize with sample data if empty
    if (savedOKRs.length === 0 && savedSMARTGoals.length === 0) {
      initializeSampleData();
    }
  };

  const saveData = () => {
    localStorage.setItem('advanced-okrs', JSON.stringify(okrs));
    localStorage.setItem('advanced-smart-goals', JSON.stringify(smartGoals));
    localStorage.setItem('goal-templates', JSON.stringify(goalTemplates));
  };

  const initializeSampleData = () => {
    // Start with empty arrays - users will create their own goals and OKRs
    const sampleOKRs: OKR[] = [];
    const sampleSMARTGoals: SMARTGoal[] = [];

    setOKRs(sampleOKRs);
    setSMARTGoals(sampleSMARTGoals);
  };

  const createOKR = () => {
    const okr: OKR = {
      id: `okr-${Date.now()}`,
      title: newOKR.title,
      description: newOKR.description,
      type: newOKR.type,
      parentId: newOKR.parentId || undefined,
      quarter: newOKR.quarter,
      year: newOKR.year,
      owner: newOKR.owner,
      collaborators: [],
      progress: 0,
      targetValue: newOKR.targetValue,
      currentValue: 0,
      unit: newOKR.unit,
      status: 'not-started',
      priority: newOKR.priority,
      category: newOKR.category,
      tags: [],
      startDate: new Date().toISOString(),
      endDate: newOKR.endDate,
      checkIns: [],
      milestones: [],
      metrics: [],
      linkedGoals: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setOKRs([...okrs, okr]);
    setShowGoalForm(false);
    resetOKRForm();
  };

  const createSMARTGoal = () => {
    const goal: SMARTGoal = {
      id: `smart-${Date.now()}`,
      title: newSMARTGoal.title,
      description: newSMARTGoal.description,
      specific: newSMARTGoal.specific,
      measurable: newSMARTGoal.measurable,
      achievable: newSMARTGoal.achievable,
      relevant: newSMARTGoal.relevant,
      timeBound: newSMARTGoal.timeBound,
      category: newSMARTGoal.category,
      priority: newSMARTGoal.priority,
      status: 'planning',
      progress: 0,
      owner: newSMARTGoal.owner,
      collaborators: [],
      startDate: new Date().toISOString(),
      endDate: newSMARTGoal.endDate,
      estimatedHours: newSMARTGoal.estimatedHours,
      actualHours: 0,
      budget: newSMARTGoal.budget,
      spentBudget: 0,
      tags: [],
      subGoals: [],
      actionItems: [],
      resources: [],
      successCriteria: [],
      risks: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setSMARTGoals([...smartGoals, goal]);
    setShowGoalForm(false);
    resetSMARTForm();
  };

  const resetOKRForm = () => {
    setNewOKR({
      title: '',
      description: '',
      type: 'objective',
      parentId: '',
      quarter: 'Q1',
      year: new Date().getFullYear(),
      owner: '',
      category: '',
      priority: 'medium',
      targetValue: 0,
      unit: '',
      endDate: ''
    });
  };

  const resetSMARTForm = () => {
    setNewSMARTGoal({
      title: '',
      description: '',
      specific: '',
      measurable: '',
      achievable: '',
      relevant: '',
      timeBound: '',
      category: '',
      priority: 'medium',
      owner: '',
      endDate: '',
      estimatedHours: 0,
      budget: 0
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen p-4 pb-24 bg-gradient-to-br from-purple-50 via-white to-blue-50"
    >
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Advanced Goal Manager</h1>
          <p className="text-gray-600">OKRs, SMART Goals, and Professional Goal Tracking</p>
        </div>
        
        <div className="flex flex-wrap items-center space-x-2">
          <Button 
            onClick={() => setShowGoalForm(true)}
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Goal
          </Button>
          <Button variant="outline">
            <BarChart3 className="w-4 h-4 mr-2" />
            Analytics
          </Button>
        </div>
      </div>

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-4">
          <TabsTrigger value="okrs" className="flex items-center text-xs md:text-sm">
            <Target className="w-4 h-4 mr-1 md:mr-2" />
            OKRs
          </TabsTrigger>
          <TabsTrigger value="smart" className="flex items-center text-xs md:text-sm">
            <Star className="w-4 h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">SMART Goals</span>
            <span className="sm:hidden">SMART</span>
          </TabsTrigger>
          <TabsTrigger value="templates" className="flex items-center text-xs md:text-sm">
            <Lightbulb className="w-4 h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Templates</span>
            <span className="sm:hidden">Temp</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center text-xs md:text-sm">
            <TrendingUp className="w-4 h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Analytics</span>
            <span className="sm:hidden">Stats</span>
          </TabsTrigger>
        </TabsList>

        {/* OKRs Tab Content */}
        <TabsContent value="okrs" className="space-y-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Objectives & Key Results</h3>
            <Button
              onClick={() => setShowOKRForm(true)}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              New OKR
            </Button>
          </div>
          <div className="grid gap-6">
            {okrs.map((okr, index) => (
              <motion.div
                key={okr.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                        {okr.objective}
                      </h3>
                      <Badge variant={okr.status === 'completed' ? 'default' :
                                    okr.status === 'in-progress' ? 'secondary' : 'outline'}>
                        {okr.status}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        Q{okr.quarter} {okr.year}
                      </Badge>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                      {okr.description}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-gray-500 mb-4">
                      <span>Owner: {okr.owner}</span>
                      <span>Priority: {okr.priority}</span>
                      <span>Category: {okr.category}</span>
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Progress
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {okr.progress}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <motion.div
                      className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${okr.progress}%` }}
                      transition={{ duration: 1, delay: 0.5 }}
                    />
                  </div>
                </div>

                {/* Key Results */}
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-700 dark:text-gray-300">Key Results</h4>
                  <div className="grid gap-2">
                    <div className="bg-white/10 rounded-lg p-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {okr.currentValue} / {okr.targetValue} {okr.unit}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {Math.round((okr.currentValue / okr.targetValue) * 100)}%
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2 mt-4">
                  <Button variant="outline" size="sm">
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                  <Button variant="outline" size="sm">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Check-in
                  </Button>
                  <Button variant="outline" size="sm">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Analytics
                  </Button>
                </div>
              </motion.div>
            ))}
          </div>

          {okrs.length === 0 && (
            <div className="text-center py-12">
              <Target className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
                No OKRs found
              </h3>
              <p className="text-gray-500 mb-4">
                Create your first OKR to get started with goal tracking
              </p>
              <Button onClick={() => setShowOKRForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create OKR
              </Button>
            </div>
          )}
        </TabsContent>

        {/* SMART Goals Tab Content */}
        <TabsContent value="smart" className="space-y-6">
          <div className="text-center py-12">
            <Target className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
              SMART Goals Coming Soon
            </h3>
            <p className="text-gray-500 mb-4">
              Specific, Measurable, Achievable, Relevant, Time-bound goals
            </p>
          </div>
        </TabsContent>

        {/* Templates Tab Content */}
        <TabsContent value="templates" className="space-y-6">
          <div className="text-center py-12">
            <FileText className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
              Goal Templates
            </h3>
            <p className="text-gray-500 mb-4">
              Pre-built templates for common goal types
            </p>
          </div>
        </TabsContent>

        {/* Analytics Tab Content */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="text-center py-12">
            <TrendingUp className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
              Goal Analytics
            </h3>
            <p className="text-gray-500 mb-4">
              Insights and progress analytics for your goals
            </p>
          </div>
        </TabsContent>
      </Tabs>

      {/* Create OKR Form Modal */}
      <AnimatePresence>
        {showOKRForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowOKRForm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">Create New OKR</h3>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="objective">Objective</Label>
                  <Input
                    id="objective"
                    value={newOKR.objective}
                    onChange={(e) => setNewOKR({ ...newOKR, objective: e.target.value })}
                    placeholder="What do you want to achieve?"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    value={newOKR.description}
                    onChange={(e) => setNewOKR({ ...newOKR, description: e.target.value })}
                    placeholder="Describe your objective"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="quarter">Quarter</Label>
                    <Select value={newOKR.quarter.toString()} onValueChange={(value) => setNewOKR({ ...newOKR, quarter: parseInt(value) })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">Q1</SelectItem>
                        <SelectItem value="2">Q2</SelectItem>
                        <SelectItem value="3">Q3</SelectItem>
                        <SelectItem value="4">Q4</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="year">Year</Label>
                    <Input
                      id="year"
                      type="number"
                      value={newOKR.year}
                      onChange={(e) => setNewOKR({ ...newOKR, year: parseInt(e.target.value) })}
                    />
                  </div>
                </div>

                <div className="flex gap-2 pt-4">
                  <Button onClick={createOKR} className="flex-1">
                    Create OKR
                  </Button>
                  <Button variant="outline" onClick={() => setShowOKRForm(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default AdvancedGoalManager;
