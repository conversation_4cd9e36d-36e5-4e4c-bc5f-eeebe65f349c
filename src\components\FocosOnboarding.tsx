
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronRight, ArrowLeft, Check, Target, DollarSign, Heart, Brain, TrendingUp,
  Briefcase, Sun, Moon, Clock, Layers, Zap, Bell, BellOff, Sparkles, Star,
  Rocket, Crown, Diamond, Palette, Smartphone, Globe, Shield, Award
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface OnboardingData {
  name: string;
  avatar: string;
  goals: string[];
  workStyle: string;
  motto: string;
  notificationsEnabled: boolean;
}

const FocosOnboarding = ({ onComplete }: { onComplete: (data: OnboardingData) => void }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showParticles, setShowParticles] = useState(true);
  const [data, setData] = useState<OnboardingData>({
    name: '',
    avatar: '👤',
    goals: [],
    workStyle: '',
    motto: '',
    notificationsEnabled: true
  });

  // Floating particles animation
  const particles = Array.from({ length: 20 }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    delay: Math.random() * 2,
    duration: 3 + Math.random() * 2
  }));

  const avatars = ['👤', '😊', '🌟', '🚀', '💼', '🎯', '💡', '🌈', '⚡', '🔥', '🌸', '🎨', '🏆', '💎', '🦄', '🌙'];
  
  const goals = [
    { id: 'productivity', label: 'Productivity', icon: Target, color: 'from-purple-500 to-pink-500' },
    { id: 'health', label: 'Health & Fitness', icon: Heart, color: 'from-pink-500 to-red-500' },
    { id: 'finance', label: 'Financial Freedom', icon: DollarSign, color: 'from-green-500 to-emerald-500' },
    { id: 'learning', label: 'Learning', icon: Brain, color: 'from-blue-500 to-indigo-500' },
    { id: 'mindfulness', label: 'Mindfulness', icon: Heart, color: 'from-teal-500 to-cyan-500' },
    { id: 'career', label: 'Career Growth', icon: TrendingUp, color: 'from-orange-500 to-yellow-500' }
  ];

  const workStyles = [
    { id: 'early-bird', label: 'Early Bird', desc: 'Most productive in the morning', icon: Sun },
    { id: 'night-owl', label: 'Night Owl', desc: 'Peak performance in the evening', icon: Moon },
    { id: 'flexible', label: 'Flexible', desc: 'Adaptable to any schedule', icon: Clock },
    { id: 'structured', label: 'Structured', desc: 'Thrives with routine and planning', icon: Layers },
    { id: 'creative-bursts', label: 'Creative Bursts', desc: 'Works in intense focused sessions', icon: Zap }
  ];

  const handleNext = () => {
    if (currentStep < 6) setCurrentStep(currentStep + 1);
  };

  const handleBack = () => {
    if (currentStep > 0) setCurrentStep(currentStep - 1);
  };

  const handleComplete = () => {
    localStorage.setItem('focosOnboardingData', JSON.stringify(data));
    onComplete(data);
  };

  const toggleGoal = (goalId: string) => {
    setData(prev => ({
      ...prev,
      goals: prev.goals.includes(goalId) 
        ? prev.goals.filter(id => id !== goalId)
        : [...prev.goals, goalId]
    }));
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1: return data.name.trim().length > 0;
      case 2: return data.avatar !== '';
      case 3: return data.goals.length > 0;
      case 4: return data.workStyle !== '';
      case 5: return data.motto.trim().length > 0;
      default: return true;
    }
  };

  const pageVariants = {
    initial: { opacity: 0, x: 50, scale: 0.95 },
    animate: { opacity: 1, x: 0, scale: 1 },
    exit: { opacity: 0, x: -50, scale: 0.95 }
  };

  const steps = [
    // Step 0: Welcome Screen
    <motion.div
      key="welcome"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="flex flex-col items-center justify-center min-h-screen text-center p-6"
    >
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ type: "spring", duration: 1.2, delay: 0.3 }}
        className="mb-12"
      >
        <div className="relative">
          <div className="w-32 h-32 bg-gradient-to-br from-purple-600 via-pink-600 to-purple-800 rounded-3xl flex items-center justify-center shadow-2xl">
            <span className="text-5xl font-bold text-white">F</span>
          </div>
          <motion.div
            className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center"
            animate={{ scale: [1, 1.2, 1], rotate: [0, 180, 360] }}
            transition={{ duration: 3, repeat: Infinity }}
          >
            <span className="text-white text-sm">✨</span>
          </motion.div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="space-y-6 mb-12"
      >
        <h1 className="text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-purple-800 bg-clip-text text-transparent">
          FOCOS
        </h1>
        <p className="text-xl text-gray-300 font-medium max-w-md">
          Your comprehensive productivity companion for achieving life goals
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.9 }}
        className="grid grid-cols-2 gap-4 mb-12 max-w-sm"
      >
        {[
          { icon: Target, label: 'Goal Tracking' },
          { icon: DollarSign, label: 'Finance Manager' },
          { icon: Heart, label: 'Wellness Hub' },
          { icon: Brain, label: 'Focus Tools' }
        ].map((feature, index) => (
          <GlassmorphCard key={feature.label} className="p-4 text-center">
            <feature.icon className="w-8 h-8 text-purple-400 mx-auto mb-2" />
            <p className="text-sm text-white font-medium">{feature.label}</p>
          </GlassmorphCard>
        ))}
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.2 }}
      >
        <Button 
          onClick={handleNext}
          className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-12 py-4 text-lg rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
        >
          Begin Your Journey
          <ChevronRight className="ml-2 w-5 h-5" />
        </Button>
      </motion.div>
    </motion.div>,

    // Step 1: Name Collection
    <motion.div
      key="name"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="flex flex-col items-center justify-center min-h-screen text-center p-6"
    >
      <div className="mb-8">
        <h2 className="text-4xl font-bold text-white mb-4">What should we call you?</h2>
        <p className="text-gray-300 text-lg">Personalization makes everything better</p>
      </div>

      <GlassmorphCard className="w-full max-w-md p-8 space-y-6">
        <Input
          type="text"
          placeholder="Enter your name..."
          value={data.name}
          onChange={(e) => setData(prev => ({ ...prev, name: e.target.value }))}
          className="text-center text-xl py-4 bg-white/10 border-white/20 text-white placeholder:text-gray-400 rounded-xl"
        />
        
        {data.name && (
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-purple-300 font-medium"
          >
            Nice to meet you, {data.name}! 👋
          </motion.p>
        )}
      </GlassmorphCard>

      <div className="flex space-x-4 mt-8 w-full max-w-md">
        <Button onClick={handleBack} variant="outline" className="flex-1 py-3 bg-white/10 border-white/20 text-white hover:bg-white/20">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!canProceed()}
          className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3"
        >
          Continue
        </Button>
      </div>
    </motion.div>,

    // Step 2: Avatar Selection
    <motion.div
      key="avatar"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="flex flex-col items-center justify-center min-h-screen text-center p-6"
    >
      <div className="mb-8">
        <h2 className="text-4xl font-bold text-white mb-4">Choose your avatar</h2>
        <p className="text-gray-300 text-lg">Pick one that represents you</p>
      </div>

      <GlassmorphCard className="w-full max-w-lg p-8">
        <div className="grid grid-cols-4 gap-4">
          {avatars.map((avatar) => (
            <motion.button
              key={avatar}
              onClick={() => setData(prev => ({ ...prev, avatar }))}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className={`p-4 rounded-2xl text-3xl transition-all ${
                data.avatar === avatar
                  ? 'bg-gradient-to-r from-purple-600 to-pink-600 shadow-lg'
                  : 'bg-white/10 hover:bg-white/20'
              }`}
            >
              {avatar}
            </motion.button>
          ))}
        </div>
      </GlassmorphCard>

      <div className="flex space-x-4 mt-8 w-full max-w-md">
        <Button onClick={handleBack} variant="outline" className="flex-1 py-3 bg-white/10 border-white/20 text-white hover:bg-white/20">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!canProceed()}
          className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3"
        >
          Continue
        </Button>
      </div>
    </motion.div>,

    // Step 3: Goal Setting
    <motion.div
      key="goals"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="flex flex-col items-center justify-center min-h-screen text-center p-6"
    >
      <div className="mb-8">
        <h2 className="text-4xl font-bold text-white mb-4">What are your goals?</h2>
        <p className="text-gray-300 text-lg">Select all that inspire you</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-2xl mb-8">
        {goals.map((goal) => (
          <motion.button
            key={goal.id}
            onClick={() => toggleGoal(goal.id)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={`relative overflow-hidden`}
          >
            <GlassmorphCard className={`p-6 transition-all ${
              data.goals.includes(goal.id) ? 'ring-2 ring-purple-400' : ''
            }`}>
              <div className="flex items-center space-x-4">
                <div className={`p-3 rounded-xl bg-gradient-to-r ${goal.color}`}>
                  <goal.icon className="w-6 h-6 text-white" />
                </div>
                <span className="text-lg font-medium text-white flex-1 text-left">{goal.label}</span>
                {data.goals.includes(goal.id) && (
                  <Check className="w-6 h-6 text-purple-400" />
                )}
              </div>
            </GlassmorphCard>
          </motion.button>
        ))}
      </div>

      <div className="flex space-x-4 w-full max-w-md">
        <Button onClick={handleBack} variant="outline" className="flex-1 py-3 bg-white/10 border-white/20 text-white hover:bg-white/20">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!canProceed()}
          className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3"
        >
          Continue
        </Button>
      </div>
    </motion.div>,

    // Step 4: Work Style Assessment
    <motion.div
      key="work-style"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="flex flex-col items-center justify-center min-h-screen text-center p-6"
    >
      <div className="mb-8">
        <h2 className="text-4xl font-bold text-white mb-4">What's your work style?</h2>
        <p className="text-gray-300 text-lg">Choose the one that fits you best</p>
      </div>

      <div className="space-y-4 w-full max-w-lg mb-8">
        {workStyles.map((style) => (
          <motion.button
            key={style.id}
            onClick={() => setData(prev => ({ ...prev, workStyle: style.id }))}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <GlassmorphCard className={`p-6 transition-all ${
              data.workStyle === style.id ? 'ring-2 ring-purple-400 bg-gradient-to-r from-purple-600/20 to-pink-600/20' : ''
            }`}>
              <div className="flex items-center space-x-4">
                <style.icon className="w-8 h-8 text-purple-400" />
                <div className="flex-1 text-left">
                  <h3 className="text-lg font-semibold text-white">{style.label}</h3>
                  <p className="text-gray-300 text-sm">{style.desc}</p>
                </div>
                {data.workStyle === style.id && (
                  <Check className="w-6 h-6 text-purple-400" />
                )}
              </div>
            </GlassmorphCard>
          </motion.button>
        ))}
      </div>

      <div className="flex space-x-4 w-full max-w-md">
        <Button onClick={handleBack} variant="outline" className="flex-1 py-3 bg-white/10 border-white/20 text-white hover:bg-white/20">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!canProceed()}
          className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3"
        >
          Continue
        </Button>
      </div>
    </motion.div>,

    // Step 5: Personal Motto
    <motion.div
      key="motto"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="flex flex-col items-center justify-center min-h-screen text-center p-6"
    >
      <div className="mb-8">
        <h2 className="text-4xl font-bold text-white mb-4">Your personal motto</h2>
        <p className="text-gray-300 text-lg">What drives you forward?</p>
      </div>

      <GlassmorphCard className="w-full max-w-lg p-8 space-y-6">
        <Textarea
          placeholder="Enter your inspiring motto..."
          value={data.motto}
          onChange={(e) => setData(prev => ({ ...prev, motto: e.target.value }))}
          className="min-h-32 bg-white/10 border-white/20 text-white placeholder:text-gray-400 rounded-xl"
        />
        
        <div className="text-left text-sm text-gray-400">
          <p className="mb-2">💡 Examples:</p>
          <ul className="space-y-1 text-xs">
            <li>"Progress over perfection"</li>
            <li>"Every day is a new opportunity"</li>
            <li>"Focus on what matters most"</li>
          </ul>
        </div>
      </GlassmorphCard>

      <div className="flex space-x-4 mt-8 w-full max-w-md">
        <Button onClick={handleBack} variant="outline" className="flex-1 py-3 bg-white/10 border-white/20 text-white hover:bg-white/20">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!canProceed()}
          className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3"
        >
          Continue
        </Button>
      </div>
    </motion.div>,

    // Step 6: Notification Preferences
    <motion.div
      key="notifications"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="flex flex-col items-center justify-center min-h-screen text-center p-6"
    >
      <div className="mb-8">
        <h2 className="text-4xl font-bold text-white mb-4">Stay motivated</h2>
        <p className="text-gray-300 text-lg">Enable notifications for reminders and encouragement</p>
      </div>

      <div className="space-y-4 w-full max-w-md mb-8">
        <motion.button
          onClick={() => setData(prev => ({ ...prev, notificationsEnabled: true }))}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <GlassmorphCard className={`p-6 transition-all ${
            data.notificationsEnabled ? 'ring-2 ring-purple-400 bg-gradient-to-r from-purple-600/20 to-pink-600/20' : ''
          }`}>
            <div className="flex items-center space-x-4">
              <Bell className="w-8 h-8 text-green-400" />
              <div className="flex-1 text-left">
                <h3 className="text-lg font-semibold text-white">Yes, keep me motivated</h3>
                <p className="text-gray-300 text-sm">Get helpful reminders and progress updates</p>
              </div>
              {data.notificationsEnabled && (
                <Check className="w-6 h-6 text-purple-400" />
              )}
            </div>
          </GlassmorphCard>
        </motion.button>

        <motion.button
          onClick={() => setData(prev => ({ ...prev, notificationsEnabled: false }))}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <GlassmorphCard className={`p-6 transition-all ${
            !data.notificationsEnabled ? 'ring-2 ring-purple-400 bg-gradient-to-r from-purple-600/20 to-pink-600/20' : ''
          }`}>
            <div className="flex items-center space-x-4">
              <BellOff className="w-8 h-8 text-gray-400" />
              <div className="flex-1 text-left">
                <h3 className="text-lg font-semibold text-white">No notifications</h3>
                <p className="text-gray-300 text-sm">I'll check the app on my own</p>
              </div>
              {!data.notificationsEnabled && (
                <Check className="w-6 h-6 text-purple-400" />
              )}
            </div>
          </GlassmorphCard>
        </motion.button>
      </div>

      <div className="flex space-x-4 w-full max-w-md">
        <Button onClick={handleBack} variant="outline" className="flex-1 py-3 bg-white/10 border-white/20 text-white hover:bg-white/20">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <Button
          onClick={handleComplete}
          className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3"
        >
          Launch FOCOS
        </Button>
      </div>
    </motion.div>
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 overflow-hidden">
      {/* Progress Indicator */}
      {currentStep > 0 && (
        <div className="fixed top-0 left-0 right-0 z-50 p-6">
          <div className="max-w-md mx-auto">
            <div className="flex space-x-2">
              {[1, 2, 3, 4, 5, 6].map((step) => (
                <motion.div
                  key={step}
                  className={`flex-1 h-2 rounded-full ${
                    step <= currentStep ? 'bg-gradient-to-r from-purple-600 to-pink-600' : 'bg-white/20'
                  }`}
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: step <= currentStep ? 1 : 0 }}
                  transition={{ duration: 0.3 }}
                />
              ))}
            </div>
          </div>
        </div>
      )}

      <AnimatePresence mode="wait">
        {steps[currentStep]}
      </AnimatePresence>
    </div>
  );
};

export default FocosOnboarding;
