
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  User, <PERSON>lette, Bell, Info, Moon, Sun, Smartphone, Shield,
  Type, Zap, Trash2, Download, Upload, Camera, RotateCcw,
  AlertTriangle, CheckCircle, Database, Volume2, Globe
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const Settings = () => {
  const [eyeProtectiveMode, setEyeProtectiveMode] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [textSize, setTextSize] = useState(16);
  const [animationSpeed, setAnimationSpeed] = useState(1);
  const [soundEffects, setSoundEffects] = useState(true);
  const [language, setLanguage] = useState('en');
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [clearSuccess, setClearSuccess] = useState(false);
  const [profile, setProfile] = useState({
    name: 'User',
    email: '',
    avatar: '',
    timezone: 'UTC',
    photo: null as File | null
  });

  useEffect(() => {
    // Load settings from localStorage
    const savedSettings = JSON.parse(localStorage.getItem('appSettings') || '{}');
    const savedProfile = JSON.parse(localStorage.getItem('userProfile') || '{}');

    setEyeProtectiveMode(savedSettings.eyeProtectiveMode || false);
    setNotifications(savedSettings.notifications !== false);
    setTextSize(savedSettings.textSize || 16);
    setAnimationSpeed(savedSettings.animationSpeed || 1);
    setSoundEffects(savedSettings.soundEffects !== false);
    setLanguage(savedSettings.language || 'en');
    setProfile(prev => ({ ...prev, ...savedProfile }));
  }, []);

  const saveSettings = () => {
    const settings = {
      eyeProtectiveMode,
      notifications,
      textSize,
      animationSpeed,
      soundEffects,
      language
    };
    localStorage.setItem('appSettings', JSON.stringify(settings));
    localStorage.setItem('userProfile', JSON.stringify(profile));

    // Apply settings
    document.documentElement.style.fontSize = `${textSize}px`;
    document.documentElement.style.setProperty('--animation-speed', `${animationSpeed}s`);

    if (eyeProtectiveMode) {
      document.documentElement.classList.add('eye-protective');
    } else {
      document.documentElement.classList.remove('eye-protective');
    }
  };

  useEffect(() => {
    saveSettings();
  }, [eyeProtectiveMode, notifications, profile, textSize, animationSpeed, soundEffects, language]);

  const clearAllData = () => {
    // Clear all app data except settings
    const keysToKeep = ['appSettings', 'userProfile'];
    const allKeys = Object.keys(localStorage);

    allKeys.forEach(key => {
      if (!keysToKeep.includes(key)) {
        localStorage.removeItem(key);
      }
    });

    setClearSuccess(true);
    setShowClearConfirm(false);

    setTimeout(() => {
      setClearSuccess(false);
      window.location.reload();
    }, 2000);
  };

  const exportData = () => {
    const allData: Record<string, any> = {};
    Object.keys(localStorage).forEach(key => {
      try {
        allData[key] = JSON.parse(localStorage.getItem(key) || '');
      } catch {
        allData[key] = localStorage.getItem(key);
      }
    });

    const dataStr = JSON.stringify(allData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `spark-life-compass-backup-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  };

  const importData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);
        Object.entries(data).forEach(([key, value]) => {
          localStorage.setItem(key, typeof value === 'string' ? value : JSON.stringify(value));
        });
        window.location.reload();
      } catch (error) {
        alert('Invalid backup file format');
      }
    };
    reader.readAsText(file);
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfile(prev => ({ ...prev, avatar: e.target?.result as string }));
      };
      reader.readAsDataURL(file);
    }
  };

  const themes = [
    { name: 'Default', value: 'default', colors: ['from-blue-500', 'to-purple-600'] },
    { name: 'Ocean', value: 'ocean', colors: ['from-blue-400', 'to-cyan-500'] },
    { name: 'Sunset', value: 'sunset', colors: ['from-orange-400', 'to-pink-500'] },
    { name: 'Forest', value: 'forest', colors: ['from-green-400', 'to-teal-500'] },
    { name: 'Royal', value: 'royal', colors: ['from-purple-500', 'to-indigo-600'] }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen p-4 pb-24 bg-gradient-to-br from-blue-50 via-white to-purple-50"
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Settings</h1>
          <p className="text-gray-600">Customize your FOCOS experience</p>
        </div>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5 bg-white/80 backdrop-blur-sm dark:bg-gray-800/80">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="appearance">Theme</TabsTrigger>
          <TabsTrigger value="notifications">Alerts</TabsTrigger>
          <TabsTrigger value="data">Data</TabsTrigger>
          <TabsTrigger value="about">About</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="w-5 h-5 mr-2" />
                Profile Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center mb-6">
                <div className="relative inline-block">
                  {profile.avatar ? (
                    <img
                      src={profile.avatar}
                      alt="Profile"
                      className="w-20 h-20 rounded-full object-cover mx-auto mb-4"
                    />
                  ) : (
                    <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center text-white text-2xl font-bold">
                      {profile.name.charAt(0).toUpperCase()}
                    </div>
                  )}
                  <label className="absolute bottom-4 right-0 bg-blue-500 hover:bg-blue-600 text-white rounded-full p-1 cursor-pointer transition-colors">
                    <Camera className="w-4 h-4" />
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handlePhotoUpload}
                      className="hidden"
                    />
                  </label>
                </div>
                <p className="text-sm text-gray-500">Click camera icon to upload photo</p>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Name</label>
                  <Input
                    value={profile.name}
                    onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                    className="rounded-xl border-2 border-gray-200 focus:border-purple-500"
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Email</label>
                  <Input
                    type="email"
                    value={profile.email}
                    onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                    className="rounded-xl border-2 border-gray-200 focus:border-purple-500"
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Timezone</label>
                  <select
                    value={profile.timezone}
                    onChange={(e) => setProfile({ ...profile, timezone: e.target.value })}
                    className="w-full px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:outline-none"
                  >
                    <option value="UTC">UTC</option>
                    <option value="America/New_York">Eastern Time</option>
                    <option value="America/Chicago">Central Time</option>
                    <option value="America/Denver">Mountain Time</option>
                    <option value="America/Los_Angeles">Pacific Time</option>
                    <option value="Europe/London">London</option>
                    <option value="Europe/Paris">Paris</option>
                    <option value="Asia/Tokyo">Tokyo</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="appearance" className="space-y-6">
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Palette className="w-5 h-5 mr-2" />
                Appearance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Eye Protective Mode Toggle */}
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                <div className="flex items-center space-x-3">
                  <Shield className="w-5 h-5 text-blue-600" />
                  <div>
                    <div className="font-medium">Eye Protective Mode</div>
                    <div className="text-sm text-gray-500">Dims white backgrounds to reduce eye strain</div>
                  </div>
                </div>
                <Switch
                  checked={eyeProtectiveMode}
                  onCheckedChange={setEyeProtectiveMode}
                />
              </div>

              {/* Text Size */}
              <div className="p-4 bg-gray-50 rounded-xl">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Type className="w-5 h-5" />
                    <div>
                      <div className="font-medium">Text Size</div>
                      <div className="text-sm text-gray-500">Adjust app text size</div>
                    </div>
                  </div>
                  <span className="text-sm font-medium">{textSize}px</span>
                </div>
                <Slider
                  value={[textSize]}
                  onValueChange={([value]) => setTextSize(value)}
                  min={12}
                  max={24}
                  step={1}
                  className="w-full"
                />
              </div>

              {/* Animation Speed */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Zap className="w-5 h-5" />
                    <div>
                      <div className="font-medium">Animation Speed</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Control animation timing</div>
                    </div>
                  </div>
                  <span className="text-sm font-medium">{animationSpeed}x</span>
                </div>
                <Slider
                  value={[animationSpeed]}
                  onValueChange={([value]) => setAnimationSpeed(value)}
                  min={0.5}
                  max={2}
                  step={0.1}
                  className="w-full"
                />
              </div>

              {/* Language Selection */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                <div className="flex items-center space-x-3 mb-3">
                  <Globe className="w-5 h-5" />
                  <div>
                    <div className="font-medium">Language</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">Choose your preferred language</div>
                  </div>
                </div>
                <Select value={language} onValueChange={setLanguage}>
                  <SelectTrigger className="rounded-xl">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">🇺🇸 English</SelectItem>
                    <SelectItem value="es">🇪🇸 Español</SelectItem>
                    <SelectItem value="fr">🇫🇷 Français</SelectItem>
                    <SelectItem value="de">🇩🇪 Deutsch</SelectItem>
                    <SelectItem value="hi">🇮🇳 हिंदी</SelectItem>
                    <SelectItem value="ja">🇯🇵 日本語</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Theme Selection */}
              <div>
                <h3 className="font-medium mb-4">Color Themes</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {themes.map((theme) => (
                    <div
                      key={theme.value}
                      className="p-4 border-2 border-gray-200 rounded-xl hover:border-purple-500 cursor-pointer transition-all"
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-full bg-gradient-to-r ${theme.colors[0]} ${theme.colors[1]}`} />
                        <div>
                          <div className="font-medium">{theme.name}</div>
                          <div className="text-sm text-gray-500">Modern gradient theme</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Bell className="w-5 h-5 mr-2" />
                Notifications
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                <div>
                  <div className="font-medium">Push Notifications</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Receive notifications for tasks and reminders</div>
                </div>
                <Switch
                  checked={notifications}
                  onCheckedChange={setNotifications}
                />
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                <div className="flex items-center space-x-3">
                  <Volume2 className="w-5 h-5" />
                  <div>
                    <div className="font-medium">Sound Effects</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">Play sounds for interactions and notifications</div>
                  </div>
                </div>
                <Switch
                  checked={soundEffects}
                  onCheckedChange={setSoundEffects}
                />
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                <div>
                  <div className="font-medium">Task Reminders</div>
                  <div className="text-sm text-gray-500">Get reminded about upcoming tasks</div>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                <div>
                  <div className="font-medium">Habit Tracking</div>
                  <div className="text-sm text-gray-500">Daily habit completion reminders</div>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                <div>
                  <div className="font-medium">Budget Alerts</div>
                  <div className="text-sm text-gray-500">Notify when approaching budget limits</div>
                </div>
                <Switch defaultChecked />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data" className="space-y-6">
          <Card className="shadow-lg border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="w-5 h-5 mr-2" />
                Data Management
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Backup & Restore */}
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl">
                <h3 className="font-medium mb-3 flex items-center">
                  <Download className="w-4 h-4 mr-2" />
                  Backup & Restore
                </h3>
                <div className="space-y-3">
                  <Button
                    onClick={exportData}
                    className="w-full bg-blue-600 hover:bg-blue-700 rounded-xl"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Export Data
                  </Button>
                  <div>
                    <input
                      type="file"
                      accept=".json"
                      onChange={importData}
                      className="hidden"
                      id="import-data"
                    />
                    <Button
                      onClick={() => document.getElementById('import-data')?.click()}
                      variant="outline"
                      className="w-full rounded-xl"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Import Data
                    </Button>
                  </div>
                </div>
              </div>

              {/* Clear Data */}
              <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-xl">
                <h3 className="font-medium mb-3 flex items-center text-red-700 dark:text-red-400">
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  Reset App Data
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  This will clear all your tasks, habits, notes, and other data. Your settings will be preserved.
                </p>

                {!showClearConfirm ? (
                  <Button
                    onClick={() => setShowClearConfirm(true)}
                    variant="destructive"
                    className="w-full rounded-xl"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Clear All Data
                  </Button>
                ) : (
                  <div className="space-y-3">
                    <p className="text-sm font-medium text-red-700 dark:text-red-400">
                      Are you sure? This action cannot be undone.
                    </p>
                    <div className="flex space-x-2">
                      <Button
                        onClick={clearAllData}
                        variant="destructive"
                        className="flex-1 rounded-xl"
                      >
                        Yes, Clear Data
                      </Button>
                      <Button
                        onClick={() => setShowClearConfirm(false)}
                        variant="outline"
                        className="flex-1 rounded-xl"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              {/* Success Message */}
              {clearSuccess && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="p-4 bg-green-50 dark:bg-green-900/20 rounded-xl"
                >
                  <div className="flex items-center text-green-700 dark:text-green-400">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    <span className="font-medium">Data cleared successfully!</span>
                  </div>
                  <p className="text-sm text-green-600 dark:text-green-500 mt-1">
                    The app will refresh automatically...
                  </p>
                </motion.div>
              )}

              {/* Reset Settings */}
              <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-xl">
                <h3 className="font-medium mb-3 flex items-center text-yellow-700 dark:text-yellow-400">
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Reset Settings
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Reset all settings to their default values.
                </p>
                <Button
                  onClick={() => {
                    setEyeProtectiveMode(false);
                    setTextSize(16);
                    setAnimationSpeed(1);
                    setSoundEffects(true);
                    setLanguage('en');
                    setNotifications(true);
                  }}
                  variant="outline"
                  className="w-full rounded-xl"
                >
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Reset to Defaults
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="about" className="space-y-6">
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Info className="w-5 h-5 mr-2" />
                About FOCOS
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-blue-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                  <Smartphone className="w-8 h-8 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800 mb-2">FOCOS</h2>
                <p className="text-gray-600 mb-1">by CODOVO STUDIO</p>
                <p className="text-sm text-gray-500">Version 1.0.0</p>
              </div>

              <div className="border-t pt-6">
                <h3 className="font-semibold mb-3">Developer Information</h3>
                <div className="space-y-2 text-sm">
                  <div><strong>Developer:</strong> Unique Shiwakoti</div>
                  <div><strong>Organization:</strong> CODOVO STUDIO</div>
                  <div><strong>Contact:</strong> <EMAIL></div>
                  <div><strong>Website:</strong> codovostudio.netlify.app</div>
                </div>
              </div>

              <div className="border-t pt-6">
                <h3 className="font-semibold mb-3">Key Features</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full" />
                    <span>Task Management</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span>Finance Tracking</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span>Health Monitoring</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full" />
                    <span>Note Taking</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full" />
                    <span>Habit Tracking</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-teal-500 rounded-full" />
                    <span>Time Management</span>
                  </div>
                </div>
              </div>

              <div className="border-t pt-6">
                <h3 className="font-semibold mb-3">Privacy & Security</h3>
                <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                  <Shield className="w-5 h-5 text-green-600" />
                  <div className="text-sm">
                    <div className="font-medium text-green-800">Your data is secure</div>
                    <div className="text-green-600">All data is stored locally on your device</div>
                  </div>
                </div>
              </div>

              <div className="text-center pt-6 border-t">
                <p className="text-sm text-gray-500">
                  © 2024 CODOVO STUDIO. All rights reserved.
                </p>
                <p className="text-xs text-gray-400 mt-2">
                  Built with React, TypeScript, and Tailwind CSS
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </motion.div>
  );
};

export default Settings;
