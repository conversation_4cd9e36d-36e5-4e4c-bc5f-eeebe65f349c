import React from 'react';
import { motion } from 'framer-motion';
import { Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SidebarToggleProps {
  onClick: () => void;
  className?: string;
}

const SidebarToggle: React.FC<SidebarToggleProps> = ({ onClick, className = '' }) => {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className={className}
    >
      <Button
        onClick={onClick}
        variant="outline"
        size="sm"
        className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200 dark:border-gray-700 hover:bg-white dark:hover:bg-gray-800 shadow-lg"
      >
        <Menu className="w-5 h-5" />
      </Button>
    </motion.div>
  );
};

export default SidebarToggle;
