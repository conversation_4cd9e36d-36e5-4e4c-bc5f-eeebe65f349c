import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Activity, Dumbbell, Apple, Heart, Clock, Calendar, Target } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface Workout {
  id: number;
  name: string;
  type: 'cardio' | 'strength' | 'flexibility' | 'sports';
  duration: number;
  calories: number;
  date: string;
  exercises?: Exercise[];
}

interface Exercise {
  id: number;
  name: string;
  sets?: number;
  reps?: number;
  weight?: number;
  duration?: number;
}

interface Meal {
  id: number;
  name: string;
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  date: string;
}

const HealthTracker = () => {
  const [workouts, setWorkouts] = useState<Workout[]>([]);
  const [meals, setMeals] = useState<Meal[]>([]);
  const [showWorkoutForm, setShowWorkoutForm] = useState(false);
  const [showMealForm, setShowMealForm] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const [newWorkout, setNewWorkout] = useState({
    name: '',
    type: 'cardio' as 'cardio' | 'strength' | 'flexibility' | 'sports',
    duration: '',
    calories: '',
    date: new Date().toISOString().split('T')[0]
  });

  const [newMeal, setNewMeal] = useState({
    name: '',
    type: 'breakfast' as const,
    calories: '',
    protein: '',
    carbs: '',
    fat: '',
    date: new Date().toISOString().split('T')[0]
  });

  const workoutTemplates = [
    {
      name: 'Full Body Strength',
      type: 'strength' as 'cardio' | 'strength' | 'flexibility' | 'sports',
      duration: 45,
      exercises: [
        { name: 'Push-ups', sets: 3, reps: 12 },
        { name: 'Squats', sets: 3, reps: 15 },
        { name: 'Pull-ups', sets: 3, reps: 8 },
        { name: 'Plank', duration: 60 }
      ]
    },
    {
      name: 'HIIT Cardio',
      type: 'cardio' as 'cardio' | 'strength' | 'flexibility' | 'sports',
      duration: 30,
      exercises: [
        { name: 'Jumping Jacks', duration: 30 },
        { name: 'Burpees', duration: 30 },
        { name: 'Mountain Climbers', duration: 30 },
        { name: 'High Knees', duration: 30 }
      ]
    },
    {
      name: 'Yoga Flow',
      type: 'flexibility' as 'cardio' | 'strength' | 'flexibility' | 'sports',
      duration: 60,
      exercises: [
        { name: 'Sun Salutation', sets: 5 },
        { name: 'Warrior Pose', duration: 60 },
        { name: 'Tree Pose', duration: 30 },
        { name: 'Savasana', duration: 300 }
      ]
    }
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const savedWorkouts = JSON.parse(localStorage.getItem('workouts') || '[]');
    const savedMeals = JSON.parse(localStorage.getItem('meals') || '[]');
    setWorkouts(savedWorkouts);
    setMeals(savedMeals);
  };

  const addWorkout = () => {
    if (!newWorkout.name || !newWorkout.duration) return;

    const workout: Workout = {
      id: Date.now(),
      name: newWorkout.name,
      type: newWorkout.type,
      duration: parseInt(newWorkout.duration),
      calories: parseInt(newWorkout.calories) || 0,
      date: newWorkout.date
    };

    const updated = [...workouts, workout];
    setWorkouts(updated);
    localStorage.setItem('workouts', JSON.stringify(updated));
    setNewWorkout({
      name: '',
      type: 'cardio',
      duration: '',
      calories: '',
      date: new Date().toISOString().split('T')[0]
    });
    setShowWorkoutForm(false);
  };

  const addMeal = () => {
    if (!newMeal.name || !newMeal.calories) return;

    const meal: Meal = {
      id: Date.now(),
      name: newMeal.name,
      type: newMeal.type,
      calories: parseInt(newMeal.calories),
      protein: parseInt(newMeal.protein) || 0,
      carbs: parseInt(newMeal.carbs) || 0,
      fat: parseInt(newMeal.fat) || 0,
      date: newMeal.date
    };

    const updated = [...meals, meal];
    setMeals(updated);
    localStorage.setItem('meals', JSON.stringify(updated));
    setNewMeal({
      name: '',
      type: 'breakfast',
      calories: '',
      protein: '',
      carbs: '',
      fat: '',
      date: new Date().toISOString().split('T')[0]
    });
    setShowMealForm(false);
  };

  const useTemplate = (template: typeof workoutTemplates[0]) => {
    setNewWorkout({
      name: template.name,
      type: template.type,
      duration: template.duration.toString(),
      calories: ((template.duration * 8)).toString(),
      date: new Date().toISOString().split('T')[0]
    });
    setShowWorkoutForm(true);
  };

  const getTodayStats = () => {
    const today = new Date().toISOString().split('T')[0];
    const todayWorkouts = workouts.filter(w => w.date === today);
    const todayMeals = meals.filter(m => m.date === today);

    const totalWorkoutTime = todayWorkouts.reduce((sum, w) => sum + w.duration, 0);
    const totalCaloriesBurned = todayWorkouts.reduce((sum, w) => sum + w.calories, 0);
    const totalCaloriesConsumed = todayMeals.reduce((sum, m) => sum + m.calories, 0);

    return {
      workoutTime: totalWorkoutTime,
      caloriesBurned: totalCaloriesBurned,
      caloriesConsumed: totalCaloriesConsumed,
      netCalories: totalCaloriesConsumed - totalCaloriesBurned,
      workoutsCount: todayWorkouts.length,
      mealsCount: todayMeals.length
    };
  };

  const stats = getTodayStats();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen p-4 pb-24 bg-gradient-to-br from-green-50 via-white to-blue-50"
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Health Tracker</h1>
          <p className="text-gray-600">Track your fitness and nutrition</p>
        </div>
        <div className="flex space-x-2">
          <Button 
            onClick={() => setShowWorkoutForm(true)}
            className="bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 rounded-full p-3"
          >
            <Dumbbell className="w-5 h-5" />
          </Button>
          <Button 
            onClick={() => setShowMealForm(true)}
            className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 rounded-full p-3"
          >
            <Apple className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Today's Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <Card className="text-center p-4 shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <div className="text-2xl font-bold text-green-600">{stats.workoutTime}</div>
          <div className="text-sm text-gray-600">Minutes Active</div>
        </Card>
        <Card className="text-center p-4 shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <div className="text-2xl font-bold text-red-600">{stats.caloriesBurned}</div>
          <div className="text-sm text-gray-600">Cal Burned</div>
        </Card>
        <Card className="text-center p-4 shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <div className="text-2xl font-bold text-blue-600">{stats.caloriesConsumed}</div>
          <div className="text-sm text-gray-600">Cal Consumed</div>
        </Card>
        <Card className="text-center p-4 shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <div className={`text-2xl font-bold ${stats.netCalories > 0 ? 'text-orange-600' : 'text-green-600'}`}>
            {stats.netCalories > 0 ? '+' : ''}{stats.netCalories}
          </div>
          <div className="text-sm text-gray-600">Net Calories</div>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-white/80 backdrop-blur-sm">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="workouts">Workouts</TabsTrigger>
          <TabsTrigger value="nutrition">Nutrition</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Recent Activity */}
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="w-5 h-5 mr-2" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[...workouts, ...meals]
                  .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                  .slice(0, 5)
                  .map((item) => (
                    <div key={`${item.id}-${'type' in item ? 'workout' : 'meal'}`} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        {'type' in item && item.type ? (
                          <Dumbbell className="w-5 h-5 text-green-600" />
                        ) : (
                          <Apple className="w-5 h-5 text-orange-600" />
                        )}
                        <div>
                          <div className="font-medium">{item.name}</div>
                          <div className="text-sm text-gray-500">
                            {'duration' in item ? `${item.duration} min` : `${item.calories} cal`}
                          </div>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(item.date).toLocaleDateString()}
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="workouts" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {workouts
              .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
              .map((workout) => (
                <Card key={workout.id} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-bold text-lg">{workout.name}</h3>
                        <span className="inline-block px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs capitalize">
                          {workout.type}
                        </span>
                      </div>
                      <Dumbbell className="w-6 h-6 text-green-600" />
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <Clock className="w-4 h-4 mx-auto mb-1" />
                        <div className="font-medium">{workout.duration}</div>
                        <div className="text-gray-500">minutes</div>
                      </div>
                      <div className="text-center">
                        <Target className="w-4 h-4 mx-auto mb-1" />
                        <div className="font-medium">{workout.calories}</div>
                        <div className="text-gray-500">calories</div>
                      </div>
                      <div className="text-center">
                        <Calendar className="w-4 h-4 mx-auto mb-1" />
                        <div className="font-medium">{new Date(workout.date).toLocaleDateString()}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </TabsContent>

        <TabsContent value="nutrition" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {meals
              .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
              .map((meal) => (
                <Card key={meal.id} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-bold text-lg">{meal.name}</h3>
                        <span className="inline-block px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs capitalize">
                          {meal.type}
                        </span>
                      </div>
                      <Apple className="w-6 h-6 text-orange-600" />
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <div className="font-medium text-lg">{meal.calories} cal</div>
                        <div className="text-gray-500">Calories</div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs space-y-1">
                          <div>Protein: {meal.protein}g</div>
                          <div>Carbs: {meal.carbs}g</div>
                          <div>Fat: {meal.fat}g</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {workoutTemplates.map((template, index) => (
              <Card key={index} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all cursor-pointer">
                <CardContent className="p-4">
                  <div className="text-center mb-4">
                    <h3 className="font-bold text-lg mb-2">{template.name}</h3>
                    <span className="inline-block px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm capitalize">
                      {template.type}
                    </span>
                  </div>
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-center items-center text-sm">
                      <Clock className="w-4 h-4 mr-1" />
                      {template.duration} minutes
                    </div>
                    <div className="text-xs text-gray-600">
                      {template.exercises.length} exercises
                    </div>
                  </div>
                  <Button 
                    onClick={() => useTemplate(template)}
                    className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                  >
                    Use Template
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Workout Form Modal */}
      <AnimatePresence>
        {showWorkoutForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowWorkoutForm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-2xl p-6 w-full max-w-md"
            >
              <h3 className="text-xl font-bold mb-4">Log Workout</h3>
              <div className="space-y-4">
                <Input
                  placeholder="Workout name"
                  value={newWorkout.name}
                  onChange={(e) => setNewWorkout({ ...newWorkout, name: e.target.value })}
                  className="rounded-xl border-2 border-gray-200 focus:border-green-500"
                />
                
                <select
                  value={newWorkout.type}
                  onChange={(e) => setNewWorkout({ ...newWorkout, type: e.target.value as any })}
                  className="w-full px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-green-500 focus:outline-none"
                >
                  <option value="cardio">Cardio</option>
                  <option value="strength">Strength</option>
                  <option value="flexibility">Flexibility</option>
                  <option value="sports">Sports</option>
                </select>

                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="number"
                    placeholder="Duration (min)"
                    value={newWorkout.duration}
                    onChange={(e) => setNewWorkout({ ...newWorkout, duration: e.target.value })}
                    className="rounded-xl border-2 border-gray-200 focus:border-green-500"
                  />
                  <Input
                    type="number"
                    placeholder="Calories"
                    value={newWorkout.calories}
                    onChange={(e) => setNewWorkout({ ...newWorkout, calories: e.target.value })}
                    className="rounded-xl border-2 border-gray-200 focus:border-green-500"
                  />
                </div>

                <Input
                  type="date"
                  value={newWorkout.date}
                  onChange={(e) => setNewWorkout({ ...newWorkout, date: e.target.value })}
                  className="rounded-xl border-2 border-gray-200 focus:border-green-500"
                />

                <div className="flex space-x-2">
                  <Button onClick={addWorkout} className="flex-1 bg-green-600 hover:bg-green-700">
                    Log Workout
                  </Button>
                  <Button variant="outline" onClick={() => setShowWorkoutForm(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Meal Form Modal */}
      <AnimatePresence>
        {showMealForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowMealForm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-2xl p-6 w-full max-w-md"
            >
              <h3 className="text-xl font-bold mb-4">Log Meal</h3>
              <div className="space-y-4">
                <Input
                  placeholder="Meal name"
                  value={newMeal.name}
                  onChange={(e) => setNewMeal({ ...newMeal, name: e.target.value })}
                  className="rounded-xl border-2 border-gray-200 focus:border-orange-500"
                />
                
                <select
                  value={newMeal.type}
                  onChange={(e) => setNewMeal({ ...newMeal, type: e.target.value as any })}
                  className="w-full px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-orange-500 focus:outline-none"
                >
                  <option value="breakfast">Breakfast</option>
                  <option value="lunch">Lunch</option>
                  <option value="dinner">Dinner</option>
                  <option value="snack">Snack</option>
                </select>

                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="number"
                    placeholder="Calories"
                    value={newMeal.calories}
                    onChange={(e) => setNewMeal({ ...newMeal, calories: e.target.value })}
                    className="rounded-xl border-2 border-gray-200 focus:border-orange-500"
                  />
                  <Input
                    type="number"
                    placeholder="Protein (g)"
                    value={newMeal.protein}
                    onChange={(e) => setNewMeal({ ...newMeal, protein: e.target.value })}
                    className="rounded-xl border-2 border-gray-200 focus:border-orange-500"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="number"
                    placeholder="Carbs (g)"
                    value={newMeal.carbs}
                    onChange={(e) => setNewMeal({ ...newMeal, carbs: e.target.value })}
                    className="rounded-xl border-2 border-gray-200 focus:border-orange-500"
                  />
                  <Input
                    type="number"
                    placeholder="Fat (g)"
                    value={newMeal.fat}
                    onChange={(e) => setNewMeal({ ...newMeal, fat: e.target.value })}
                    className="rounded-xl border-2 border-gray-200 focus:border-orange-500"
                  />
                </div>

                <Input
                  type="date"
                  value={newMeal.date}
                  onChange={(e) => setNewMeal({ ...newMeal, date: e.target.value })}
                  className="rounded-xl border-2 border-gray-200 focus:border-orange-500"
                />

                <div className="flex space-x-2">
                  <Button onClick={addMeal} className="flex-1 bg-orange-600 hover:bg-orange-700">
                    Log Meal
                  </Button>
                  <Button variant="outline" onClick={() => setShowMealForm(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default HealthTracker;
