import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus, Activity, Dumbbell, Apple, Heart, Clock, Calendar, Target,
  Droplets, Moon, Sun, Utensils, Leaf, Timer, TrendingUp, Award,
  BookOpen, Play, Pause, RotateCcw, CheckCircle, AlertCircle,
  Zap, Coffee, Fish, Egg, Wheat, Carrot
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { GlassmorphCard } from '@/components/ui/glassmorphcard';

interface Workout {
  id: number;
  name: string;
  type: 'cardio' | 'strength' | 'flexibility' | 'sports' | 'yoga' | 'pilates';
  duration: number;
  calories: number;
  date: string;
  exercises?: Exercise[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  equipment?: string[];
  notes?: string;
  completed: boolean;
}

interface Exercise {
  id: number;
  name: string;
  sets?: number;
  reps?: number;
  weight?: number;
  duration?: number;
  restTime?: number;
  instructions?: string;
  targetMuscles?: string[];
}

interface Meal {
  id: number;
  name: string;
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
  date: string;
  isVegetarian: boolean;
  isVegan?: boolean;
  ingredients?: string[];
  recipe?: string;
  prepTime?: number;
}

interface WaterIntake {
  id: number;
  amount: number; // in ml
  time: string;
  date: string;
  type: 'water' | 'tea' | 'coffee' | 'juice' | 'other';
}

interface SleepLog {
  id: number;
  date: string;
  bedtime: string;
  wakeTime: string;
  duration: number;
  quality: number; // 1-10
  mood: 'excellent' | 'good' | 'fair' | 'poor';
  notes?: string;
}

interface DietPlan {
  id: number;
  name: string;
  type: 'vegetarian' | 'vegan' | 'mediterranean' | 'keto' | 'balanced';
  duration: number; // in days
  dailyCalories: number;
  macros: {
    protein: number;
    carbs: number;
    fat: number;
  };
  meals: {
    breakfast: Meal[];
    lunch: Meal[];
    dinner: Meal[];
    snacks: Meal[];
  };
  description: string;
  benefits: string[];
}

interface WorkoutPlan {
  id: number;
  name: string;
  type: 'strength' | 'cardio' | 'flexibility' | 'mixed';
  duration: number; // in weeks
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  workoutsPerWeek: number;
  workouts: Workout[];
  description: string;
  goals: string[];
}

const HealthTracker = () => {
  const [workouts, setWorkouts] = useState<Workout[]>([]);
  const [meals, setMeals] = useState<Meal[]>([]);
  const [waterIntakes, setWaterIntakes] = useState<WaterIntake[]>([]);
  const [sleepLogs, setSleepLogs] = useState<SleepLog[]>([]);
  const [showWorkoutForm, setShowWorkoutForm] = useState(false);
  const [showMealForm, setShowMealForm] = useState(false);
  const [showWaterForm, setShowWaterForm] = useState(false);
  const [showSleepForm, setShowSleepForm] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedDietPlan, setSelectedDietPlan] = useState<DietPlan | null>(null);
  const [selectedWorkoutPlan, setSelectedWorkoutPlan] = useState<WorkoutPlan | null>(null);
  const [isWorkoutActive, setIsWorkoutActive] = useState(false);
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [workoutTimer, setWorkoutTimer] = useState(0);
  const [restTimer, setRestTimer] = useState(0);

  const [newWorkout, setNewWorkout] = useState({
    name: '',
    type: 'cardio' as 'cardio' | 'strength' | 'flexibility' | 'sports' | 'yoga' | 'pilates',
    duration: '',
    calories: '',
    difficulty: 'beginner' as 'beginner' | 'intermediate' | 'advanced',
    date: new Date().toISOString().split('T')[0]
  });

  const [newMeal, setNewMeal] = useState({
    name: '',
    type: 'breakfast' as const,
    calories: '',
    protein: '',
    carbs: '',
    fat: '',
    isVegetarian: true,
    date: new Date().toISOString().split('T')[0]
  });

  const [newWaterIntake, setNewWaterIntake] = useState({
    amount: '',
    type: 'water' as 'water' | 'tea' | 'coffee' | 'juice' | 'other',
    time: new Date().toTimeString().slice(0, 5)
  });

  const [newSleepLog, setNewSleepLog] = useState({
    bedtime: '',
    wakeTime: '',
    quality: 7,
    mood: 'good' as 'excellent' | 'good' | 'fair' | 'poor',
    notes: '',
    date: new Date().toISOString().split('T')[0]
  });

  // Vegetarian Diet Plans
  const vegetarianDietPlans: DietPlan[] = [
    {
      id: 1,
      name: "Mediterranean Vegetarian",
      type: "vegetarian",
      duration: 7,
      dailyCalories: 2000,
      macros: { protein: 20, carbs: 50, fat: 30 },
      description: "A plant-based Mediterranean diet rich in vegetables, fruits, whole grains, and healthy fats.",
      benefits: ["Heart health", "Weight management", "Anti-inflammatory", "Rich in antioxidants"],
      meals: {
        breakfast: [
          {
            id: 1, name: "Greek Yogurt with Berries", type: "breakfast", calories: 300,
            protein: 15, carbs: 35, fat: 8, isVegetarian: true,
            ingredients: ["Greek yogurt", "Mixed berries", "Honey", "Granola"],
            prepTime: 5, date: new Date().toISOString().split('T')[0]
          }
        ],
        lunch: [
          {
            id: 2, name: "Quinoa Mediterranean Bowl", type: "lunch", calories: 450,
            protein: 18, carbs: 55, fat: 15, isVegetarian: true,
            ingredients: ["Quinoa", "Chickpeas", "Cucumber", "Tomatoes", "Feta", "Olive oil"],
            prepTime: 20, date: new Date().toISOString().split('T')[0]
          }
        ],
        dinner: [
          {
            id: 3, name: "Eggplant Moussaka", type: "dinner", calories: 400,
            protein: 16, carbs: 30, fat: 22, isVegetarian: true,
            ingredients: ["Eggplant", "Lentils", "Tomato sauce", "Bechamel", "Cheese"],
            prepTime: 60, date: new Date().toISOString().split('T')[0]
          }
        ],
        snacks: [
          {
            id: 4, name: "Hummus with Vegetables", type: "snack", calories: 150,
            protein: 6, carbs: 18, fat: 7, isVegetarian: true,
            ingredients: ["Hummus", "Carrots", "Bell peppers", "Cucumber"],
            prepTime: 5, date: new Date().toISOString().split('T')[0]
          }
        ]
      }
    },
    {
      id: 2,
      name: "High-Protein Vegetarian",
      type: "vegetarian",
      duration: 7,
      dailyCalories: 2200,
      macros: { protein: 30, carbs: 40, fat: 30 },
      description: "A protein-rich vegetarian diet focusing on legumes, nuts, seeds, and dairy.",
      benefits: ["Muscle building", "Satiety", "Balanced nutrition", "Energy boost"],
      meals: {
        breakfast: [
          {
            id: 5, name: "Protein Smoothie Bowl", type: "breakfast", calories: 350,
            protein: 25, carbs: 30, fat: 12, isVegetarian: true,
            ingredients: ["Protein powder", "Banana", "Spinach", "Almond milk", "Chia seeds"],
            prepTime: 10, date: new Date().toISOString().split('T')[0]
          }
        ],
        lunch: [
          {
            id: 6, name: "Lentil Power Salad", type: "lunch", calories: 500,
            protein: 22, carbs: 45, fat: 18, isVegetarian: true,
            ingredients: ["Red lentils", "Quinoa", "Avocado", "Pumpkin seeds", "Tahini dressing"],
            prepTime: 25, date: new Date().toISOString().split('T')[0]
          }
        ],
        dinner: [
          {
            id: 7, name: "Tofu Stir-fry with Brown Rice", type: "dinner", calories: 450,
            protein: 20, carbs: 40, fat: 20, isVegetarian: true,
            ingredients: ["Firm tofu", "Brown rice", "Mixed vegetables", "Soy sauce", "Sesame oil"],
            prepTime: 30, date: new Date().toISOString().split('T')[0]
          }
        ],
        snacks: [
          {
            id: 8, name: "Greek Yogurt with Nuts", type: "snack", calories: 200,
            protein: 12, carbs: 15, fat: 10, isVegetarian: true,
            ingredients: ["Greek yogurt", "Mixed nuts", "Honey"],
            prepTime: 2, date: new Date().toISOString().split('T')[0]
          }
        ]
      }
    }
  ];

  // Enhanced Workout Templates
  const workoutTemplates: WorkoutPlan[] = [
    {
      id: 1,
      name: "Beginner Full Body",
      type: "strength",
      duration: 4,
      difficulty: "beginner",
      workoutsPerWeek: 3,
      description: "A comprehensive beginner-friendly full-body workout plan",
      goals: ["Build strength", "Improve form", "Establish routine"],
      workouts: [
        {
          id: 1, name: "Full Body Strength A", type: "strength", duration: 45, calories: 300,
          difficulty: "beginner", equipment: ["Dumbbells", "Mat"], completed: false,
          date: new Date().toISOString().split('T')[0],
          exercises: [
            { id: 1, name: "Bodyweight Squats", sets: 3, reps: 12, restTime: 60, targetMuscles: ["Legs", "Glutes"] },
            { id: 2, name: "Push-ups (Modified)", sets: 3, reps: 8, restTime: 60, targetMuscles: ["Chest", "Arms"] },
            { id: 3, name: "Plank", duration: 30, restTime: 60, targetMuscles: ["Core"] },
            { id: 4, name: "Glute Bridges", sets: 3, reps: 15, restTime: 60, targetMuscles: ["Glutes", "Hamstrings"] }
          ]
        }
      ]
    },
    {
      id: 2,
      name: "HIIT Cardio Blast",
      type: "cardio",
      duration: 2,
      difficulty: "intermediate",
      workoutsPerWeek: 4,
      description: "High-intensity interval training for fat burning and cardiovascular health",
      goals: ["Burn calories", "Improve cardio", "Boost metabolism"],
      workouts: [
        {
          id: 2, name: "HIIT Circuit", type: "cardio", duration: 30, calories: 400,
          difficulty: "intermediate", equipment: ["None"], completed: false,
          date: new Date().toISOString().split('T')[0],
          exercises: [
            { id: 5, name: "Jumping Jacks", duration: 45, restTime: 15, targetMuscles: ["Full body"] },
            { id: 6, name: "Burpees", duration: 30, restTime: 30, targetMuscles: ["Full body"] },
            { id: 7, name: "Mountain Climbers", duration: 45, restTime: 15, targetMuscles: ["Core", "Cardio"] },
            { id: 8, name: "High Knees", duration: 30, restTime: 30, targetMuscles: ["Legs", "Cardio"] }
          ]
        }
      ]
    },
    {
      id: 3,
      name: "Yoga & Flexibility",
      type: "flexibility",
      duration: 6,
      difficulty: "beginner",
      workoutsPerWeek: 5,
      description: "Gentle yoga flows and stretching routines for flexibility and mindfulness",
      goals: ["Improve flexibility", "Reduce stress", "Better posture"],
      workouts: [
        {
          id: 3, name: "Morning Yoga Flow", type: "yoga", duration: 60, calories: 200,
          difficulty: "beginner", equipment: ["Yoga mat"], completed: false,
          date: new Date().toISOString().split('T')[0],
          exercises: [
            { id: 9, name: "Sun Salutation A", sets: 5, restTime: 30, targetMuscles: ["Full body"] },
            { id: 10, name: "Warrior I", duration: 60, restTime: 30, targetMuscles: ["Legs", "Core"] },
            { id: 11, name: "Downward Dog", duration: 90, restTime: 30, targetMuscles: ["Arms", "Back"] },
            { id: 12, name: "Child's Pose", duration: 120, restTime: 0, targetMuscles: ["Back", "Hips"] }
          ]
        }
      ]
    }
  ];

  // Load data from localStorage
  useEffect(() => {
    const savedWorkouts = localStorage.getItem('healthTracker_workouts');
    const savedMeals = localStorage.getItem('healthTracker_meals');
    const savedWaterIntakes = localStorage.getItem('healthTracker_waterIntakes');
    const savedSleepLogs = localStorage.getItem('healthTracker_sleepLogs');

    if (savedWorkouts) setWorkouts(JSON.parse(savedWorkouts));
    if (savedMeals) setMeals(JSON.parse(savedMeals));
    if (savedWaterIntakes) setWaterIntakes(JSON.parse(savedWaterIntakes));
    if (savedSleepLogs) setSleepLogs(JSON.parse(savedSleepLogs));
  }, []);

  // Save data to localStorage
  useEffect(() => {
    localStorage.setItem('healthTracker_workouts', JSON.stringify(workouts));
  }, [workouts]);

  useEffect(() => {
    localStorage.setItem('healthTracker_meals', JSON.stringify(meals));
  }, [meals]);

  useEffect(() => {
    localStorage.setItem('healthTracker_waterIntakes', JSON.stringify(waterIntakes));
  }, [waterIntakes]);

  useEffect(() => {
    localStorage.setItem('healthTracker_sleepLogs', JSON.stringify(sleepLogs));
  }, [sleepLogs]);

  // Workout timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isWorkoutActive) {
      interval = setInterval(() => {
        setWorkoutTimer(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isWorkoutActive]);

  // Utility functions
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const calculateDailyCalories = (date: string) => {
    return meals
      .filter(meal => meal.date === date)
      .reduce((total, meal) => total + meal.calories, 0);
  };

  const calculateDailyWater = (date: string) => {
    return waterIntakes
      .filter(intake => intake.date === date)
      .reduce((total, intake) => total + intake.amount, 0);
  };

  const getTodaysStats = () => {
    const today = new Date().toISOString().split('T')[0];
    const todayMeals = meals.filter(meal => meal.date === today);
    const todayWorkouts = workouts.filter(workout => workout.date === today);
    const todayWater = calculateDailyWater(today);
    const todaySleep = sleepLogs.find(log => log.date === today);

    return {
      calories: todayMeals.reduce((sum, meal) => sum + meal.calories, 0),
      protein: todayMeals.reduce((sum, meal) => sum + meal.protein, 0),
      workouts: todayWorkouts.length,
      workoutMinutes: todayWorkouts.reduce((sum, workout) => sum + workout.duration, 0),
      water: todayWater,
      sleep: todaySleep?.duration || 0,
      sleepQuality: todaySleep?.quality || 0
    };
  };

  const addWorkout = () => {
    if (!newWorkout.name || !newWorkout.duration) return;

    const workout: Workout = {
      id: Date.now(),
      name: newWorkout.name,
      type: newWorkout.type,
      duration: parseInt(newWorkout.duration),
      calories: parseInt(newWorkout.calories) || 0,
      difficulty: newWorkout.difficulty,
      date: newWorkout.date,
      completed: false,
      equipment: [],
      exercises: []
    };

    setWorkouts(prev => [workout, ...prev]);
    setNewWorkout({
      name: '',
      type: 'cardio',
      duration: '',
      calories: '',
      difficulty: 'beginner',
      date: new Date().toISOString().split('T')[0]
    });
    setShowWorkoutForm(false);
  };

  const addMeal = () => {
    if (!newMeal.name || !newMeal.calories) return;

    const meal: Meal = {
      id: Date.now(),
      name: newMeal.name,
      type: newMeal.type,
      calories: parseInt(newMeal.calories),
      protein: parseInt(newMeal.protein) || 0,
      carbs: parseInt(newMeal.carbs) || 0,
      fat: parseInt(newMeal.fat) || 0,
      isVegetarian: newMeal.isVegetarian,
      date: newMeal.date,
      ingredients: []
    };

    setMeals(prev => [meal, ...prev]);
    setNewMeal({
      name: '',
      type: 'breakfast',
      calories: '',
      protein: '',
      carbs: '',
      fat: '',
      isVegetarian: true,
      date: new Date().toISOString().split('T')[0]
    });
    setShowMealForm(false);
  };

  const addWaterIntake = () => {
    if (!newWaterIntake.amount) return;

    const waterIntake: WaterIntake = {
      id: Date.now(),
      amount: parseInt(newWaterIntake.amount),
      type: newWaterIntake.type,
      time: newWaterIntake.time,
      date: new Date().toISOString().split('T')[0]
    };

    setWaterIntakes(prev => [waterIntake, ...prev]);
    setNewWaterIntake({
      amount: '',
      type: 'water',
      time: new Date().toTimeString().slice(0, 5)
    });
    setShowWaterForm(false);
  };

  const addSleepLog = () => {
    if (!newSleepLog.bedtime || !newSleepLog.wakeTime) return;

    const bedtime = new Date(`2000-01-01T${newSleepLog.bedtime}`);
    const wakeTime = new Date(`2000-01-01T${newSleepLog.wakeTime}`);
    let duration = (wakeTime.getTime() - bedtime.getTime()) / (1000 * 60 * 60);

    if (duration < 0) duration += 24; // Handle overnight sleep

    const sleepLog: SleepLog = {
      id: Date.now(),
      date: newSleepLog.date,
      bedtime: newSleepLog.bedtime,
      wakeTime: newSleepLog.wakeTime,
      duration: Math.round(duration * 100) / 100,
      quality: newSleepLog.quality,
      mood: newSleepLog.mood,
      notes: newSleepLog.notes
    };

    setSleepLogs(prev => [sleepLog, ...prev]);
    setNewSleepLog({
      bedtime: '',
      wakeTime: '',
      quality: 7,
      mood: 'good',
      notes: '',
      date: new Date().toISOString().split('T')[0]
    });
    setShowSleepForm(false);
  };

  const startWorkoutFromTemplate = (template: WorkoutPlan) => {
    setSelectedWorkoutPlan(template);
    setIsWorkoutActive(true);
    setCurrentExerciseIndex(0);
    setWorkoutTimer(0);
  };

  const todaysStats = getTodaysStats();

  useEffect(() => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen p-4 pb-24 bg-gradient-to-br from-green-50 via-white to-blue-50"
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Enhanced Health Tracker</h1>
          <p className="text-gray-600">Complete wellness tracking with diet plans, workouts, sleep & hydration</p>
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={() => setShowWorkoutForm(true)}
            className="bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 rounded-full p-3"
          >
            <Dumbbell className="w-5 h-5" />
          </Button>
          <Button
            onClick={() => setShowMealForm(true)}
            className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 rounded-full p-3"
          >
            <Utensils className="w-5 h-5" />
          </Button>
          <Button
            onClick={() => setShowWaterForm(true)}
            className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 rounded-full p-3"
          >
            <Droplets className="w-5 h-5" />
          </Button>
          <Button
            onClick={() => setShowSleepForm(true)}
            className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 rounded-full p-3"
          >
            <Moon className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Today's Stats Dashboard */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mb-8">
        <GlassmorphCard className="p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <Apple className="w-6 h-6 text-orange-500" />
          </div>
          <p className="text-2xl font-bold text-gray-800">{todaysStats.calories}</p>
          <p className="text-sm text-gray-600">Calories</p>
        </GlassmorphCard>

        <GlassmorphCard className="p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <Zap className="w-6 h-6 text-red-500" />
          </div>
          <p className="text-2xl font-bold text-gray-800">{todaysStats.protein}g</p>
          <p className="text-sm text-gray-600">Protein</p>
        </GlassmorphCard>

        <GlassmorphCard className="p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <Dumbbell className="w-6 h-6 text-green-500" />
          </div>
          <p className="text-2xl font-bold text-gray-800">{todaysStats.workouts}</p>
          <p className="text-sm text-gray-600">Workouts</p>
        </GlassmorphCard>

        <GlassmorphCard className="p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <Clock className="w-6 h-6 text-blue-500" />
          </div>
          <p className="text-2xl font-bold text-gray-800">{todaysStats.workoutMinutes}</p>
          <p className="text-sm text-gray-600">Minutes</p>
        </GlassmorphCard>

        <GlassmorphCard className="p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <Droplets className="w-6 h-6 text-cyan-500" />
          </div>
          <p className="text-2xl font-bold text-gray-800">{todaysStats.water}ml</p>
          <p className="text-sm text-gray-600">Water</p>
        </GlassmorphCard>

        <GlassmorphCard className="p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <Moon className="w-6 h-6 text-purple-500" />
          </div>
          <p className="text-2xl font-bold text-gray-800">{todaysStats.sleep}h</p>
          <p className="text-sm text-gray-600">Sleep</p>
        </GlassmorphCard>

        <GlassmorphCard className="p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <Heart className="w-6 h-6 text-pink-500" />
          </div>
          <p className="text-2xl font-bold text-gray-800">{todaysStats.sleepQuality}/10</p>
          <p className="text-sm text-gray-600">Quality</p>
        </GlassmorphCard>
      </div>
      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="workouts">Workouts</TabsTrigger>
          <TabsTrigger value="nutrition">Nutrition</TabsTrigger>
          <TabsTrigger value="hydration">Hydration</TabsTrigger>
          <TabsTrigger value="sleep">Sleep</TabsTrigger>
          <TabsTrigger value="plans">Plans</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Quick Actions */}
            <GlassmorphCard className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center">
                  <Zap className="w-5 h-5 mr-2" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <Button
                    onClick={() => setShowWorkoutForm(true)}
                    className="h-20 flex flex-col items-center justify-center bg-gradient-to-r from-green-500 to-teal-500"
                  >
                    <Dumbbell className="w-6 h-6 mb-2" />
                    Log Workout
                  </Button>
                  <Button
                    onClick={() => setShowMealForm(true)}
                    className="h-20 flex flex-col items-center justify-center bg-gradient-to-r from-orange-500 to-red-500"
                  >
                    <Utensils className="w-6 h-6 mb-2" />
                    Log Meal
                  </Button>
                  <Button
                    onClick={() => setShowWaterForm(true)}
                    className="h-20 flex flex-col items-center justify-center bg-gradient-to-r from-blue-500 to-cyan-500"
                  >
                    <Droplets className="w-6 h-6 mb-2" />
                    Add Water
                  </Button>
                  <Button
                    onClick={() => setShowSleepForm(true)}
                    className="h-20 flex flex-col items-center justify-center bg-gradient-to-r from-purple-500 to-indigo-500"
                  >
                    <Moon className="w-6 h-6 mb-2" />
                    Log Sleep
                  </Button>
                </div>
              </CardContent>
            </GlassmorphCard>

            {/* Weekly Progress */}
            <GlassmorphCard className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Weekly Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Workout Goal</span>
                      <span>{todaysStats.workouts}/5 days</span>
                    </div>
                    <Progress value={(todaysStats.workouts / 5) * 100} />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Water Goal</span>
                      <span>{todaysStats.water}/2000ml</span>
                    </div>
                    <Progress value={(todaysStats.water / 2000) * 100} />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Sleep Quality</span>
                      <span>{todaysStats.sleepQuality}/10</span>
                    </div>
                    <Progress value={(todaysStats.sleepQuality / 10) * 100} />
                  </div>
                </div>
              </CardContent>
            </GlassmorphCard>
          </div>
        </TabsContent>

        <TabsContent value="workouts" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Workout Templates */}
            <div className="lg:col-span-1">
              <GlassmorphCard className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center">
                    <BookOpen className="w-5 h-5 mr-2" />
                    Workout Plans
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {workoutTemplates.map((template) => (
                      <div key={template.id} className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                           onClick={() => startWorkoutFromTemplate(template)}>
                        <h4 className="font-semibold">{template.name}</h4>
                        <p className="text-sm text-gray-600">{template.description}</p>
                        <div className="flex items-center justify-between mt-2">
                          <Badge variant="outline">{template.difficulty}</Badge>
                          <span className="text-sm text-gray-500">{template.duration} weeks</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </GlassmorphCard>
            </div>

            {/* Recent Workouts */}
            <div className="lg:col-span-2">
              <GlassmorphCard className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Activity className="w-5 h-5 mr-2" />
                      Recent Workouts
                    </div>
                    <Button onClick={() => setShowWorkoutForm(true)} size="sm">
                      <Plus className="w-4 h-4 mr-2" />
                      Add Workout
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {workouts.slice(0, 5).map((workout) => (
                      <div key={workout.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <h4 className="font-semibold">{workout.name}</h4>
                          <p className="text-sm text-gray-600">
                            {workout.type} • {workout.duration} min • {workout.calories} cal
                          </p>
                          <p className="text-xs text-gray-500">{workout.date}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant={workout.completed ? "default" : "outline"}>
                            {workout.completed ? "Completed" : "Pending"}
                          </Badge>
                          {workout.completed && <CheckCircle className="w-5 h-5 text-green-500" />}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </GlassmorphCard>
            </div>
          </div>
        </TabsContent>
        <TabsContent value="nutrition" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Diet Plans */}
            <div className="lg:col-span-1">
              <GlassmorphCard className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center">
                    <Leaf className="w-5 h-5 mr-2" />
                    Vegetarian Diet Plans
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {vegetarianDietPlans.map((plan) => (
                      <div key={plan.id} className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                           onClick={() => setSelectedDietPlan(plan)}>
                        <h4 className="font-semibold">{plan.name}</h4>
                        <p className="text-sm text-gray-600">{plan.description}</p>
                        <div className="flex items-center justify-between mt-2">
                          <Badge variant="outline">{plan.type}</Badge>
                          <span className="text-sm text-gray-500">{plan.dailyCalories} cal/day</span>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {plan.benefits.slice(0, 2).map((benefit, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {benefit}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </GlassmorphCard>
            </div>

            {/* Recent Meals */}
            <div className="lg:col-span-2">
              <GlassmorphCard className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Utensils className="w-5 h-5 mr-2" />
                      Recent Meals
                    </div>
                    <Button onClick={() => setShowMealForm(true)} size="sm">
                      <Plus className="w-4 h-4 mr-2" />
                      Add Meal
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {meals.slice(0, 5).map((meal) => (
                      <div key={meal.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-full ${
                            meal.type === 'breakfast' ? 'bg-yellow-100' :
                            meal.type === 'lunch' ? 'bg-orange-100' :
                            meal.type === 'dinner' ? 'bg-red-100' : 'bg-purple-100'
                          }`}>
                            {meal.type === 'breakfast' ? <Coffee className="w-4 h-4" /> :
                             meal.type === 'lunch' ? <Sun className="w-4 h-4" /> :
                             meal.type === 'dinner' ? <Moon className="w-4 h-4" /> : <Apple className="w-4 h-4" />}
                          </div>
                          <div>
                            <h4 className="font-semibold">{meal.name}</h4>
                            <p className="text-sm text-gray-600">
                              {meal.calories} cal • P: {meal.protein}g • C: {meal.carbs}g • F: {meal.fat}g
                            </p>
                            <div className="flex items-center space-x-2 mt-1">
                              {meal.isVegetarian && (
                                <Badge variant="outline" className="text-xs">
                                  <Leaf className="w-3 h-3 mr-1" />
                                  Vegetarian
                                </Badge>
                              )}
                              <span className="text-xs text-gray-500">{meal.date}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </GlassmorphCard>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="hydration" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Water Intake Tracker */}
            <GlassmorphCard className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center">
                  <Droplets className="w-5 h-5 mr-2" />
                  Daily Hydration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-6">
                  <div className="text-4xl font-bold text-blue-600 mb-2">
                    {todaysStats.water}ml
                  </div>
                  <div className="text-sm text-gray-600">of 2000ml goal</div>
                  <Progress value={(todaysStats.water / 2000) * 100} className="mt-4" />
                </div>

                <div className="grid grid-cols-4 gap-2 mb-4">
                  {[250, 500, 750, 1000].map((amount) => (
                    <Button
                      key={amount}
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setNewWaterIntake(prev => ({ ...prev, amount: amount.toString() }));
                        addWaterIntake();
                      }}
                      className="h-12 flex flex-col"
                    >
                      <Droplets className="w-4 h-4 mb-1" />
                      {amount}ml
                    </Button>
                  ))}
                </div>

                <Button
                  onClick={() => setShowWaterForm(true)}
                  className="w-full bg-gradient-to-r from-blue-500 to-cyan-500"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Custom Amount
                </Button>
              </CardContent>
            </GlassmorphCard>

            {/* Recent Water Intake */}
            <GlassmorphCard className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center">
                  <Clock className="w-5 h-5 mr-2" />
                  Today's Intake
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {waterIntakes
                    .filter(intake => intake.date === new Date().toISOString().split('T')[0])
                    .slice(0, 8)
                    .map((intake) => (
                      <div key={intake.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-blue-100 rounded-full">
                            <Droplets className="w-4 h-4 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium">{intake.amount}ml</div>
                            <div className="text-sm text-gray-500">{intake.type}</div>
                          </div>
                        </div>
                        <div className="text-sm text-gray-500">{intake.time}</div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </GlassmorphCard>
          </div>
        </TabsContent>
        <TabsContent value="sleep" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Sleep Overview */}
            <GlassmorphCard className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center">
                  <Moon className="w-5 h-5 mr-2" />
                  Sleep Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-6">
                  <div className="text-4xl font-bold text-purple-600 mb-2">
                    {todaysStats.sleep}h
                  </div>
                  <div className="text-sm text-gray-600">Last night's sleep</div>
                  <div className="flex items-center justify-center mt-2">
                    {[...Array(5)].map((_, i) => (
                      <Heart
                        key={i}
                        className={`w-4 h-4 ${i < todaysStats.sleepQuality / 2 ? 'text-purple-500 fill-current' : 'text-gray-300'}`}
                      />
                    ))}
                    <span className="ml-2 text-sm text-gray-600">{todaysStats.sleepQuality}/10</span>
                  </div>
                </div>

                <Button
                  onClick={() => setShowSleepForm(true)}
                  className="w-full bg-gradient-to-r from-purple-500 to-indigo-500"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Log Sleep
                </Button>
              </CardContent>
            </GlassmorphCard>

            {/* Recent Sleep Logs */}
            <GlassmorphCard className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center">
                  <Clock className="w-5 h-5 mr-2" />
                  Sleep History
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {sleepLogs.slice(0, 5).map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-purple-100 rounded-full">
                          <Moon className="w-4 h-4 text-purple-600" />
                        </div>
                        <div>
                          <div className="font-medium">{log.duration}h sleep</div>
                          <div className="text-sm text-gray-500">
                            {log.bedtime} - {log.wakeTime}
                          </div>
                          <div className="flex items-center mt-1">
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              log.mood === 'excellent' ? 'bg-green-100 text-green-800' :
                              log.mood === 'good' ? 'bg-blue-100 text-blue-800' :
                              log.mood === 'fair' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {log.mood}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">{log.quality}/10</div>
                        <div className="text-xs text-gray-500">{log.date}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </GlassmorphCard>
          </div>
        </TabsContent>

        <TabsContent value="plans" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Selected Diet Plan */}
            {selectedDietPlan && (
              <GlassmorphCard className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Leaf className="w-5 h-5 mr-2" />
                      {selectedDietPlan.name}
                    </div>
                    <Badge variant="outline">{selectedDietPlan.type}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{selectedDietPlan.description}</p>

                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{selectedDietPlan.dailyCalories}</div>
                      <div className="text-sm text-gray-600">Calories</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{selectedDietPlan.macros.protein}%</div>
                      <div className="text-sm text-gray-600">Protein</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{selectedDietPlan.macros.carbs}%</div>
                      <div className="text-sm text-gray-600">Carbs</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-semibold">Benefits:</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedDietPlan.benefits.map((benefit, index) => (
                        <Badge key={index} variant="secondary">{benefit}</Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </GlassmorphCard>
            )}

            {/* Selected Workout Plan */}
            {selectedWorkoutPlan && (
              <GlassmorphCard className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Dumbbell className="w-5 h-5 mr-2" />
                      {selectedWorkoutPlan.name}
                    </div>
                    <Badge variant="outline">{selectedWorkoutPlan.difficulty}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{selectedWorkoutPlan.description}</p>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{selectedWorkoutPlan.duration}</div>
                      <div className="text-sm text-gray-600">Weeks</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{selectedWorkoutPlan.workoutsPerWeek}</div>
                      <div className="text-sm text-gray-600">Per Week</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-semibold">Goals:</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedWorkoutPlan.goals.map((goal, index) => (
                        <Badge key={index} variant="secondary">{goal}</Badge>
                      ))}
                    </div>
                  </div>

                  {isWorkoutActive && (
                    <div className="mt-4 p-4 bg-green-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-semibold">Workout Active</span>
                        <span className="text-green-600 font-mono">{formatTime(workoutTimer)}</span>
                      </div>
                      <Progress value={(currentExerciseIndex / selectedWorkoutPlan.workouts[0].exercises!.length) * 100} />
                      <div className="flex space-x-2 mt-2">
                        <Button size="sm" onClick={() => setIsWorkoutActive(false)}>
                          <Pause className="w-4 h-4 mr-1" />
                          Pause
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => {
                          setIsWorkoutActive(false);
                          setWorkoutTimer(0);
                          setCurrentExerciseIndex(0);
                        }}>
                          <RotateCcw className="w-4 h-4 mr-1" />
                          Reset
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </GlassmorphCard>
            )}
          </div>
        </TabsContent>
      </Tabs>
      {/* Forms */}
      <AnimatePresence>
        {/* Workout Form */}
        {showWorkoutForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowWorkoutForm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-2xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto"
            >
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <Dumbbell className="w-5 h-5 mr-2" />
                Log Workout
              </h3>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="workout-name">Workout Name</Label>
                  <Input
                    id="workout-name"
                    placeholder="e.g., Morning Run"
                    value={newWorkout.name}
                    onChange={(e) => setNewWorkout({ ...newWorkout, name: e.target.value })}
                  />
                </div>

                <div>
                  <Label htmlFor="workout-type">Type</Label>
                  <Select value={newWorkout.type} onValueChange={(value: any) => setNewWorkout({ ...newWorkout, type: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select workout type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cardio">Cardio</SelectItem>
                      <SelectItem value="strength">Strength</SelectItem>
                      <SelectItem value="flexibility">Flexibility</SelectItem>
                      <SelectItem value="yoga">Yoga</SelectItem>
                      <SelectItem value="pilates">Pilates</SelectItem>
                      <SelectItem value="sports">Sports</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="workout-duration">Duration (min)</Label>
                    <Input
                      id="workout-duration"
                      type="number"
                      placeholder="30"
                      value={newWorkout.duration}
                      onChange={(e) => setNewWorkout({ ...newWorkout, duration: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="workout-calories">Calories</Label>
                    <Input
                      id="workout-calories"
                      type="number"
                      placeholder="300"
                      value={newWorkout.calories}
                      onChange={(e) => setNewWorkout({ ...newWorkout, calories: e.target.value })}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="workout-difficulty">Difficulty</Label>
                  <Select value={newWorkout.difficulty} onValueChange={(value: any) => setNewWorkout({ ...newWorkout, difficulty: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="workout-date">Date</Label>
                  <Input
                    id="workout-date"
                    type="date"
                    value={newWorkout.date}
                    onChange={(e) => setNewWorkout({ ...newWorkout, date: e.target.value })}
                  />
                </div>

                <div className="flex space-x-3 pt-4">
                  <Button onClick={addWorkout} className="flex-1 bg-gradient-to-r from-green-500 to-teal-500">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Workout
                  </Button>
                  <Button variant="outline" onClick={() => setShowWorkoutForm(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}

        {/* Meal Form */}
        {showMealForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowMealForm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-2xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto"
            >
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <Utensils className="w-5 h-5 mr-2" />
                Log Meal
              </h3>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="meal-name">Meal Name</Label>
                  <Input
                    id="meal-name"
                    placeholder="e.g., Quinoa Salad"
                    value={newMeal.name}
                    onChange={(e) => setNewMeal({ ...newMeal, name: e.target.value })}
                  />
                </div>

                <div>
                  <Label htmlFor="meal-type">Meal Type</Label>
                  <Select value={newMeal.type} onValueChange={(value: any) => setNewMeal({ ...newMeal, type: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select meal type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="breakfast">Breakfast</SelectItem>
                      <SelectItem value="lunch">Lunch</SelectItem>
                      <SelectItem value="dinner">Dinner</SelectItem>
                      <SelectItem value="snack">Snack</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="meal-calories">Calories</Label>
                    <Input
                      id="meal-calories"
                      type="number"
                      placeholder="400"
                      value={newMeal.calories}
                      onChange={(e) => setNewMeal({ ...newMeal, calories: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="meal-protein">Protein (g)</Label>
                    <Input
                      id="meal-protein"
                      type="number"
                      placeholder="20"
                      value={newMeal.protein}
                      onChange={(e) => setNewMeal({ ...newMeal, protein: e.target.value })}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="meal-carbs">Carbs (g)</Label>
                    <Input
                      id="meal-carbs"
                      type="number"
                      placeholder="45"
                      value={newMeal.carbs}
                      onChange={(e) => setNewMeal({ ...newMeal, carbs: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="meal-fat">Fat (g)</Label>
                    <Input
                      id="meal-fat"
                      type="number"
                      placeholder="15"
                      value={newMeal.fat}
                      onChange={(e) => setNewMeal({ ...newMeal, fat: e.target.value })}
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="vegetarian"
                    checked={newMeal.isVegetarian}
                    onChange={(e) => setNewMeal({ ...newMeal, isVegetarian: e.target.checked })}
                    className="rounded"
                  />
                  <Label htmlFor="vegetarian" className="flex items-center">
                    <Leaf className="w-4 h-4 mr-1 text-green-500" />
                    Vegetarian
                  </Label>
                </div>

                <div>
                  <Label htmlFor="meal-date">Date</Label>
                  <Input
                    id="meal-date"
                    type="date"
                    value={newMeal.date}
                    onChange={(e) => setNewMeal({ ...newMeal, date: e.target.value })}
                  />
                </div>

                <div className="flex space-x-3 pt-4">
                  <Button onClick={addMeal} className="flex-1 bg-gradient-to-r from-orange-500 to-red-500">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Meal
                  </Button>
                  <Button variant="outline" onClick={() => setShowMealForm(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}

        {/* Water Form */}
        {showWaterForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowWaterForm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-2xl p-6 w-full max-w-md"
            >
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <Droplets className="w-5 h-5 mr-2" />
                Log Water Intake
              </h3>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="water-amount">Amount (ml)</Label>
                  <Input
                    id="water-amount"
                    type="number"
                    placeholder="250"
                    value={newWaterIntake.amount}
                    onChange={(e) => setNewWaterIntake({ ...newWaterIntake, amount: e.target.value })}
                  />
                </div>

                <div>
                  <Label htmlFor="water-type">Type</Label>
                  <Select value={newWaterIntake.type} onValueChange={(value: any) => setNewWaterIntake({ ...newWaterIntake, type: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select drink type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="water">Water</SelectItem>
                      <SelectItem value="tea">Tea</SelectItem>
                      <SelectItem value="coffee">Coffee</SelectItem>
                      <SelectItem value="juice">Juice</SelectItem>
                      <SelectItem value="smoothie">Smoothie</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="water-time">Time</Label>
                  <Input
                    id="water-time"
                    type="time"
                    value={newWaterIntake.time}
                    onChange={(e) => setNewWaterIntake({ ...newWaterIntake, time: e.target.value })}
                  />
                </div>

                <div className="flex space-x-3 pt-4">
                  <Button onClick={addWaterIntake} className="flex-1 bg-gradient-to-r from-blue-500 to-cyan-500">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Water
                  </Button>
                  <Button variant="outline" onClick={() => setShowWaterForm(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}

        {/* Sleep Form */}
        {showSleepForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowSleepForm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-2xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto"
            >
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <Moon className="w-5 h-5 mr-2" />
                Log Sleep
              </h3>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="bedtime">Bedtime</Label>
                    <Input
                      id="bedtime"
                      type="time"
                      value={newSleepLog.bedtime}
                      onChange={(e) => setNewSleepLog({ ...newSleepLog, bedtime: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="wake-time">Wake Time</Label>
                    <Input
                      id="wake-time"
                      type="time"
                      value={newSleepLog.wakeTime}
                      onChange={(e) => setNewSleepLog({ ...newSleepLog, wakeTime: e.target.value })}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="sleep-quality">Sleep Quality (1-10)</Label>
                  <Input
                    id="sleep-quality"
                    type="number"
                    min="1"
                    max="10"
                    placeholder="8"
                    value={newSleepLog.quality}
                    onChange={(e) => setNewSleepLog({ ...newSleepLog, quality: parseInt(e.target.value) || 0 })}
                  />
                </div>

                <div>
                  <Label htmlFor="sleep-mood">Morning Mood</Label>
                  <Select value={newSleepLog.mood} onValueChange={(value: any) => setNewSleepLog({ ...newSleepLog, mood: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="How did you feel?" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="excellent">Excellent</SelectItem>
                      <SelectItem value="good">Good</SelectItem>
                      <SelectItem value="fair">Fair</SelectItem>
                      <SelectItem value="poor">Poor</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="sleep-notes">Notes (optional)</Label>
                  <textarea
                    id="sleep-notes"
                    className="w-full p-2 border rounded-md resize-none"
                    rows={3}
                    placeholder="Any notes about your sleep..."
                    value={newSleepLog.notes}
                    onChange={(e) => setNewSleepLog({ ...newSleepLog, notes: e.target.value })}
                  />
                </div>

                <div>
                  <Label htmlFor="sleep-date">Date</Label>
                  <Input
                    id="sleep-date"
                    type="date"
                    value={newSleepLog.date}
                    onChange={(e) => setNewSleepLog({ ...newSleepLog, date: e.target.value })}
                  />
                </div>

                <div className="flex space-x-3 pt-4">
                  <Button onClick={addSleepLog} className="flex-1 bg-gradient-to-r from-purple-500 to-indigo-500">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Sleep Log
                  </Button>
                  <Button variant="outline" onClick={() => setShowSleepForm(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};
export default HealthTracker;
