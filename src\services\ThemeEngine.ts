// Advanced Theme Engine for FOCOS
export interface ThemeConfig {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    success: string;
    warning: string;
    error: string;
    gradient: {
      start: string;
      end: string;
      direction: number; // degrees
    };
  };
  effects: {
    blur: number; // 0-20
    borderRadius: number; // 0-30
    glassmorphism: boolean;
    shadows: boolean;
    animations: boolean;
  };
  wallpaper: {
    type: 'none' | 'gradient' | 'pattern' | 'image';
    value: string;
    opacity: number; // 0-100
  };
  sounds: {
    enabled: boolean;
    volume: number; // 0-100
    taskComplete: string;
    notification: string;
    click: string;
  };
  haptics: {
    enabled: boolean;
    intensity: 'light' | 'medium' | 'heavy';
  };
}

export const defaultThemes: ThemeConfig[] = [
  {
    id: 'default',
    name: 'FOCOS Default',
    colors: {
      primary: '#8B5CF6',
      secondary: '#06B6D4',
      accent: '#F59E0B',
      background: '#FFFFFF',
      surface: '#F8FAFC',
      text: '#1F2937',
      textSecondary: '#6B7280',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444',
      gradient: {
        start: '#8B5CF6',
        end: '#06B6D4',
        direction: 135
      }
    },
    effects: {
      blur: 8,
      borderRadius: 16,
      glassmorphism: true,
      shadows: true,
      animations: true
    },
    wallpaper: {
      type: 'gradient',
      value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      opacity: 10
    },
    sounds: {
      enabled: true,
      volume: 50,
      taskComplete: 'success',
      notification: 'gentle',
      click: 'soft'
    },
    haptics: {
      enabled: true,
      intensity: 'medium'
    }
  },
  {
    id: 'dark-mode',
    name: 'Dark Professional',
    colors: {
      primary: '#A855F7',
      secondary: '#0EA5E9',
      accent: '#FBBF24',
      background: '#0F172A',
      surface: '#1E293B',
      text: '#F1F5F9',
      textSecondary: '#94A3B8',
      success: '#22C55E',
      warning: '#FBBF24',
      error: '#F87171',
      gradient: {
        start: '#1E293B',
        end: '#0F172A',
        direction: 180
      }
    },
    effects: {
      blur: 12,
      borderRadius: 20,
      glassmorphism: true,
      shadows: false,
      animations: true
    },
    wallpaper: {
      type: 'gradient',
      value: 'linear-gradient(180deg, #0F172A 0%, #1E293B 100%)',
      opacity: 80
    },
    sounds: {
      enabled: true,
      volume: 40,
      taskComplete: 'digital',
      notification: 'minimal',
      click: 'tick'
    },
    haptics: {
      enabled: true,
      intensity: 'light'
    }
  },
  {
    id: 'productivity-focus',
    name: 'Productivity Focus',
    colors: {
      primary: '#059669',
      secondary: '#0D9488',
      accent: '#DC2626',
      background: '#ECFDF5',
      surface: '#F0FDF4',
      text: '#064E3B',
      textSecondary: '#047857',
      success: '#059669',
      warning: '#D97706',
      error: '#DC2626',
      gradient: {
        start: '#059669',
        end: '#0D9488',
        direction: 90
      }
    },
    effects: {
      blur: 4,
      borderRadius: 8,
      glassmorphism: false,
      shadows: true,
      animations: false
    },
    wallpaper: {
      type: 'none',
      value: '',
      opacity: 0
    },
    sounds: {
      enabled: false,
      volume: 0,
      taskComplete: 'none',
      notification: 'none',
      click: 'none'
    },
    haptics: {
      enabled: false,
      intensity: 'light'
    }
  },
  {
    id: 'sunset-vibes',
    name: 'Sunset Vibes',
    colors: {
      primary: '#F97316',
      secondary: '#EC4899',
      accent: '#FBBF24',
      background: '#FFF7ED',
      surface: '#FFEDD5',
      text: '#9A3412',
      textSecondary: '#EA580C',
      success: '#65A30D',
      warning: '#F59E0B',
      error: '#DC2626',
      gradient: {
        start: '#F97316',
        end: '#EC4899',
        direction: 45
      }
    },
    effects: {
      blur: 16,
      borderRadius: 24,
      glassmorphism: true,
      shadows: true,
      animations: true
    },
    wallpaper: {
      type: 'gradient',
      value: 'linear-gradient(45deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)',
      opacity: 30
    },
    sounds: {
      enabled: true,
      volume: 60,
      taskComplete: 'chime',
      notification: 'warm',
      click: 'bubble'
    },
    haptics: {
      enabled: true,
      intensity: 'heavy'
    }
  }
];

class ThemeEngine {
  private static instance: ThemeEngine;
  private currentTheme: ThemeConfig;
  private customThemes: ThemeConfig[] = [];

  constructor() {
    this.loadThemes();
    this.currentTheme = this.getTheme(this.getSavedThemeId()) || defaultThemes[0];
    this.applyTheme(this.currentTheme);
  }

  static getInstance(): ThemeEngine {
    if (!ThemeEngine.instance) {
      ThemeEngine.instance = new ThemeEngine();
    }
    return ThemeEngine.instance;
  }

  private loadThemes(): void {
    const saved = localStorage.getItem('customThemes');
    if (saved) {
      try {
        this.customThemes = JSON.parse(saved);
      } catch (error) {
        console.error('Failed to load custom themes:', error);
        this.customThemes = [];
      }
    }
  }

  private saveThemes(): void {
    localStorage.setItem('customThemes', JSON.stringify(this.customThemes));
  }

  private getSavedThemeId(): string {
    return localStorage.getItem('currentThemeId') || 'default';
  }

  getAllThemes(): ThemeConfig[] {
    return [...defaultThemes, ...this.customThemes];
  }

  getTheme(id: string): ThemeConfig | undefined {
    return this.getAllThemes().find(theme => theme.id === id);
  }

  getCurrentTheme(): ThemeConfig {
    return this.currentTheme;
  }

  setTheme(themeId: string): void {
    const theme = this.getTheme(themeId);
    if (theme) {
      this.currentTheme = theme;
      this.applyTheme(theme);
      localStorage.setItem('currentThemeId', themeId);
    }
  }

  createCustomTheme(theme: Omit<ThemeConfig, 'id'>): string {
    const id = `custom-${Date.now()}`;
    const newTheme: ThemeConfig = { ...theme, id };
    this.customThemes.push(newTheme);
    this.saveThemes();
    return id;
  }

  updateCustomTheme(id: string, updates: Partial<ThemeConfig>): void {
    const index = this.customThemes.findIndex(theme => theme.id === id);
    if (index !== -1) {
      this.customThemes[index] = { ...this.customThemes[index], ...updates };
      this.saveThemes();
      
      if (this.currentTheme.id === id) {
        this.currentTheme = this.customThemes[index];
        this.applyTheme(this.currentTheme);
      }
    }
  }

  deleteCustomTheme(id: string): void {
    this.customThemes = this.customThemes.filter(theme => theme.id !== id);
    this.saveThemes();
    
    if (this.currentTheme.id === id) {
      this.setTheme('default');
    }
  }

  private applyTheme(theme: ThemeConfig): void {
    const root = document.documentElement;
    
    // Apply CSS custom properties
    root.style.setProperty('--color-primary', theme.colors.primary);
    root.style.setProperty('--color-secondary', theme.colors.secondary);
    root.style.setProperty('--color-accent', theme.colors.accent);
    root.style.setProperty('--color-background', theme.colors.background);
    root.style.setProperty('--color-surface', theme.colors.surface);
    root.style.setProperty('--color-text', theme.colors.text);
    root.style.setProperty('--color-text-secondary', theme.colors.textSecondary);
    root.style.setProperty('--color-success', theme.colors.success);
    root.style.setProperty('--color-warning', theme.colors.warning);
    root.style.setProperty('--color-error', theme.colors.error);
    
    // Apply gradient
    const gradient = `linear-gradient(${theme.colors.gradient.direction}deg, ${theme.colors.gradient.start}, ${theme.colors.gradient.end})`;
    root.style.setProperty('--gradient-primary', gradient);
    
    // Apply effects
    root.style.setProperty('--blur-amount', `${theme.effects.blur}px`);
    root.style.setProperty('--border-radius', `${theme.effects.borderRadius}px`);
    root.style.setProperty('--glassmorphism', theme.effects.glassmorphism ? '1' : '0');
    root.style.setProperty('--shadows', theme.effects.shadows ? '1' : '0');
    
    // Apply wallpaper
    this.applyWallpaper(theme.wallpaper);
    
    // Apply animations
    if (theme.effects.animations) {
      root.classList.add('animations-enabled');
    } else {
      root.classList.remove('animations-enabled');
    }
  }

  private applyWallpaper(wallpaper: ThemeConfig['wallpaper']): void {
    const body = document.body;
    
    if (wallpaper.type === 'none') {
      body.style.backgroundImage = '';
      body.style.backgroundColor = '';
    } else {
      body.style.backgroundImage = wallpaper.value;
      body.style.backgroundSize = 'cover';
      body.style.backgroundPosition = 'center';
      body.style.backgroundRepeat = 'no-repeat';
      body.style.backgroundAttachment = 'fixed';
      
      // Apply opacity overlay
      const overlay = document.querySelector('.wallpaper-overlay') as HTMLElement;
      if (overlay) {
        overlay.style.opacity = `${(100 - wallpaper.opacity) / 100}`;
      }
    }
  }

  // Enhanced Sound effects with Web Audio API
  playSound(type: keyof ThemeConfig['sounds']): void {
    if (!this.currentTheme.sounds.enabled || type === 'enabled' || type === 'volume') return;

    const soundName = this.currentTheme.sounds[type];
    if (soundName && soundName !== 'none') {
      this.playAudioFile(soundName, this.currentTheme.sounds.volume);
    }
  }

  private playAudioFile(soundName: string, volume: number): void {
    try {
      // Try to use generated sounds first, fallback to audio files
      if (this.canGenerateSound(soundName)) {
        this.generateSound(soundName, volume);
      } else {
        const audio = new Audio(`/sounds/${soundName}.mp3`);
        audio.volume = volume / 100;
        audio.play().catch(error => {
          console.warn('Could not play sound:', error);
          // Fallback to generated sound
          this.generateSound('click', volume);
        });
      }
    } catch (error) {
      console.warn('Sound not available:', soundName);
    }
  }

  private canGenerateSound(soundName: string): boolean {
    return ['click', 'success', 'error', 'notification', 'tick', 'bubble', 'chime'].includes(soundName);
  }

  private generateSound(type: string, volume: number): void {
    if (!('AudioContext' in window) && !('webkitAudioContext' in window)) return;

    const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
    const audioContext = new AudioContext();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    gainNode.gain.value = (volume / 100) * 0.1; // Keep volume low

    switch (type) {
      case 'click':
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
        break;
      case 'success':
        oscillator.frequency.setValueAtTime(523, audioContext.currentTime); // C5
        oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1); // E5
        oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.2); // G5
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
        break;
      case 'error':
        oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 0.2);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
        break;
      case 'notification':
        oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(554, audioContext.currentTime + 0.1);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
        break;
      case 'tick':
        oscillator.frequency.setValueAtTime(1000, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.05);
        break;
      case 'bubble':
        oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.1);
        oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.2);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
        break;
      case 'chime':
        oscillator.frequency.setValueAtTime(523, audioContext.currentTime); // C5
        oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.2); // E5
        oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.4); // G5
        oscillator.frequency.setValueAtTime(1047, audioContext.currentTime + 0.6); // C6
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.8);
        break;
      default:
        oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
    }

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 1);
  }

  // Haptic feedback
  triggerHaptic(type: 'light' | 'medium' | 'heavy' = 'medium'): void {
    if (!this.currentTheme.haptics.enabled) return;
    
    if ('vibrate' in navigator) {
      const patterns = {
        light: [50],
        medium: [100],
        heavy: [200]
      };
      
      const intensity = this.currentTheme.haptics.intensity;
      navigator.vibrate(patterns[intensity]);
    }
  }

  // Export/Import themes
  exportTheme(themeId: string): string {
    const theme = this.getTheme(themeId);
    if (!theme) throw new Error('Theme not found');
    
    return JSON.stringify(theme, null, 2);
  }

  importTheme(themeData: string): string {
    try {
      const theme = JSON.parse(themeData) as ThemeConfig;
      
      // Validate theme structure
      if (!theme.name || !theme.colors || !theme.effects) {
        throw new Error('Invalid theme format');
      }
      
      // Generate new ID to avoid conflicts
      const newId = this.createCustomTheme(theme);
      return newId;
    } catch (error) {
      throw new Error('Failed to import theme: ' + (error as Error).message);
    }
  }
}

// Wallpaper patterns
export const wallpaperPatterns = [
  {
    name: 'Geometric Waves',
    value: 'linear-gradient(45deg, #667eea 0%, #764ba2 100%)',
    type: 'gradient'
  },
  {
    name: 'Sunset Gradient',
    value: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)',
    type: 'gradient'
  },
  {
    name: 'Ocean Breeze',
    value: 'linear-gradient(120deg, #a8edea 0%, #fed6e3 100%)',
    type: 'gradient'
  },
  {
    name: 'Purple Rain',
    value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    type: 'gradient'
  },
  {
    name: 'Forest Mist',
    value: 'linear-gradient(135deg, #134e5e 0%, #71b280 100%)',
    type: 'gradient'
  },
  {
    name: 'Dots Pattern',
    value: 'radial-gradient(circle, #667eea 1px, transparent 1px)',
    type: 'pattern'
  },
  {
    name: 'Grid Pattern',
    value: 'linear-gradient(90deg, rgba(102,126,234,0.1) 1px, transparent 1px), linear-gradient(rgba(102,126,234,0.1) 1px, transparent 1px)',
    type: 'pattern'
  }
];

export default ThemeEngine.getInstance();
