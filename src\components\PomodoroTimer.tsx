import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Play, Pause, Square, RotateCcw, Settings, TrendingUp,
  Coffee, Brain, Clock, Target, Award, Calendar,
  BarChart3, Activity, Zap, CheckCircle
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { GlassmorphCard } from '@/components/ui/glassmorphcard';

interface PomodoroSession {
  id: string;
  type: 'work' | 'short-break' | 'long-break';
  duration: number;
  completedAt: string;
  interrupted: boolean;
  productivity: number; // 1-5 rating
  notes?: string;
  tags?: string[];
}

interface PomodoroSettings {
  workDuration: number;
  shortBreakDuration: number;
  longBreakDuration: number;
  longBreakInterval: number;
  autoStartBreaks: boolean;
  autoStartWork: boolean;
  soundEnabled: boolean;
  notificationsEnabled: boolean;
  dailyGoal: number;
}

interface BreakActivity {
  id: string;
  name: string;
  duration: number;
  category: 'physical' | 'mental' | 'social' | 'creative';
  icon: string;
}

const PomodoroTimer = () => {
  // Timer State
  const [timeLeft, setTimeLeft] = useState(25 * 60); // 25 minutes in seconds
  const [isRunning, setIsRunning] = useState(false);
  const [currentSession, setCurrentSession] = useState<'work' | 'short-break' | 'long-break'>('work');
  const [sessionsCompleted, setSessionsCompleted] = useState(0);
  const [currentCycle, setCurrentCycle] = useState(1);
  
  // Analytics State
  const [sessions, setSessions] = useState<PomodoroSession[]>([]);
  const [todayStats, setTodayStats] = useState({
    sessionsCompleted: 0,
    totalFocusTime: 0,
    averageProductivity: 0,
    streakDays: 0
  });
  
  // Settings State
  const [settings, setSettings] = useState<PomodoroSettings>({
    workDuration: 25,
    shortBreakDuration: 5,
    longBreakDuration: 15,
    longBreakInterval: 4,
    autoStartBreaks: false,
    autoStartWork: false,
    soundEnabled: true,
    notificationsEnabled: true,
    dailyGoal: 8
  });
  
  // UI State
  const [showSettings, setShowSettings] = useState(false);
  const [showProductivityRating, setShowProductivityRating] = useState(false);
  const [productivityRating, setProductivityRating] = useState(3);
  const [sessionNotes, setSessionNotes] = useState('');
  const [selectedBreakActivity, setSelectedBreakActivity] = useState<BreakActivity | null>(null);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Break Activities
  const breakActivities: BreakActivity[] = [
    { id: '1', name: 'Deep Breathing', duration: 3, category: 'mental', icon: '🧘' },
    { id: '2', name: 'Stretch', duration: 5, category: 'physical', icon: '🤸' },
    { id: '3', name: 'Walk Around', duration: 5, category: 'physical', icon: '🚶' },
    { id: '4', name: 'Hydrate', duration: 2, category: 'physical', icon: '💧' },
    { id: '5', name: 'Eye Exercises', duration: 3, category: 'physical', icon: '👁️' },
    { id: '6', name: 'Quick Chat', duration: 5, category: 'social', icon: '💬' },
    { id: '7', name: 'Mindful Moment', duration: 3, category: 'mental', icon: '🌸' },
    { id: '8', name: 'Quick Sketch', duration: 5, category: 'creative', icon: '🎨' }
  ];

  useEffect(() => {
    loadData();
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            handleSessionComplete();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, timeLeft]);

  const loadData = () => {
    const savedSessions = JSON.parse(localStorage.getItem('pomodoroSessions') || '[]');
    const savedSettings = JSON.parse(localStorage.getItem('pomodoroSettings') || '{}');
    
    setSessions(savedSessions);
    setSettings(prev => ({ ...prev, ...savedSettings }));
    calculateTodayStats(savedSessions);
  };

  const saveData = (newSessions: PomodoroSession[]) => {
    setSessions(newSessions);
    localStorage.setItem('pomodoroSessions', JSON.stringify(newSessions));
    calculateTodayStats(newSessions);
  };

  const calculateTodayStats = (sessionData: PomodoroSession[]) => {
    const today = new Date().toDateString();
    const todaySessions = sessionData.filter(s => 
      new Date(s.completedAt).toDateString() === today && s.type === 'work'
    );
    
    const totalFocusTime = todaySessions.reduce((sum, s) => sum + s.duration, 0);
    const averageProductivity = todaySessions.length > 0 
      ? todaySessions.reduce((sum, s) => sum + s.productivity, 0) / todaySessions.length 
      : 0;
    
    // Calculate streak
    let streakDays = 0;
    const dates = [...new Set(sessionData.map(s => new Date(s.completedAt).toDateString()))].sort();
    for (let i = dates.length - 1; i >= 0; i--) {
      const date = new Date(dates[i]);
      const expectedDate = new Date();
      expectedDate.setDate(expectedDate.getDate() - streakDays);
      
      if (date.toDateString() === expectedDate.toDateString()) {
        streakDays++;
      } else {
        break;
      }
    }
    
    setTodayStats({
      sessionsCompleted: todaySessions.length,
      totalFocusTime,
      averageProductivity,
      streakDays
    });
  };

  const handleSessionComplete = () => {
    setIsRunning(false);
    
    if (settings.soundEnabled) {
      // Play completion sound
      const audio = new Audio('/notification.mp3');
      audio.play().catch(() => {});
    }
    
    if (settings.notificationsEnabled && 'Notification' in window) {
      const message = currentSession === 'work' 
        ? '🎉 Work session complete! Time for a break.'
        : '✨ Break time over! Ready to focus?';
      
      new Notification('Pomodoro Timer', {
        body: message,
        icon: '/favicon.ico'
      });
    }
    
    if (currentSession === 'work') {
      setShowProductivityRating(true);
    } else {
      handleBreakComplete();
    }
  };

  const handleBreakComplete = () => {
    const nextSessionType = getNextSessionType();
    setCurrentSession(nextSessionType);
    setTimeLeft(getSessionDuration(nextSessionType) * 60);

    if (settings.autoStartWork && nextSessionType === 'work') {
      setIsRunning(true);
    }
  };

  const getNextSessionType = (): 'work' | 'short-break' | 'long-break' => {
    if (currentSession === 'work') {
      setSessionsCompleted(prev => prev + 1);
      return (sessionsCompleted + 1) % settings.longBreakInterval === 0 ? 'long-break' : 'short-break';
    }
    return 'work';
  };

  const getSessionDuration = (type: 'work' | 'short-break' | 'long-break') => {
    switch (type) {
      case 'work': return settings.workDuration;
      case 'short-break': return settings.shortBreakDuration;
      case 'long-break': return settings.longBreakDuration;
    }
  };

  const saveSession = () => {
    const session: PomodoroSession = {
      id: Date.now().toString(),
      type: currentSession,
      duration: getSessionDuration(currentSession),
      completedAt: new Date().toISOString(),
      interrupted: false,
      productivity: productivityRating,
      notes: sessionNotes
    };

    const newSessions = [...sessions, session];
    saveData(newSessions);

    setShowProductivityRating(false);
    setProductivityRating(3);
    setSessionNotes('');

    handleBreakComplete();
  };

  const startBreakActivity = (activity: BreakActivity) => {
    setSelectedBreakActivity(activity);
    // In a real app, you might start a mini-timer for the activity
    setTimeout(() => {
      setSelectedBreakActivity(null);
    }, activity.duration * 1000);
  };

  const getWeeklyStats = () => {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const weekSessions = sessions.filter(s =>
      new Date(s.completedAt) >= oneWeekAgo && s.type === 'work'
    );

    const dailyStats = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dayString = date.toDateString();

      const daySessions = weekSessions.filter(s =>
        new Date(s.completedAt).toDateString() === dayString
      );

      return {
        date: dayString,
        sessions: daySessions.length,
        focusTime: daySessions.reduce((sum, s) => sum + s.duration, 0)
      };
    }).reverse();

    return dailyStats;
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="min-h-screen p-4 pb-24 bg-gradient-to-br from-red-50 via-white to-orange-50">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <h1 className="text-3xl font-bold bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent mb-2">
          Pomodoro Timer 🍅
        </h1>
        <p className="text-gray-600">Focus deeply with the Pomodoro Technique</p>
      </motion.div>

      {/* Main Timer Display */}
      <GlassmorphCard className="p-8 text-center mb-6">
        <div className="text-6xl font-mono font-bold text-gray-800 mb-4">
          {formatTime(timeLeft)}
        </div>
        <div className="text-lg text-gray-600 mb-6 capitalize">
          {currentSession.replace('-', ' ')} Session
        </div>
        
        <div className="flex justify-center space-x-4 mb-6">
          <Button
            onClick={() => setIsRunning(!isRunning)}
            size="lg"
            className="bg-gradient-to-r from-red-500 to-orange-500 text-white"
          >
            {isRunning ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
          </Button>
          <Button
            onClick={() => {
              setIsRunning(false);
              setTimeLeft(settings.workDuration * 60);
            }}
            size="lg"
            variant="outline"
          >
            <RotateCcw className="w-6 h-6" />
          </Button>
          <Button
            onClick={() => setShowSettings(true)}
            size="lg"
            variant="outline"
          >
            <Settings className="w-6 h-6" />
          </Button>
        </div>

        <Progress 
          value={((settings.workDuration * 60 - timeLeft) / (settings.workDuration * 60)) * 100} 
          className="w-full h-3"
        />
      </GlassmorphCard>

      {/* Today's Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <Card className="text-center p-4">
          <Target className="w-8 h-8 text-red-500 mx-auto mb-2" />
          <div className="text-2xl font-bold">{todayStats.sessionsCompleted}</div>
          <div className="text-sm text-gray-600">Sessions</div>
        </Card>
        <Card className="text-center p-4">
          <Clock className="w-8 h-8 text-orange-500 mx-auto mb-2" />
          <div className="text-2xl font-bold">{Math.round(todayStats.totalFocusTime / 60)}</div>
          <div className="text-sm text-gray-600">Minutes</div>
        </Card>
        <Card className="text-center p-4">
          <Activity className="w-8 h-8 text-green-500 mx-auto mb-2" />
          <div className="text-2xl font-bold">{todayStats.averageProductivity.toFixed(1)}</div>
          <div className="text-sm text-gray-600">Avg Rating</div>
        </Card>
        <Card className="text-center p-4">
          <Zap className="w-8 h-8 text-blue-500 mx-auto mb-2" />
          <div className="text-2xl font-bold">{todayStats.streakDays}</div>
          <div className="text-sm text-gray-600">Day Streak</div>
        </Card>
      </div>

      {/* Break Activities (shown during breaks) */}
      {(currentSession === 'short-break' || currentSession === 'long-break') && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Coffee className="w-5 h-5 mr-2" />
              Suggested Break Activities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {breakActivities.map((activity) => (
                <Button
                  key={activity.id}
                  onClick={() => startBreakActivity(activity)}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  disabled={selectedBreakActivity?.id === activity.id}
                >
                  <span className="text-2xl">{activity.icon}</span>
                  <span className="text-sm font-medium">{activity.name}</span>
                  <span className="text-xs text-gray-500">{activity.duration}min</span>
                </Button>
              ))}
            </div>
            {selectedBreakActivity && (
              <div className="mt-4 p-4 bg-green-50 rounded-lg text-center">
                <div className="text-lg font-medium text-green-800">
                  {selectedBreakActivity.icon} Enjoying your {selectedBreakActivity.name}
                </div>
                <div className="text-sm text-green-600">
                  Take your time and recharge!
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Analytics Tab */}
      <Tabs defaultValue="today" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="today" className="text-xs md:text-sm">Today</TabsTrigger>
          <TabsTrigger value="week" className="text-xs md:text-sm">
            <span className="hidden sm:inline">This Week</span>
            <span className="sm:hidden">Week</span>
          </TabsTrigger>
          <TabsTrigger value="insights" className="text-xs md:text-sm">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="today">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Today's Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Daily Goal Progress</span>
                    <span>{todayStats.sessionsCompleted}/{settings.dailyGoal}</span>
                  </div>
                  <Progress
                    value={(todayStats.sessionsCompleted / settings.dailyGoal) * 100}
                    className="h-3"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4 text-center">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {Math.round(todayStats.totalFocusTime / 60)}
                    </div>
                    <div className="text-sm text-blue-600">Minutes Focused</div>
                  </div>
                  <div className="p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {todayStats.averageProductivity.toFixed(1)}/5
                    </div>
                    <div className="text-sm text-green-600">Avg Productivity</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="week">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Weekly Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {getWeeklyStats().map((day, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="text-sm font-medium">
                      {new Date(day.date).toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })}
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-sm text-gray-600">
                        {day.sessions} sessions
                      </div>
                      <div className="text-sm text-gray-600">
                        {Math.round(day.focusTime / 60)}min
                      </div>
                      <div className="w-20">
                        <Progress value={(day.sessions / settings.dailyGoal) * 100} className="h-2" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Productivity Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-yellow-50 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Award className="w-5 h-5 text-yellow-600 mr-2" />
                    <span className="font-medium text-yellow-800">Achievement</span>
                  </div>
                  <p className="text-sm text-yellow-700">
                    {todayStats.streakDays > 0
                      ? `🔥 You're on a ${todayStats.streakDays}-day streak! Keep it up!`
                      : "Start your productivity streak today!"
                    }
                  </p>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Brain className="w-5 h-5 text-blue-600 mr-2" />
                    <span className="font-medium text-blue-800">Focus Tip</span>
                  </div>
                  <p className="text-sm text-blue-700">
                    {todayStats.averageProductivity < 3
                      ? "Try eliminating distractions during your next session."
                      : "Your focus is strong! Consider tackling more challenging tasks."
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Productivity Rating Modal */}
      <AnimatePresence>
        {showProductivityRating && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-6 w-full max-w-md"
            >
              <h3 className="text-xl font-bold mb-4 text-center">
                How productive was this session?
              </h3>

              <div className="flex justify-center space-x-2 mb-6">
                {[1, 2, 3, 4, 5].map((rating) => (
                  <Button
                    key={rating}
                    onClick={() => setProductivityRating(rating)}
                    variant={productivityRating === rating ? "default" : "outline"}
                    className="w-12 h-12 rounded-full"
                  >
                    {rating}
                  </Button>
                ))}
              </div>

              <div className="mb-6">
                <Label htmlFor="session-notes">Session Notes (optional)</Label>
                <Input
                  id="session-notes"
                  value={sessionNotes}
                  onChange={(e) => setSessionNotes(e.target.value)}
                  placeholder="What did you work on?"
                  className="mt-2"
                />
              </div>

              <div className="flex space-x-3">
                <Button onClick={saveSession} className="flex-1">
                  Save & Continue
                </Button>
                <Button
                  onClick={() => setShowProductivityRating(false)}
                  variant="outline"
                  className="flex-1"
                >
                  Skip
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PomodoroTimer;
