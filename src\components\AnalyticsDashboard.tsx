import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Bar<PERSON>hart3, TrendingUp, Clock, Target, Award, Calendar,
  Activity, Zap, Brain, Heart, Star, ArrowUp, ArrowDown,
  PieChart, LineChart, Users, CheckCircle, AlertCircle
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { GlassmorphCard } from '@/components/ui/glassmorphcard';

interface AnalyticsData {
  productivity: {
    dailyFocus: number[];
    weeklyGoals: number;
    completionRate: number;
    streakDays: number;
  };
  wellness: {
    sleepQuality: number[];
    stressLevels: number[];
    energyLevels: number[];
    moodScores: number[];
  };
  habits: {
    completed: number;
    total: number;
    consistency: number;
    topHabits: string[];
  };
  goals: {
    completed: number;
    inProgress: number;
    overdue: number;
    successRate: number;
  };
}

interface Insight {
  id: string;
  type: 'success' | 'warning' | 'info' | 'improvement';
  title: string;
  description: string;
  action?: string;
  priority: 'high' | 'medium' | 'low';
}

const AnalyticsDashboard = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    productivity: {
      dailyFocus: [6.5, 7.2, 5.8, 8.1, 7.5, 6.9, 8.3],
      weeklyGoals: 12,
      completionRate: 78,
      streakDays: 15
    },
    wellness: {
      sleepQuality: [7.2, 6.8, 8.1, 7.5, 6.9, 8.0, 7.8],
      stressLevels: [3.2, 4.1, 2.8, 3.5, 4.0, 2.9, 3.1],
      energyLevels: [7.5, 6.8, 8.2, 7.9, 7.1, 8.0, 8.1],
      moodScores: [8.1, 7.5, 8.3, 7.8, 7.9, 8.2, 8.0]
    },
    habits: {
      completed: 23,
      total: 28,
      consistency: 82,
      topHabits: ['Morning Meditation', 'Exercise', 'Reading', 'Journaling']
    },
    goals: {
      completed: 8,
      inProgress: 5,
      overdue: 2,
      successRate: 73
    }
  });

  const [insights, setInsights] = useState<Insight[]>([
    {
      id: '1',
      type: 'success',
      title: 'Productivity Streak!',
      description: 'You\'ve maintained a 15-day focus streak. Keep it up!',
      priority: 'high'
    },
    {
      id: '2',
      type: 'warning',
      title: 'Sleep Pattern Alert',
      description: 'Your sleep quality has decreased by 12% this week.',
      action: 'Review sleep schedule',
      priority: 'medium'
    },
    {
      id: '3',
      type: 'improvement',
      title: 'Goal Completion Opportunity',
      description: 'You have 2 overdue goals that need attention.',
      action: 'Review goals',
      priority: 'high'
    },
    {
      id: '4',
      type: 'info',
      title: 'Habit Consistency',
      description: 'Your habit completion rate is 82% - excellent work!',
      priority: 'low'
    }
  ]);

  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'year'>('week');

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning': return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'improvement': return <TrendingUp className="w-5 h-5 text-blue-500" />;
      default: return <Activity className="w-5 h-5 text-gray-500" />;
    }
  };

  const StatCard = ({ title, value, change, icon: Icon, trend }: any) => (
    <GlassmorphCard className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">{value}</p>
          {change && (
            <div className={`flex items-center mt-1 ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
              {trend === 'up' ? <ArrowUp className="w-4 h-4" /> : <ArrowDown className="w-4 h-4" />}
              <span className="text-sm ml-1">{change}</span>
            </div>
          )}
        </div>
        <Icon className="w-8 h-8 text-blue-500" />
      </div>
    </GlassmorphCard>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-purple-900 dark:to-pink-900 p-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            Analytics Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Comprehensive insights into your productivity, wellness, and growth
          </p>
        </motion.div>

        {/* Time Frame Selector */}
        <div className="mb-6">
          <div className="flex space-x-2">
            {['week', 'month', 'year'].map((timeframe) => (
              <Button
                key={timeframe}
                variant={selectedTimeframe === timeframe ? 'default' : 'outline'}
                onClick={() => setSelectedTimeframe(timeframe as any)}
                className="capitalize"
              >
                {timeframe}
              </Button>
            ))}
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Focus Hours"
            value="52.3h"
            change="+12%"
            trend="up"
            icon={Clock}
          />
          <StatCard
            title="Goals Completed"
            value={analyticsData.goals.completed}
            change="+3"
            trend="up"
            icon={Target}
          />
          <StatCard
            title="Habit Consistency"
            value={`${analyticsData.habits.consistency}%`}
            change="+5%"
            trend="up"
            icon={Activity}
          />
          <StatCard
            title="Wellness Score"
            value="8.1/10"
            change="+0.3"
            trend="up"
            icon={Heart}
          />
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="productivity">Productivity</TabsTrigger>
            <TabsTrigger value="wellness">Wellness</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Weekly Progress */}
              <GlassmorphCard className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center">
                    <BarChart3 className="w-5 h-5 mr-2" />
                    Weekly Progress
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Goals Completion</span>
                        <span>{analyticsData.productivity.completionRate}%</span>
                      </div>
                      <Progress value={analyticsData.productivity.completionRate} />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Habit Consistency</span>
                        <span>{analyticsData.habits.consistency}%</span>
                      </div>
                      <Progress value={analyticsData.habits.consistency} />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Focus Time</span>
                        <span>85%</span>
                      </div>
                      <Progress value={85} />
                    </div>
                  </div>
                </CardContent>
              </GlassmorphCard>

              {/* Top Habits */}
              <GlassmorphCard className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center">
                    <Star className="w-5 h-5 mr-2" />
                    Top Performing Habits
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.habits.topHabits.map((habit, index) => (
                      <div key={habit} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className={`w-2 h-2 rounded-full mr-3 ${
                            index === 0 ? 'bg-green-500' : 
                            index === 1 ? 'bg-blue-500' : 
                            index === 2 ? 'bg-purple-500' : 'bg-gray-400'
                          }`} />
                          <span className="text-sm">{habit}</span>
                        </div>
                        <span className="text-sm text-gray-500">
                          {95 - index * 5}%
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </GlassmorphCard>
            </div>
          </TabsContent>

          <TabsContent value="productivity" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <GlassmorphCard className="p-6">
                <CardHeader>
                  <CardTitle>Daily Focus Hours</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-end justify-between space-x-2">
                    {analyticsData.productivity.dailyFocus.map((hours, index) => (
                      <div key={index} className="flex flex-col items-center">
                        <div 
                          className="w-8 bg-blue-500 rounded-t"
                          style={{ height: `${(hours / 10) * 200}px` }}
                        />
                        <span className="text-xs mt-2">
                          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][index]}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </GlassmorphCard>

              <GlassmorphCard className="p-6">
                <CardHeader>
                  <CardTitle>Goal Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-green-600">Completed</span>
                      <span className="font-semibold">{analyticsData.goals.completed}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-blue-600">In Progress</span>
                      <span className="font-semibold">{analyticsData.goals.inProgress}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-red-600">Overdue</span>
                      <span className="font-semibold">{analyticsData.goals.overdue}</span>
                    </div>
                    <div className="pt-4 border-t">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">Success Rate</span>
                        <span className="font-bold text-lg">{analyticsData.goals.successRate}%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </GlassmorphCard>
            </div>
          </TabsContent>

          <TabsContent value="wellness" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <GlassmorphCard className="p-6">
                <CardHeader>
                  <CardTitle>Sleep Quality Trend</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-end justify-between space-x-2">
                    {analyticsData.wellness.sleepQuality.map((quality, index) => (
                      <div key={index} className="flex flex-col items-center">
                        <div 
                          className="w-8 bg-purple-500 rounded-t"
                          style={{ height: `${(quality / 10) * 200}px` }}
                        />
                        <span className="text-xs mt-2">
                          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][index]}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </GlassmorphCard>

              <GlassmorphCard className="p-6">
                <CardHeader>
                  <CardTitle>Wellness Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Average Sleep Quality</span>
                        <span>7.6/10</span>
                      </div>
                      <Progress value={76} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Energy Levels</span>
                        <span>7.8/10</span>
                      </div>
                      <Progress value={78} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Mood Score</span>
                        <span>8.0/10</span>
                      </div>
                      <Progress value={80} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Stress Levels</span>
                        <span>3.4/10</span>
                      </div>
                      <Progress value={34} className="h-2" />
                    </div>
                  </div>
                </CardContent>
              </GlassmorphCard>
            </div>
          </TabsContent>

          <TabsContent value="insights" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {insights.map((insight) => (
                <motion.div
                  key={insight.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <GlassmorphCard className="p-6">
                    <div className="flex items-start space-x-3">
                      {getInsightIcon(insight.type)}
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {insight.title}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {insight.description}
                        </p>
                        {insight.action && (
                          <Button size="sm" className="mt-3">
                            {insight.action}
                          </Button>
                        )}
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        insight.priority === 'high' ? 'bg-red-100 text-red-800' :
                        insight.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {insight.priority}
                      </span>
                    </div>
                  </GlassmorphCard>
                </motion.div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
