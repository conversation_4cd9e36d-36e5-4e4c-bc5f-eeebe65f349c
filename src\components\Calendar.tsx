
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Plus, Calendar as CalendarIcon, Clock, MapPin, Users, Edit2, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  location?: string;
  attendees?: string[];
  color: string;
  category: string;
}

const Calendar = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newEvent, setNewEvent] = useState({
    title: '',
    description: '',
    time: '',
    location: '',
    category: 'personal',
    color: 'blue'
  });

  useEffect(() => {
    const savedEvents = localStorage.getItem('calendarEvents');
    if (savedEvents) {
      setEvents(JSON.parse(savedEvents));
    }
  }, []);

  const saveEvents = (updatedEvents: Event[]) => {
    try {
      setEvents(updatedEvents);
      localStorage.setItem('calendarEvents', JSON.stringify(updatedEvents));
      console.log('Events saved successfully to localStorage');
    } catch (error) {
      console.error('Failed to save events to localStorage:', error);
      alert('Failed to save event. Please try again.');
    }
  };

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const addEvent = () => {
    if (!newEvent.title.trim()) {
      alert('Please enter an event title');
      return;
    }

    if (!selectedDate) {
      alert('Please select a date first by clicking on a calendar day');
      return;
    }

    const event: Event = {
      id: Date.now().toString(),
      title: newEvent.title,
      description: newEvent.description,
      date: selectedDate.toISOString().split('T')[0],
      time: newEvent.time,
      location: newEvent.location,
      color: newEvent.color,
      category: newEvent.category,
      attendees: []
    };

    console.log('Adding event:', event); // Debug log
    const updatedEvents = [...events, event];
    saveEvents(updatedEvents);
    console.log('Events after save:', updatedEvents); // Debug log

    // Show success message
    alert(`Event "${event.title}" created successfully!`);

    setNewEvent({
      title: '',
      description: '',
      time: '',
      location: '',
      category: 'personal',
      color: 'blue'
    });
    setShowAddForm(false);
    setSelectedDate(selectedDate); // Ensure the date stays selected to show the new event
  };

  const deleteEvent = (id: string) => {
    const updatedEvents = events.filter(event => event.id !== id);
    saveEvents(updatedEvents);
  };

  const getEventsForDate = (date: Date) => {
    const dateString = date.toISOString().split('T')[0];
    return events.filter(event => event.date === dateString);
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const colors = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    purple: 'bg-purple-500',
    pink: 'bg-pink-500',
    orange: 'bg-orange-500',
    red: 'bg-red-500'
  };

  const daysInMonth = getDaysInMonth(currentDate);
  const firstDay = getFirstDayOfMonth(currentDate);
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const containerVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 }
  };

  return (
    <div className="min-h-screen p-4 pb-24 bg-gradient-to-br from-rose-50 via-white to-orange-50">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex justify-between items-center mb-6"
      >
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-rose-600 to-orange-600 bg-clip-text text-transparent">
            Calendar 📅
          </h1>
          <p className="text-gray-600">Organize your schedule and events</p>
        </div>
        <Button
          onClick={() => {
            setShowAddForm(true);
            if (!selectedDate) {
              // Auto-select today's date if no date is selected
              setSelectedDate(new Date());
            }
          }}
          className="bg-gradient-to-r from-rose-600 to-orange-600 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <Plus className="w-4 h-4 mr-2" />
          New Event
        </Button>
      </motion.div>

      {/* Calendar Header */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="mb-6"
      >
        <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
          <CardHeader>
            <div className="flex justify-between items-center">
              <Button
                onClick={() => navigateMonth('prev')}
                variant="outline"
                size="sm"
                className="rounded-xl"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <h2 className="text-2xl font-bold text-gray-800">
                {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
              </h2>
              <Button
                onClick={() => navigateMonth('next')}
                variant="outline"
                size="sm"
                className="rounded-xl"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Days of Week */}
            <div className="grid grid-cols-7 gap-2 mb-4">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                <div key={day} className="text-center text-sm font-semibold text-gray-600 py-2">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-2">
              {/* Empty cells for days before month starts */}
              {Array.from({ length: firstDay }, (_, i) => (
                <div key={`empty-${i}`} className="h-12"></div>
              ))}

              {/* Days of the month */}
              {Array.from({ length: daysInMonth }, (_, i) => {
                const day = i + 1;
                const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
                const dayEvents = getEventsForDate(date);
                const isSelected = selectedDate?.toDateString() === date.toDateString();
                const isToday = new Date().toDateString() === date.toDateString();

                return (
                  <motion.button
                    key={day}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setSelectedDate(date)}
                    className={`h-12 rounded-xl relative transition-all duration-200 ${
                      isSelected
                        ? 'bg-gradient-to-r from-rose-500 to-orange-500 text-white shadow-lg'
                        : isToday
                        ? 'bg-blue-100 text-blue-800 border-2 border-blue-500'
                        : 'hover:bg-gray-100'
                    }`}
                  >
                    <span className="font-medium">{day}</span>
                    {dayEvents.length > 0 && (
                      <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 flex space-x-1">
                        {dayEvents.slice(0, 3).map((event, idx) => (
                          <div
                            key={idx}
                            className={`w-1.5 h-1.5 rounded-full ${colors[event.color as keyof typeof colors]}`}
                          />
                        ))}
                      </div>
                    )}
                  </motion.button>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Add Event Form */}
      <AnimatePresence>
        {showAddForm && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="mb-6"
          >
            <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-gray-800">Create New Event</CardTitle>
                {selectedDate && (
                  <p className="text-sm text-gray-600">
                    Creating event for: <span className="font-semibold text-rose-600">{selectedDate.toLocaleDateString()}</span>
                  </p>
                )}
                {!selectedDate && (
                  <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded-lg">
                    ⚠️ Please select a date from the calendar first
                  </p>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  placeholder="Event title"
                  value={newEvent.title}
                  onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}
                  className="rounded-xl border-2 border-gray-200 focus:border-rose-500"
                />
                <Textarea
                  placeholder="Event description"
                  value={newEvent.description}
                  onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}
                  className="rounded-xl border-2 border-gray-200 focus:border-rose-500"
                />
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Input
                    type="time"
                    value={newEvent.time}
                    onChange={(e) => setNewEvent({ ...newEvent, time: e.target.value })}
                    className="rounded-xl border-2 border-gray-200 focus:border-rose-500"
                  />
                  <Input
                    placeholder="Location (optional)"
                    value={newEvent.location}
                    onChange={(e) => setNewEvent({ ...newEvent, location: e.target.value })}
                    className="rounded-xl border-2 border-gray-200 focus:border-rose-500"
                  />
                  <select
                    value={newEvent.category}
                    onChange={(e) => setNewEvent({ ...newEvent, category: e.target.value })}
                    className="rounded-xl border-2 border-gray-200 focus:border-rose-500 px-3 py-2"
                  >
                    <option value="personal">Personal</option>
                    <option value="work">Work</option>
                    <option value="health">Health</option>
                    <option value="social">Social</option>
                  </select>
                </div>
                <div className="flex space-x-2">
                  <span className="text-sm font-medium text-gray-700 py-2">Color:</span>
                  {Object.entries(colors).map(([colorName, colorClass]) => (
                    <button
                      key={colorName}
                      onClick={() => setNewEvent({ ...newEvent, color: colorName })}
                      className={`w-8 h-8 rounded-full ${colorClass} ${
                        newEvent.color === colorName ? 'ring-4 ring-gray-300' : ''
                      } transition-all duration-200`}
                    />
                  ))}
                </div>
                <div className="flex space-x-3">
                  <Button
                    onClick={addEvent}
                    disabled={!newEvent.title.trim() || !selectedDate}
                    className="bg-gradient-to-r from-rose-600 to-orange-600 text-white rounded-xl px-6"
                  >
                    Create Event
                  </Button>
                  <Button
                    onClick={() => setShowAddForm(false)}
                    variant="outline"
                    className="rounded-xl"
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Selected Date Events */}
      {selectedDate && (
        <motion.div
          variants={containerVariants}
          initial="initial"
          animate="animate"
          className="space-y-4"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            Events for {selectedDate.toLocaleDateString()}
          </h2>
          
          {getEventsForDate(selectedDate).map((event, index) => (
            <motion.div
              key={event.id}
              variants={itemVariants}
              whileHover={{ scale: 1.02 }}
              className="group"
            >
              <Card className="border-0 shadow-lg bg-white/90 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className={`w-4 h-4 rounded-full ${colors[event.color as keyof typeof colors]}`}></div>
                        <h3 className="font-bold text-lg text-gray-800">{event.title}</h3>
                        <span className="px-3 py-1 bg-rose-100 text-rose-800 rounded-full text-sm font-medium">
                          {event.category}
                        </span>
                      </div>
                      <p className="text-gray-600 mb-3">{event.description}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        {event.time && (
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            {event.time}
                          </div>
                        )}
                        {event.location && (
                          <div className="flex items-center">
                            <MapPin className="w-4 h-4 mr-1" />
                            {event.location}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => deleteEvent(event.id)}
                        className="rounded-xl text-red-600 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}

          {getEventsForDate(selectedDate).length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-8"
            >
              <CalendarIcon className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">No events for this date</p>
              <Button
                onClick={() => setShowAddForm(true)}
                className="mt-4 bg-gradient-to-r from-rose-600 to-orange-600 text-white rounded-xl"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Event
              </Button>
            </motion.div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default Calendar;
