
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Search, Star, Calendar, Pencil, FileText, ChevronRight, ChevronDown, Folder, BookOpen, Edit3, Trash2, Edit, Save, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';

interface Note {
  id: number;
  title: string;
  content: string;
  tags: string[];
  pinned: boolean;
  updatedAt: string;
  createdAt: string;
  parentId?: number;
  isFolder?: boolean;
  expanded?: boolean;
  level?: number;
}

const Notes = () => {
  const [notes, setNotes] = useState<Note[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showEditor, setShowEditor] = useState(false);
  const [selectedFolder, setSelectedFolder] = useState<number | null>(null);
  const [editingFolderId, setEditingFolderId] = useState<number | null>(null);
  const [editingFolderName, setEditingFolderName] = useState('');
  const [currentNote, setCurrentNote] = useState<Partial<Note>>({
    id: undefined,
    title: '',
    content: '',
    tags: [],
    pinned: false,
    parentId: undefined,
    isFolder: false
  });

  useEffect(() => {
    loadNotes();
  }, []);

  const loadNotes = () => {
    const savedNotes = JSON.parse(localStorage.getItem('notes') || '[]') as Note[];
    setNotes(savedNotes);
  };

  const saveNote = () => {
    const newNote: Note = {
      id: currentNote.id || Date.now(),
      title: currentNote.title || '',
      content: currentNote.content || '',
      tags: currentNote.tags || [],
      pinned: currentNote.pinned || false,
      updatedAt: new Date().toISOString(),
      createdAt: currentNote.createdAt || new Date().toISOString(),
      parentId: currentNote.parentId || selectedFolder || undefined,
      isFolder: currentNote.isFolder || false,
      expanded: currentNote.expanded || false
    };

    const updatedNotes = currentNote.id 
      ? notes.map(note => note.id === currentNote.id ? newNote : note)
      : [...notes, newNote];

    setNotes(updatedNotes);
    localStorage.setItem('notes', JSON.stringify(updatedNotes));
    setShowEditor(false);
    setCurrentNote({ id: undefined, title: '', content: '', tags: [], pinned: false, parentId: undefined, isFolder: false });
  };

  const createFolder = () => {
    const folder: Note = {
      id: Date.now(),
      title: 'New Folder',
      content: '',
      tags: [],
      pinned: false,
      updatedAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      isFolder: true,
      expanded: true
    };

    const updatedNotes = [...notes, folder];
    setNotes(updatedNotes);
    localStorage.setItem('notes', JSON.stringify(updatedNotes));
  };

  const editNote = (note: Note) => {
    setCurrentNote(note);
    setShowEditor(true);
  };

  const togglePin = (noteId: number) => {
    const updatedNotes = notes.map(note => 
      note.id === noteId ? { ...note, pinned: !note.pinned } : note
    );
    setNotes(updatedNotes);
    localStorage.setItem('notes', JSON.stringify(updatedNotes));
  };

  const toggleFolder = (folderId: number) => {
    const updatedNotes = notes.map(note =>
      note.id === folderId ? { ...note, expanded: !note.expanded } : note
    );
    setNotes(updatedNotes);
    localStorage.setItem('notes', JSON.stringify(updatedNotes));
  };

  const startEditingFolder = (folder: Note) => {
    setEditingFolderId(folder.id);
    setEditingFolderName(folder.title);
  };

  const saveFolder = () => {
    if (!editingFolderName.trim()) return;

    const updatedNotes = notes.map(note =>
      note.id === editingFolderId ? { ...note, title: editingFolderName.trim() } : note
    );
    setNotes(updatedNotes);
    localStorage.setItem('notes', JSON.stringify(updatedNotes));
    setEditingFolderId(null);
    setEditingFolderName('');
  };

  const cancelEditingFolder = () => {
    setEditingFolderId(null);
    setEditingFolderName('');
  };

  const deleteNote = (noteId: number) => {
    if (confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
      // Delete the note and all its children
      const noteToDelete = notes.find(n => n.id === noteId);
      if (noteToDelete?.isFolder) {
        // Delete all notes in this folder
        const updatedNotes = notes.filter(note =>
          note.id !== noteId && note.parentId !== noteId
        );
        setNotes(updatedNotes);
        localStorage.setItem('notes', JSON.stringify(updatedNotes));
      } else {
        // Delete just the note
        const updatedNotes = notes.filter(note => note.id !== noteId);
        setNotes(updatedNotes);
        localStorage.setItem('notes', JSON.stringify(updatedNotes));
      }
    }
  };

  const getOrganizedNotes = () => {
    const rootNotes = notes.filter(note => !note.parentId);
    const organizedNotes: Note[] = [];

    const addNoteWithChildren = (note: Note, level: number = 0) => {
      const noteWithLevel = { ...note, level };
      organizedNotes.push(noteWithLevel);

      if (note.isFolder && note.expanded) {
        const children = notes.filter(child => child.parentId === note.id);
        children.forEach(child => addNoteWithChildren(child, level + 1));
      }
    };

    rootNotes
      .filter(note => 
        note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.content.toLowerCase().includes(searchQuery.toLowerCase())
      )
      .sort((a, b) => {
        if (a.pinned && !b.pinned) return -1;
        if (!a.pinned && b.pinned) return 1;
        if (a.isFolder && !b.isFolder) return -1;
        if (!a.isFolder && b.isFolder) return 1;
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
      })
      .forEach(note => addNoteWithChildren(note));

    return organizedNotes;
  };

  const organizedNotes = getOrganizedNotes();

  const [isDarkMode, setIsDarkMode] = useState(() => {
    const saved = localStorage.getItem('billionDollarDarkMode');
    return saved ? JSON.parse(saved) : false;
  });

  if (showEditor) {
    return (
      <div className={`min-h-screen transition-all duration-700 ${
        isDarkMode
          ? 'bg-gradient-to-br from-slate-900 via-indigo-900/30 to-slate-900'
          : 'bg-gradient-to-br from-indigo-50 via-white to-purple-50'
      }`}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="relative z-10 p-4 pb-24"
        >
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">
            {currentNote.id ? 'Edit Note' : 'New Note'}
          </h1>
          <div className="space-x-2">
            <Button variant="outline" onClick={() => setShowEditor(false)}>
              Cancel
            </Button>
            <Button onClick={saveNote} className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
              Save
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <Input
              placeholder="Note title..."
              value={currentNote.title || ''}
              onChange={(e) => setCurrentNote({ ...currentNote, title: e.target.value })}
              className="flex-1 text-lg font-medium py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500"
            />
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isFolder"
                checked={currentNote.isFolder || false}
                onChange={(e) => setCurrentNote({ ...currentNote, isFolder: e.target.checked })}
                className="rounded"
              />
              <label htmlFor="isFolder" className="text-sm font-medium">Folder</label>
            </div>
          </div>
          
          {!currentNote.isFolder && (
            <Textarea
              placeholder="Start writing your note..."
              value={currentNote.content || ''}
              onChange={(e) => setCurrentNote({ ...currentNote, content: e.target.value })}
              className="min-h-96 rounded-xl border-2 border-gray-200 focus:border-purple-500 resize-none"
            />
          )}

          {/* Folder Selection */}
          <div>
            <label className="block text-sm font-medium mb-2">Save in folder (optional)</label>
            <select
              value={selectedFolder || ''}
              onChange={(e) => setSelectedFolder(e.target.value ? parseInt(e.target.value) : null)}
              className="w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:border-purple-500"
            >
              <option value="">Root (No folder)</option>
              {notes.filter(n => n.isFolder).map(folder => (
                <option key={folder.id} value={folder.id}>
                  📁 {folder.title}
                </option>
              ))}
            </select>
          </div>

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium mb-2">Tags (comma separated)</label>
            <Input
              placeholder="work, personal, ideas..."
              value={currentNote.tags?.join(', ') || ''}
              onChange={(e) => setCurrentNote({
                ...currentNote,
                tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
              })}
              className="rounded-xl border-2 border-gray-200 focus:border-purple-500"
            />
          </div>
        </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-all duration-700 ${
      isDarkMode
        ? 'bg-gradient-to-br from-slate-900 via-indigo-900/30 to-slate-900'
        : 'bg-gradient-to-br from-indigo-50 via-white to-purple-50'
    }`}>
      {/* Floating particles */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        {[...Array(10)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-indigo-400 to-purple-500 rounded-full opacity-20"
            animate={{
              y: [-20, -100],
              x: [0, Math.random() * 100 - 50],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: '100%',
            }}
          />
        ))}
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative z-10 p-4 pb-24"
      >
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 mb-6"
        >
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
              📝 Notes & Journal
            </h1>
            <p className={`text-sm sm:text-base ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Organize your thoughts and ideas
            </p>
          </div>

          {/* Action Buttons - Positioned Higher */}
          <div className="flex flex-col sm:flex-row gap-2 flex-shrink-0 sm:-mt-1">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={createFolder}
                variant="outline"
                size="sm"
                className={`w-full sm:w-auto rounded-2xl backdrop-blur-xl border-2 ${
                  isDarkMode
                    ? 'bg-gray-800/50 border-gray-600 text-gray-300 hover:bg-gray-700/50'
                    : 'bg-white/50 border-gray-200 hover:bg-white/80'
                }`}
              >
                <Folder className="w-4 h-4 mr-2" />
                New Folder
              </Button>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={() => setShowEditor(true)}
                size="sm"
                className="w-full sm:w-auto bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 rounded-2xl shadow-lg"
              >
                <Plus className="w-4 h-4 mr-2" />
                New Note
              </Button>
            </motion.div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="relative z-10 p-4 pb-24"
        >
        {/* Search */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="relative mb-6"
        >
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <Input
          placeholder="Search notes..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 rounded-xl border-2 border-gray-200 focus:border-purple-500"
        />
        </motion.div>

        {/* Notes List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-3"
        >
        {organizedNotes.map((note) => (
          <motion.div
            key={note.id}
            layout
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="group"
            style={{ marginLeft: `${(note.level || 0) * 20}px` }}
          >
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex items-center space-x-2 flex-1">
                    {note.isFolder && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleFolder(note.id);
                        }}
                        className="p-0 h-6 w-6"
                      >
                        {note.expanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                      </Button>
                    )}
                    {note.isFolder ? (
                      <Folder className="w-5 h-5 text-purple-600" />
                    ) : (
                      <FileText className="w-5 h-5 text-gray-600" />
                    )}
                    {editingFolderId === note.id ? (
                      <div className="flex items-center space-x-2 flex-1">
                        <Input
                          value={editingFolderName}
                          onChange={(e) => setEditingFolderName(e.target.value)}
                          className="text-lg font-medium"
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') saveFolder();
                            if (e.key === 'Escape') cancelEditingFolder();
                          }}
                          autoFocus
                        />
                        <Button
                          size="sm"
                          onClick={saveFolder}
                          className="bg-green-500 hover:bg-green-600 text-white"
                        >
                          <Save className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={cancelEditingFolder}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    ) : (
                      <CardTitle className="text-lg line-clamp-2 flex-1">
                        {note.title || 'Untitled Note'}
                      </CardTitle>
                    )}
                  </div>
                  {editingFolderId !== note.id && (
                    <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      {!note.isFolder && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            togglePin(note.id);
                          }}
                          className={`p-1 h-8 w-8 ${note.pinned ? 'text-yellow-500' : 'text-gray-400'}`}
                        >
                          <Star className="w-4 h-4" fill={note.pinned ? 'currentColor' : 'none'} />
                        </Button>
                      )}

                      {note.isFolder ? (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            startEditingFolder(note);
                          }}
                          className="p-1 h-8 w-8 text-gray-400 hover:text-blue-600"
                          title="Rename folder"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                      ) : (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            editNote(note);
                          }}
                          className="p-1 h-8 w-8 text-gray-400 hover:text-purple-600"
                          title="Edit note"
                        >
                          <Pencil className="w-4 h-4" />
                        </Button>
                      )}

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteNote(note.id);
                        }}
                        className="p-1 h-8 w-8 text-gray-400 hover:text-red-600"
                        title="Delete"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              {!note.isFolder && (
                <CardContent 
                  className="pt-0"
                  onClick={() => editNote(note)}
                >
                  <p className="text-gray-600 text-sm line-clamp-3 mb-3">
                    {note.content || 'No content yet...'}
                  </p>
                  <div className="flex items-center text-xs text-gray-400">
                    <Calendar className="w-3 h-3 mr-1" />
                    {new Date(note.updatedAt).toLocaleDateString()}
                  </div>
                </CardContent>
              )}
            </Card>
          </motion.div>
        ))}
        </motion.div>

      {organizedNotes.length === 0 && (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-500 mb-2">No notes found</h3>
          <p className="text-gray-400 mb-4">
            {searchQuery ? 'Try a different search term' : 'Start organizing your thoughts!'}
          </p>
          {!searchQuery && (
            <div className="space-x-2">
              <Button 
                onClick={createFolder}
                variant="outline"
                className="mb-2"
              >
                <Folder className="w-4 h-4 mr-2" />
                Create Folder
              </Button>
              <Button 
                onClick={() => setShowEditor(true)}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Note
              </Button>
            </div>
          )}
        </div>
      )}
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Notes;
