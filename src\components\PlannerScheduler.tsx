
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calendar, ChevronLeft, ChevronRight, Plus, Clock,
  Play, Pause, Square, AlertTriangle, Zap, Settings,
  RotateCcw, Target, Palette, Mail, Download, Repeat,
  MapPin, Users, Brain, Grid, List, Tag, Sync
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface TimeBlock {
  id: string;
  title: string;
  description?: string;
  category: string;
  color: string;
  startTime: string;
  endTime: string;
  date: string;
  focusLabel: string;
  isRecurring: boolean;
  recurringPattern?: 'daily' | 'weekly' | 'monthly';
  pomodoroSessions: number;
  completedPomodoros: number;
  isRunning: boolean;
  currentSessionTime: number;
  location?: string;
  attendees?: string[];
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
  linkedTasks?: string[];
  autoFilled?: boolean;
  calendarSynced?: boolean;
  createdAt: string;
}

const PlannerScheduler = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [timeBlocks, setTimeBlocks] = useState<TimeBlock[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [draggedBlock, setDraggedBlock] = useState<TimeBlock | null>(null);
  const [conflicts, setConflicts] = useState<string[]>([]);
  const [activePomodoro, setActivePomodoro] = useState<string | null>(null);
  const [pomodoroTime, setPomodoroTime] = useState(25 * 60); // 25 minutes in seconds

  const [newBlock, setNewBlock] = useState({
    title: '',
    description: '',
    category: 'work',
    color: 'blue',
    startTime: '09:00',
    endTime: '10:00',
    focusLabel: '',
    isRecurring: false,
    recurringPattern: 'daily' as 'daily' | 'weekly' | 'monthly',
    pomodoroSessions: 1,
    location: '',
    attendees: [] as string[],
    priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
    linkedTasks: [] as string[]
  });

  const categories = {
    work: { color: 'bg-blue-500', label: 'Work' },
    personal: { color: 'bg-green-500', label: 'Personal' },
    health: { color: 'bg-red-500', label: 'Health' },
    learning: { color: 'bg-purple-500', label: 'Learning' },
    social: { color: 'bg-pink-500', label: 'Social' },
    break: { color: 'bg-orange-500', label: 'Break' }
  };

  useEffect(() => {
    const saved = localStorage.getItem('plannerBlocks');
    if (saved) {
      setTimeBlocks(JSON.parse(saved));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('plannerBlocks', JSON.stringify(timeBlocks));
    detectConflicts();
  }, [timeBlocks]);

  // Pomodoro timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (activePomodoro && pomodoroTime > 0) {
      interval = setInterval(() => {
        setPomodoroTime(prev => {
          if (prev <= 1) {
            completePomodoro();
            return 25 * 60; // Reset to 25 minutes
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [activePomodoro, pomodoroTime]);

  const detectConflicts = () => {
    const conflictIds: string[] = [];
    const sortedBlocks = [...timeBlocks].sort((a, b) => 
      new Date(`${a.date} ${a.startTime}`).getTime() - new Date(`${b.date} ${b.startTime}`).getTime()
    );

    for (let i = 0; i < sortedBlocks.length - 1; i++) {
      const current = sortedBlocks[i];
      const next = sortedBlocks[i + 1];
      
      if (current.date === next.date) {
        const currentEnd = new Date(`${current.date} ${current.endTime}`);
        const nextStart = new Date(`${next.date} ${next.startTime}`);
        
        if (currentEnd > nextStart) {
          conflictIds.push(current.id, next.id);
        }
      }
    }
    
    setConflicts(conflictIds);
  };

  const addTimeBlock = () => {
    if (!newBlock.title.trim()) return;

    const block: TimeBlock = {
      id: Date.now().toString(),
      title: newBlock.title,
      description: newBlock.description,
      category: newBlock.category,
      color: newBlock.color,
      startTime: newBlock.startTime,
      endTime: newBlock.endTime,
      date: currentDate.toISOString().split('T')[0],
      focusLabel: newBlock.focusLabel,
      isRecurring: newBlock.isRecurring,
      recurringPattern: newBlock.recurringPattern,
      pomodoroSessions: newBlock.pomodoroSessions,
      completedPomodoros: 0,
      isRunning: false,
      currentSessionTime: 0,
      location: newBlock.location,
      attendees: newBlock.attendees,
      priority: newBlock.priority,
      status: 'scheduled',
      linkedTasks: newBlock.linkedTasks,
      autoFilled: false,
      calendarSynced: false,
      createdAt: new Date().toISOString()
    };

    const updatedBlocks = [...timeBlocks, block];
    setTimeBlocks(updatedBlocks);

    // Create recurring blocks if enabled
    if (block.isRecurring) {
      createRecurringBlocks(block, updatedBlocks);
    }

    // Reset form
    setNewBlock({
      title: '',
      description: '',
      category: 'work',
      color: 'blue',
      startTime: '09:00',
      endTime: '10:00',
      focusLabel: '',
      isRecurring: false,
      recurringPattern: 'daily',
      pomodoroSessions: 1,
      location: '',
      attendees: [],
      priority: 'medium',
      linkedTasks: []
    });
    setShowAddForm(false);
  };

  const createRecurringBlocks = (originalBlock: TimeBlock, currentBlocks: TimeBlock[]) => {
    const recurringBlocks: TimeBlock[] = [];
    const startDate = new Date(originalBlock.date);

    // Create next 4 weeks of recurring blocks
    for (let i = 1; i <= 28; i++) {
      const newDate = new Date(startDate);

      switch (originalBlock.recurringPattern) {
        case 'daily':
          newDate.setDate(startDate.getDate() + i);
          break;
        case 'weekly':
          if (i % 7 === 0) {
            newDate.setDate(startDate.getDate() + i);
          } else {
            continue;
          }
          break;
        case 'monthly':
          if (i === 28) { // Only create one monthly recurring block
            newDate.setMonth(startDate.getMonth() + 1);
          } else {
            continue;
          }
          break;
      }

      recurringBlocks.push({
        ...originalBlock,
        id: `${originalBlock.id}_recurring_${i}`,
        date: newDate.toISOString().split('T')[0],
        createdAt: new Date().toISOString()
      });
    }

    setTimeBlocks([...currentBlocks, ...recurringBlocks]);
  };

  const startPomodoro = (blockId: string) => {
    setActivePomodoro(blockId);
    setPomodoroTime(25 * 60);
    setTimeBlocks(blocks => blocks.map(block => 
      block.id === blockId ? { ...block, isRunning: true } : block
    ));
  };

  const pausePomodoro = () => {
    setActivePomodoro(null);
    setTimeBlocks(blocks => blocks.map(block => 
      block.isRunning ? { ...block, isRunning: false } : block
    ));
  };

  const completePomodoro = () => {
    if (activePomodoro) {
      setTimeBlocks(blocks => blocks.map(block => 
        block.id === activePomodoro 
          ? { ...block, completedPomodoros: block.completedPomodoros + 1, isRunning: false }
          : block
      ));
      setActivePomodoro(null);
      // Show notification or celebration
    }
  };

  const generateAISuggestions = () => {
    const suggestions = [
      { title: 'Deep Work Session', category: 'work', focusLabel: 'High Focus', duration: 2 },
      { title: 'Email Processing', category: 'work', focusLabel: 'Administrative', duration: 1 },
      { title: 'Exercise Break', category: 'health', focusLabel: 'Energy Boost', duration: 0.5 },
      { title: 'Learning Session', category: 'learning', focusLabel: 'Skill Building', duration: 1.5 },
      { title: 'Team Meeting', category: 'work', focusLabel: 'Collaboration', duration: 1 }
    ];

    const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
    setNewBlock(prev => ({
      ...prev,
      title: randomSuggestion.title,
      category: randomSuggestion.category,
      focusLabel: randomSuggestion.focusLabel
    }));
  };

  const generateDailySummary = () => {
    const today = currentDate.toISOString().split('T')[0];
    const todayBlocks = timeBlocks.filter(block => block.date === today);
    
    const summary = {
      totalBlocks: todayBlocks.length,
      completedPomodoros: todayBlocks.reduce((sum, block) => sum + block.completedPomodoros, 0),
      totalPlannedPomodoros: todayBlocks.reduce((sum, block) => sum + block.pomodoroSessions, 0),
      categories: todayBlocks.reduce((acc, block) => {
        acc[block.category] = (acc[block.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    // In a real app, this would send an email
    console.log('Daily Summary:', summary);
    alert('Daily summary generated! Check console for details.');
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTodayBlocks = () => {
    const today = currentDate.toISOString().split('T')[0];
    return timeBlocks.filter(block => block.date === today);
  };

  return (
    <div className="min-h-screen p-4 pb-24 bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex justify-between items-center mb-6"
      >
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-cyan-600 bg-clip-text text-transparent">
            Planner & Scheduler 📅
          </h1>
          <p className="text-gray-600">Time-blocking with Pomodoro integration</p>
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={generateDailySummary}
            variant="outline"
            className="rounded-2xl"
          >
            <Mail className="w-4 h-4 mr-2" />
            Summary
          </Button>
          <Button
            onClick={() => setShowAddForm(true)}
            className="bg-gradient-to-r from-indigo-600 to-cyan-600 text-white rounded-2xl"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Block
          </Button>
        </div>
      </motion.div>

      {/* View Controls */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex space-x-2">
          {(['daily', 'weekly', 'monthly'] as const).map(viewType => (
            <Button
              key={viewType}
              onClick={() => setView(viewType)}
              variant={view === viewType ? 'default' : 'outline'}
              className="rounded-xl capitalize"
            >
              {viewType}
            </Button>
          ))}
        </div>
        
        <div className="flex items-center space-x-4">
          <Button
            onClick={() => setCurrentDate(new Date(currentDate.setDate(currentDate.getDate() - 1)))}
            variant="outline"
            size="sm"
            className="rounded-xl"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <span className="font-semibold text-lg">
            {currentDate.toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </span>
          <Button
            onClick={() => setCurrentDate(new Date(currentDate.setDate(currentDate.getDate() + 1)))}
            variant="outline"
            size="sm"
            className="rounded-xl"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Conflicts Warning */}
      {conflicts.length > 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-6"
        >
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center text-red-700">
                <AlertTriangle className="w-5 h-5 mr-2" />
                <span className="font-medium">Time conflicts detected in your schedule!</span>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Add Block Form */}
      <AnimatePresence>
        {showAddForm && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="mb-6"
          >
            <Card className="border-0 shadow-xl">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Create Time Block
                  <Button
                    onClick={generateAISuggestions}
                    variant="outline"
                    size="sm"
                    className="rounded-xl"
                  >
                    <Zap className="w-4 h-4 mr-2" />
                    AI Suggest
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  placeholder="Block title"
                  value={newBlock.title}
                  onChange={(e) => setNewBlock({ ...newBlock, title: e.target.value })}
                  className="rounded-xl"
                />

                <Textarea
                  placeholder="Description (optional)"
                  value={newBlock.description}
                  onChange={(e) => setNewBlock({ ...newBlock, description: e.target.value })}
                  className="rounded-xl"
                  rows={2}
                />

                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="time"
                    value={newBlock.startTime}
                    onChange={(e) => setNewBlock({ ...newBlock, startTime: e.target.value })}
                    className="rounded-xl"
                  />
                  <Input
                    type="time"
                    value={newBlock.endTime}
                    onChange={(e) => setNewBlock({ ...newBlock, endTime: e.target.value })}
                    className="rounded-xl"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <select
                    value={newBlock.category}
                    onChange={(e) => setNewBlock({ ...newBlock, category: e.target.value })}
                    className="rounded-xl border-2 border-gray-200 px-3 py-2"
                  >
                    {Object.entries(categories).map(([key, cat]) => (
                      <option key={key} value={key}>{cat.label}</option>
                    ))}
                  </select>
                  <select
                    value={newBlock.priority}
                    onChange={(e) => setNewBlock({ ...newBlock, priority: e.target.value as any })}
                    className="rounded-xl border-2 border-gray-200 px-3 py-2"
                  >
                    <option value="low">Low Priority</option>
                    <option value="medium">Medium Priority</option>
                    <option value="high">High Priority</option>
                    <option value="urgent">Urgent</option>
                  </select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Input
                    placeholder="Focus label"
                    value={newBlock.focusLabel}
                    onChange={(e) => setNewBlock({ ...newBlock, focusLabel: e.target.value })}
                    className="rounded-xl"
                  />
                  <Input
                    placeholder="Location (optional)"
                    value={newBlock.location}
                    onChange={(e) => setNewBlock({ ...newBlock, location: e.target.value })}
                    className="rounded-xl"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="number"
                    min="1"
                    max="8"
                    placeholder="Pomodoro sessions"
                    value={newBlock.pomodoroSessions}
                    onChange={(e) => setNewBlock({ ...newBlock, pomodoroSessions: parseInt(e.target.value) || 1 })}
                    className="rounded-xl"
                  />
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="recurring"
                      checked={newBlock.isRecurring}
                      onChange={(e) => setNewBlock({ ...newBlock, isRecurring: e.target.checked })}
                      className="rounded"
                    />
                    <label htmlFor="recurring" className="text-sm">Recurring</label>
                  </div>
                </div>

                {newBlock.isRecurring && (
                  <select
                    value={newBlock.recurringPattern}
                    onChange={(e) => setNewBlock({ ...newBlock, recurringPattern: e.target.value as any })}
                    className="rounded-xl border-2 border-gray-200 px-3 py-2"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                )}
                <div className="flex space-x-3">
                  <Button
                    onClick={addTimeBlock}
                    className="bg-gradient-to-r from-indigo-600 to-cyan-600"
                  >
                    Create Block
                  </Button>
                  <Button
                    onClick={() => setShowAddForm(false)}
                    variant="outline"
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Time Blocks */}
      <div className="space-y-4">
        {getTodayBlocks().map(block => (
          <motion.div
            key={block.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            whileHover={{ scale: 1.02 }}
            className="group"
          >
            <Card className={`border-0 shadow-lg ${conflicts.includes(block.id) ? 'ring-2 ring-red-500' : ''}`}>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className={`w-4 h-4 rounded-full ${categories[block.category as keyof typeof categories].color}`}></div>
                      <h3 className="font-bold text-lg">{block.title}</h3>
                      <span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded-full text-xs">
                        {block.focusLabel}
                      </span>
                      <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">
                        {categories[block.category as keyof typeof categories].label}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {block.startTime} - {block.endTime}
                      </div>
                      <div className="flex items-center">
                        <Target className="w-4 h-4 mr-1" />
                        {block.completedPomodoros}/{block.pomodoroSessions} Pomodoros
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                      <div 
                        className="bg-gradient-to-r from-indigo-500 to-cyan-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(block.completedPomodoros / block.pomodoroSessions) * 100}%` }}
                      ></div>
                    </div>

                    {/* Pomodoro Controls */}
                    <div className="flex items-center space-x-2">
                      {activePomodoro === block.id ? (
                        <>
                          <div className="font-mono text-lg font-bold text-indigo-600">
                            {formatTime(pomodoroTime)}
                          </div>
                          <Button
                            onClick={pausePomodoro}
                            size="sm"
                            variant="outline"
                            className="rounded-xl"
                          >
                            <Pause className="w-4 h-4" />
                          </Button>
                          <Button
                            onClick={completePomodoro}
                            size="sm"
                            variant="outline"
                            className="rounded-xl"
                          >
                            <Square className="w-4 h-4" />
                          </Button>
                        </>
                      ) : (
                        <Button
                          onClick={() => startPomodoro(block.id)}
                          size="sm"
                          className="bg-gradient-to-r from-indigo-500 to-cyan-500 rounded-xl"
                          disabled={block.completedPomodoros >= block.pomodoroSessions}
                        >
                          <Play className="w-4 h-4 mr-1" />
                          Start Pomodoro
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}

        {getTodayBlocks().length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg mb-4">No time blocks scheduled for today</p>
            <Button
              onClick={() => setShowAddForm(true)}
              className="bg-gradient-to-r from-indigo-600 to-cyan-600 rounded-2xl"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Your First Block
            </Button>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default PlannerScheduler;
