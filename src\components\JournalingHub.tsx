import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus, Calendar, BookOpen, TrendingUp, Search, Filter,
  Edit, Trash2, Heart, Star, Smile, Frown, Meh, Mic,
  Camera, Image, FileText, Download, Lock, Brain,
  Tag, Link, Grid, List, Upload, Volume2, Eye, Clock,
  Square
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';

interface JournalEntry {
  id: string;
  title: string;
  content: string;
  type: 'free-write' | 'daily-prompt' | 'mood-based' | 'voice-note' | 'threaded';
  mood: 'happy' | 'sad' | 'neutral' | 'excited' | 'anxious' | 'grateful' | 'reflective';
  tags: string[];
  date: string;
  createdAt: string;
  weather?: string;
  location?: string;
  mediaAttachments?: {
    type: 'image' | 'audio' | 'video';
    url: string;
    name: string;
  }[];
  voiceTranscription?: string;
  isPrivate: boolean;
  linkedEntries?: string[];
  prompt?: string;
  aiSummary?: string;
  wordCount: number;
  readingTime: number;
}

const JournalingHub = () => {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const saved = localStorage.getItem('billionDollarDarkMode');
    return saved ? JSON.parse(saved) : false;
  });

  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMood, setSelectedMood] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [view, setView] = useState<'list' | 'calendar' | 'analytics' | 'grid'>('list');
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [selectedEntry, setSelectedEntry] = useState<JournalEntry | null>(null);
  const [showEntryDetails, setShowEntryDetails] = useState(false);

  const [newEntry, setNewEntry] = useState({
    title: '',
    content: '',
    type: 'free-write' as JournalEntry['type'],
    mood: 'neutral' as JournalEntry['mood'],
    tags: [] as string[],
    weather: '',
    location: '',
    mediaAttachments: [] as JournalEntry['mediaAttachments'],
    isPrivate: false,
    linkedEntries: [] as string[],
    prompt: ''
  });

  useEffect(() => {
    loadEntries();
  }, []);

  const loadEntries = () => {
    const savedEntries = localStorage.getItem('journalEntries');
    if (savedEntries) {
      setEntries(JSON.parse(savedEntries));
    }
  };

  const saveEntries = (updatedEntries: JournalEntry[]) => {
    setEntries(updatedEntries);
    localStorage.setItem('journalEntries', JSON.stringify(updatedEntries));
  };

  const addEntry = () => {
    if (!newEntry.title.trim() || !newEntry.content.trim()) return;

    const wordCount = newEntry.content.trim().split(/\s+/).length;
    const readingTime = Math.ceil(wordCount / 200); // Average reading speed

    const entry: JournalEntry = {
      id: Date.now().toString(),
      title: newEntry.title,
      content: newEntry.content,
      type: newEntry.type,
      mood: newEntry.mood,
      tags: newEntry.tags,
      date: new Date().toISOString().split('T')[0],
      createdAt: new Date().toISOString(),
      weather: newEntry.weather,
      location: newEntry.location,
      mediaAttachments: newEntry.mediaAttachments,
      isPrivate: newEntry.isPrivate,
      linkedEntries: newEntry.linkedEntries,
      prompt: newEntry.prompt,
      wordCount,
      readingTime
    };

    const updatedEntries = [entry, ...entries];
    saveEntries(updatedEntries);
    setNewEntry({
      title: '',
      content: '',
      type: 'free-write',
      mood: 'neutral',
      tags: [],
      weather: '',
      location: '',
      mediaAttachments: [],
      isPrivate: false,
      linkedEntries: [],
      prompt: ''
    });
    setShowForm(false);
  };

  const deleteEntry = (entryId: string) => {
    const updatedEntries = entries.filter(entry => entry.id !== entryId);
    saveEntries(updatedEntries);
  };

  const getFilteredEntries = () => {
    return entries.filter(entry => {
      const matchesSearch = entry.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           entry.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           entry.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      const matchesMood = !selectedMood || entry.mood === selectedMood;
      const matchesType = !selectedType || entry.type === selectedType;

      return matchesSearch && matchesMood && matchesType;
    });
  };

  const getEntryStats = () => {
    const total = entries.length;
    const thisWeek = entries.filter(entry => {
      const entryDate = new Date(entry.date);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return entryDate >= weekAgo;
    }).length;

    const moodCounts = entries.reduce((acc, entry) => {
      acc[entry.mood] = (acc[entry.mood] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return { total, thisWeek, moodCounts };
  };

  const stats = getEntryStats();
  const filteredEntries = getFilteredEntries();

  // Voice recording functions
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setIsRecording(true);
      setRecordingTime(0);

      // Start recording timer
      const timer = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

      // In a real app, you would use MediaRecorder API here
      console.log('Recording started...');

      // Stop recording after 5 minutes max
      setTimeout(() => {
        if (isRecording) {
          stopRecording();
        }
      }, 300000);

    } catch (error) {
      console.error('Error accessing microphone:', error);
      alert('Could not access microphone. Please check permissions.');
    }
  };

  const stopRecording = () => {
    setIsRecording(false);
    setRecordingTime(0);
    // In a real app, you would process the recording here
    console.log('Recording stopped');
    alert('Voice recording feature coming soon!');
  };

  // Daily prompt generator
  const generateDailyPrompt = () => {
    const prompts = [
      "What are three things you're grateful for today?",
      "Describe a moment that made you smile recently.",
      "What challenge did you overcome this week?",
      "If you could give your past self advice, what would it be?",
      "What's something new you learned today?",
      "How did you show kindness to someone recently?",
      "What's a goal you're working towards and why is it important?",
      "Describe your ideal day from start to finish.",
      "What's a memory that always makes you happy?",
      "How have you grown as a person this year?"
    ];

    const randomPrompt = prompts[Math.floor(Math.random() * prompts.length)];
    setNewEntry(prev => ({
      ...prev,
      type: 'daily-prompt',
      prompt: randomPrompt,
      title: 'Daily Reflection',
      content: `Prompt: ${randomPrompt}\n\n`
    }));
    setShowForm(true);
  };

  // Export functions
  const exportToPDF = () => {
    alert('PDF export feature coming soon!');
  };

  const exportToMarkdown = () => {
    const markdown = filteredEntries.map(entry => {
      return `# ${entry.title}\n\n**Date:** ${entry.date}\n**Mood:** ${entry.mood}\n**Tags:** ${entry.tags.join(', ')}\n\n${entry.content}\n\n---\n\n`;
    }).join('');

    const blob = new Blob([markdown], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'journal-entries.md';
    a.click();
    URL.revokeObjectURL(url);
  };

  // Format time for recording
  const formatRecordingTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const moodIcons = {
    happy: <Smile className="w-5 h-5 text-green-600" />,
    sad: <Frown className="w-5 h-5 text-blue-600" />,
    neutral: <Meh className="w-5 h-5 text-gray-600" />,
    excited: <Star className="w-5 h-5 text-yellow-600" />,
    anxious: <Heart className="w-5 h-5 text-red-600" />
  };

  const moodColors = {
    happy: 'bg-green-100 text-green-800 border-green-200',
    sad: 'bg-blue-100 text-blue-800 border-blue-200',
    neutral: 'bg-gray-100 text-gray-800 border-gray-200',
    excited: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    anxious: 'bg-red-100 text-red-800 border-red-200'
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-rose-50">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-4 pb-32">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4 sm:mb-6"
        >
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-amber-600 via-rose-600 to-pink-600 bg-clip-text text-transparent">
              📖 Journal Hub
            </h1>
            <p className={`text-sm sm:text-base ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Capture your thoughts and emotions
            </p>
          </div>

          {/* Action Buttons - Positioned Higher and More Visible */}
          <div className="flex flex-col sm:flex-row gap-2 flex-shrink-0 sm:-mt-1">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={() => setShowForm(true)}
                className="w-full sm:w-auto bg-gradient-to-r from-amber-600 to-rose-600 hover:from-amber-700 hover:to-rose-700 rounded-2xl shadow-lg text-sm"
                size="sm"
              >
                <Plus className="w-4 h-4 mr-2" />
                New Entry
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={generateDailyPrompt}
                variant="outline"
                className="w-full sm:w-auto rounded-2xl text-sm border-amber-200 hover:bg-amber-50"
                size="sm"
              >
                <Brain className="w-4 h-4 mr-2" />
                Daily Prompt
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={isRecording ? stopRecording : startRecording}
                variant="outline"
                className={`w-full sm:w-auto rounded-2xl text-sm ${
                  isRecording
                    ? 'bg-red-50 border-red-200 text-red-600 hover:bg-red-100'
                    : 'border-rose-200 hover:bg-rose-50'
                }`}
                size="sm"
              >
                {isRecording ? (
                  <>
                    <Square className="w-4 h-4 mr-2" />
                    Stop ({formatRecordingTime(recordingTime)})
                  </>
                ) : (
                  <>
                    <Mic className="w-4 h-4 mr-2" />
                    Voice Note
                  </>
                )}
              </Button>
            </motion.div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-4 sm:mb-6"
        >
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-amber-100 to-orange-100 border-0 shadow-lg">
            <BookOpen className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-amber-600" />
            <div className="text-xl sm:text-2xl font-bold text-amber-700">{stats.total}</div>
            <div className="text-xs sm:text-sm text-amber-600">Total Entries</div>
          </Card>
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-rose-100 to-pink-100 border-0 shadow-lg">
            <Calendar className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-rose-600" />
            <div className="text-xl sm:text-2xl font-bold text-rose-700">{stats.thisWeek}</div>
            <div className="text-xs sm:text-sm text-rose-600">This Week</div>
          </Card>
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-green-100 to-emerald-100 border-0 shadow-lg">
            <Smile className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-green-600" />
            <div className="text-xl sm:text-2xl font-bold text-green-700">{stats.moodCounts.happy || 0}</div>
            <div className="text-xs sm:text-sm text-green-600">Happy Days</div>
          </Card>
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-purple-100 to-indigo-100 border-0 shadow-lg">
            <TrendingUp className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-purple-600" />
            <div className="text-xl sm:text-2xl font-bold text-purple-700">
              {entries.length > 0 ? Math.round((stats.moodCounts.happy || 0) / entries.length * 100) : 0}%
            </div>
            <div className="text-xs sm:text-sm text-purple-600">Happiness</div>
          </Card>
        </motion.div>

        {/* Enhanced Toolbar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex flex-wrap gap-2 mb-4"
        >
          <Button
            onClick={() => setView('list')}
            variant={view === 'list' ? 'default' : 'outline'}
            size="sm"
            className="rounded-xl"
          >
            <List className="w-4 h-4 mr-1" />
            List
          </Button>
          <Button
            onClick={() => setView('grid')}
            variant={view === 'grid' ? 'default' : 'outline'}
            size="sm"
            className="rounded-xl"
          >
            <Grid className="w-4 h-4 mr-1" />
            Grid
          </Button>
          <Button
            onClick={() => setView('calendar')}
            variant={view === 'calendar' ? 'default' : 'outline'}
            size="sm"
            className="rounded-xl"
          >
            <Calendar className="w-4 h-4 mr-1" />
            Calendar
          </Button>
          <Button
            onClick={() => setView('analytics')}
            variant={view === 'analytics' ? 'default' : 'outline'}
            size="sm"
            className="rounded-xl"
          >
            <TrendingUp className="w-4 h-4 mr-1" />
            Analytics
          </Button>

          <div className="ml-auto flex gap-2">
            <Button
              onClick={exportToMarkdown}
              variant="outline"
              size="sm"
              className="rounded-xl"
            >
              <Download className="w-4 h-4 mr-1" />
              Export
            </Button>
            <Button
              onClick={exportToPDF}
              variant="outline"
              size="sm"
              className="rounded-xl"
            >
              <FileText className="w-4 h-4 mr-1" />
              PDF
            </Button>
          </div>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex flex-col sm:flex-row gap-3 mb-6"
        >
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search entries, tags, or content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 rounded-xl"
            />
          </div>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 border rounded-xl text-sm"
          >
            <option value="">All Types</option>
            <option value="free-write">Free Write</option>
            <option value="daily-prompt">Daily Prompt</option>
            <option value="mood-based">Mood Based</option>
            <option value="voice-note">Voice Note</option>
          </select>

          <select
            value={selectedMood}
            onChange={(e) => setSelectedMood(e.target.value)}
            className="px-3 py-2 border rounded-xl text-sm"
          >
            <option value="">All Moods</option>
            <option value="happy">😊 Happy</option>
            <option value="sad">😢 Sad</option>
            <option value="excited">🤩 Excited</option>
            <option value="anxious">😰 Anxious</option>
            <option value="grateful">🙏 Grateful</option>
            <option value="reflective">🤔 Reflective</option>
          </select>
        </motion.div>

        {/* Dynamic View Rendering */}
        {view === 'grid' ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          >
            {filteredEntries.map(entry => (
              <motion.div
                key={entry.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="group"
              >
                <Card className={`border-0 shadow-lg hover:shadow-xl transition-all duration-300 ${
                  isDarkMode ? 'bg-gray-800/80' : 'bg-white/80'
                } backdrop-blur-sm rounded-2xl h-full`}>
                  <CardContent className="p-4 h-full flex flex-col">
                    <div className="flex items-center gap-2 mb-3">
                      {moodIcons[entry.mood]}
                      <Badge className={`text-xs ${moodColors[entry.mood]}`}>
                        {entry.mood}
                      </Badge>
                      <Badge variant="outline" className="text-xs ml-auto">
                        {entry.type}
                      </Badge>
                    </div>

                    <h3 className={`font-medium text-sm mb-2 ${
                      isDarkMode ? 'text-white' : 'text-gray-800'
                    }`}>
                      {entry.title}
                    </h3>

                    <p className={`text-xs mb-3 flex-1 ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      {entry.content.substring(0, 120)}
                      {entry.content.length > 120 ? '...' : ''}
                    </p>

                    <div className="flex flex-wrap items-center gap-1 mb-3">
                      {entry.tags.slice(0, 3).map(tag => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          #{tag}
                        </Badge>
                      ))}
                      {entry.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{entry.tags.length - 3}
                        </Badge>
                      )}
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">{entry.date}</span>
                      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          onClick={() => {
                            setSelectedEntry(entry);
                            setShowEntryDetails(true);
                          }}
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:bg-blue-50"
                        >
                          <Eye className="w-3 h-3" />
                        </Button>
                        <Button
                          onClick={() => deleteEntry(entry.id)}
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:bg-red-50"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        ) : view === 'calendar' ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-2xl shadow-lg p-6"
          >
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold mb-2">Journal Calendar</h3>
              <p className="text-gray-600">Your writing journey visualized</p>
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-2 mb-4">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                <div key={day} className="text-center text-sm font-medium text-gray-500 p-2">
                  {day}
                </div>
              ))}

              {/* Generate calendar days for current month */}
              {Array.from({ length: 35 }, (_, i) => {
                const date = new Date();
                date.setDate(date.getDate() - date.getDay() + i);
                const dateStr = date.toISOString().split('T')[0];
                const isCurrentMonth = date.getMonth() === new Date().getMonth();
                const isToday = dateStr === new Date().toISOString().split('T')[0];

                // Check if any entries exist on this date
                const dayEntries = entries.filter(entry => entry.date === dateStr);
                const hasEntry = dayEntries.length > 0;
                const entryMood = hasEntry ? dayEntries[0].mood : null;

                return (
                  <div
                    key={i}
                    className={`aspect-square flex items-center justify-center text-sm rounded-lg transition-all cursor-pointer ${
                      isCurrentMonth
                        ? isToday
                          ? 'bg-amber-500 text-white font-bold'
                          : hasEntry
                            ? entryMood === 'happy'
                              ? 'bg-green-100 text-green-800 hover:bg-green-200'
                              : entryMood === 'sad'
                                ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                                : entryMood === 'excited'
                                  ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                                  : 'bg-purple-100 text-purple-800 hover:bg-purple-200'
                            : 'bg-gray-50 text-gray-400 hover:bg-gray-100'
                        : 'text-gray-300'
                    }`}
                    onClick={() => {
                      if (hasEntry) {
                        setSelectedEntry(dayEntries[0]);
                        setShowEntryDetails(true);
                      }
                    }}
                  >
                    <div className="text-center">
                      <div>{date.getDate()}</div>
                      {hasEntry && (
                        <div className="text-xs mt-1">
                          {dayEntries.length > 1 ? `${dayEntries.length} entries` : '1 entry'}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Legend */}
            <div className="flex justify-center gap-4 text-xs">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-green-100 rounded"></div>
                <span>Happy</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-blue-100 rounded"></div>
                <span>Sad</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-yellow-100 rounded"></div>
                <span>Excited</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-purple-100 rounded"></div>
                <span>Other</span>
              </div>
            </div>
          </motion.div>
        ) : view === 'analytics' ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            {/* Writing Analytics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="p-4 text-center bg-gradient-to-br from-amber-100 to-orange-100">
                <FileText className="w-8 h-8 mx-auto mb-2 text-amber-600" />
                <div className="text-2xl font-bold text-amber-700">
                  {entries.reduce((sum, entry) => sum + entry.wordCount, 0)}
                </div>
                <div className="text-sm text-amber-600">Total Words</div>
              </Card>

              <Card className="p-4 text-center bg-gradient-to-br from-blue-100 to-cyan-100">
                <Clock className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                <div className="text-2xl font-bold text-blue-700">
                  {entries.reduce((sum, entry) => sum + entry.readingTime, 0)}
                </div>
                <div className="text-sm text-blue-600">Minutes of Content</div>
              </Card>

              <Card className="p-4 text-center bg-gradient-to-br from-green-100 to-emerald-100">
                <TrendingUp className="w-8 h-8 mx-auto mb-2 text-green-600" />
                <div className="text-2xl font-bold text-green-700">
                  {Math.max(...Array.from({ length: 7 }, (_, i) => {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    return entries.filter(e => e.date === date.toISOString().split('T')[0]).length;
                  }), 0)}
                </div>
                <div className="text-sm text-green-600">Best Day</div>
              </Card>

              <Card className="p-4 text-center bg-gradient-to-br from-purple-100 to-pink-100">
                <Heart className="w-8 h-8 mx-auto mb-2 text-purple-600" />
                <div className="text-2xl font-bold text-purple-700">
                  {Math.round((entries.filter(e => e.mood === 'happy').length / Math.max(entries.length, 1)) * 100)}%
                </div>
                <div className="text-sm text-purple-600">Happy Entries</div>
              </Card>
            </div>

            {/* Recent Entries List */}
            <Card className="p-6">
              <h3 className="text-lg font-bold mb-4">Recent Entries</h3>
              <div className="space-y-3">
                {entries.slice(0, 5).map(entry => (
                  <div key={entry.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      {moodIcons[entry.mood]}
                      <div>
                        <div className="font-medium">{entry.title}</div>
                        <div className="text-sm text-gray-500">{entry.date}</div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-500">
                      {entry.wordCount} words
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </motion.div>
        ) : (
          <div className="space-y-3 sm:space-y-4">
            {filteredEntries.map(entry => (
              <motion.div
                key={entry.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="group"
              >
                <Card className={`border-0 shadow-lg hover:shadow-xl transition-all duration-300 ${
                  isDarkMode ? 'bg-gray-800/80' : 'bg-white/80'
                } backdrop-blur-sm rounded-2xl`}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          {moodIcons[entry.mood]}
                          <h3 className={`font-medium text-sm sm:text-base ${
                            isDarkMode ? 'text-white' : 'text-gray-800'
                          }`}>
                            {entry.title}
                          </h3>
                          <Badge className={`text-xs ${moodColors[entry.mood]}`}>
                            {entry.mood}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {entry.type}
                          </Badge>
                        </div>
                        <p className={`text-xs sm:text-sm mb-2 ${
                          isDarkMode ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                          {entry.content.substring(0, 150)}
                          {entry.content.length > 150 ? '...' : ''}
                        </p>
                        <div className="flex flex-wrap items-center gap-2">
                          <span className="text-xs text-gray-500">{entry.date}</span>
                          <span className="text-xs text-gray-500">•</span>
                          <span className="text-xs text-gray-500">{entry.wordCount} words</span>
                          <span className="text-xs text-gray-500">•</span>
                          <span className="text-xs text-gray-500">{entry.readingTime} min read</span>
                          {entry.tags.map(tag => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              #{tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          onClick={() => {
                            setSelectedEntry(entry);
                            setShowEntryDetails(true);
                          }}
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:bg-blue-50"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          onClick={() => deleteEntry(entry.id)}
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}

        {filteredEntries.length === 0 && (
          <div className="text-center py-12">
            <BookOpen className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-500 mb-2">No journal entries found</h3>
            <p className="text-gray-400 mb-4">Start writing your first entry!</p>
            <Button 
              onClick={() => setShowForm(true)}
              className="bg-amber-600 hover:bg-amber-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Entry
            </Button>
          </div>
        )}

        {/* Enhanced Journal Entry Form */}
        <Dialog open={showForm} onOpenChange={setShowForm}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Plus className="w-5 h-5" />
                {newEntry.type === 'daily-prompt' ? 'Daily Reflection' : 'New Journal Entry'}
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              {/* Entry Type Selection */}
              <div>
                <Label>Entry Type</Label>
                <select
                  value={newEntry.type}
                  onChange={(e) => setNewEntry(prev => ({ ...prev, type: e.target.value as any }))}
                  className="w-full mt-1 px-3 py-2 border rounded-lg"
                >
                  <option value="free-write">Free Write</option>
                  <option value="daily-prompt">Daily Prompt</option>
                  <option value="mood-based">Mood Based</option>
                  <option value="voice-note">Voice Note</option>
                </select>
              </div>

              {/* Daily Prompt Display */}
              {newEntry.type === 'daily-prompt' && newEntry.prompt && (
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <h4 className="font-medium text-amber-800 mb-2">Today's Prompt:</h4>
                  <p className="text-amber-700">{newEntry.prompt}</p>
                </div>
              )}

              {/* Title */}
              <div>
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  placeholder="Give your entry a title..."
                  value={newEntry.title}
                  onChange={(e) => setNewEntry(prev => ({ ...prev, title: e.target.value }))}
                  className="mt-1"
                />
              </div>

              {/* Content */}
              <div>
                <Label htmlFor="content">Content</Label>
                <Textarea
                  id="content"
                  placeholder="What's on your mind?"
                  value={newEntry.content}
                  onChange={(e) => setNewEntry(prev => ({ ...prev, content: e.target.value }))}
                  className="mt-1 min-h-32"
                  rows={8}
                />
                <div className="text-xs text-gray-500 mt-1">
                  {newEntry.content.trim().split(/\s+/).length} words •
                  {Math.ceil(newEntry.content.trim().split(/\s+/).length / 200)} min read
                </div>
              </div>

              {/* Mood Selection */}
              <div>
                <Label>How are you feeling?</Label>
                <div className="grid grid-cols-3 gap-2 mt-2">
                  {Object.entries({
                    happy: '😊 Happy',
                    sad: '😢 Sad',
                    excited: '🤩 Excited',
                    anxious: '😰 Anxious',
                    grateful: '🙏 Grateful',
                    reflective: '🤔 Reflective'
                  }).map(([mood, label]) => (
                    <Button
                      key={mood}
                      type="button"
                      variant={newEntry.mood === mood ? "default" : "outline"}
                      className="text-sm"
                      onClick={() => setNewEntry(prev => ({ ...prev, mood: mood as any }))}
                    >
                      {label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Tags */}
              <div>
                <Label htmlFor="tags">Tags (comma separated)</Label>
                <Input
                  id="tags"
                  placeholder="work, personal, reflection..."
                  value={newEntry.tags.join(', ')}
                  onChange={(e) => setNewEntry(prev => ({
                    ...prev,
                    tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                  }))}
                  className="mt-1"
                />
              </div>

              {/* Location & Weather */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="location">Location (optional)</Label>
                  <Input
                    id="location"
                    placeholder="Where are you?"
                    value={newEntry.location}
                    onChange={(e) => setNewEntry(prev => ({ ...prev, location: e.target.value }))}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="weather">Weather (optional)</Label>
                  <Input
                    id="weather"
                    placeholder="Sunny, rainy, cloudy..."
                    value={newEntry.weather}
                    onChange={(e) => setNewEntry(prev => ({ ...prev, weather: e.target.value }))}
                    className="mt-1"
                  />
                </div>
              </div>

              {/* Privacy Toggle */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="private"
                  checked={newEntry.isPrivate}
                  onChange={(e) => setNewEntry(prev => ({ ...prev, isPrivate: e.target.checked }))}
                  className="rounded"
                />
                <Label htmlFor="private" className="flex items-center gap-2">
                  <Lock className="w-4 h-4" />
                  Private Entry
                </Label>
              </div>

              {/* Media Attachments */}
              <div>
                <Label>Attachments</Label>
                <div className="flex gap-2 mt-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => alert('Photo attachment coming soon!')}
                  >
                    <Camera className="w-4 h-4 mr-2" />
                    Photo
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => alert('Voice recording coming soon!')}
                  >
                    <Mic className="w-4 h-4 mr-2" />
                    Voice
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => alert('File attachment coming soon!')}
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    File
                  </Button>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowForm(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={addEntry}
                  disabled={!newEntry.title.trim() || !newEntry.content.trim()}
                  className="bg-gradient-to-r from-amber-600 to-rose-600 hover:from-amber-700 hover:to-rose-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Save Entry
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Entry Details Dialog */}
        <Dialog open={showEntryDetails} onOpenChange={setShowEntryDetails}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <BookOpen className="w-5 h-5" />
                Entry Details
              </DialogTitle>
            </DialogHeader>

            {selectedEntry && (
              <div className="space-y-6">
                {/* Entry Header */}
                <div className="text-center border-b pb-4">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    {moodIcons[selectedEntry.mood]}
                    <Badge className={`${moodColors[selectedEntry.mood]}`}>
                      {selectedEntry.mood}
                    </Badge>
                    <Badge variant="outline">
                      {selectedEntry.type}
                    </Badge>
                    {selectedEntry.isPrivate && (
                      <Badge variant="outline" className="text-purple-600">
                        <Lock className="w-3 h-3 mr-1" />
                        Private
                      </Badge>
                    )}
                  </div>
                  <h2 className="text-2xl font-bold mb-2">{selectedEntry.title}</h2>
                  <div className="flex justify-center gap-4 text-sm text-gray-500">
                    <span>{selectedEntry.date}</span>
                    <span>•</span>
                    <span>{selectedEntry.wordCount} words</span>
                    <span>•</span>
                    <span>{selectedEntry.readingTime} min read</span>
                  </div>
                </div>

                {/* Daily Prompt */}
                {selectedEntry.prompt && (
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                    <h4 className="font-medium text-amber-800 mb-2">Prompt:</h4>
                    <p className="text-amber-700">{selectedEntry.prompt}</p>
                  </div>
                )}

                {/* Entry Content */}
                <div className="prose max-w-none">
                  <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                    {selectedEntry.content}
                  </div>
                </div>

                {/* Metadata */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                  {selectedEntry.location && (
                    <div>
                      <h4 className="font-medium text-gray-700 mb-1">Location</h4>
                      <p className="text-gray-600">{selectedEntry.location}</p>
                    </div>
                  )}

                  {selectedEntry.weather && (
                    <div>
                      <h4 className="font-medium text-gray-700 mb-1">Weather</h4>
                      <p className="text-gray-600">{selectedEntry.weather}</p>
                    </div>
                  )}

                  {selectedEntry.tags.length > 0 && (
                    <div className="md:col-span-2">
                      <h4 className="font-medium text-gray-700 mb-2">Tags</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedEntry.tags.map(tag => (
                          <Badge key={tag} variant="outline">
                            #{tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Media Attachments */}
                {selectedEntry.mediaAttachments && selectedEntry.mediaAttachments.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-700 mb-2">Attachments</h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {selectedEntry.mediaAttachments.map((attachment, index) => (
                        <div key={index} className="border rounded-lg p-3 text-center">
                          {attachment.type === 'image' ? (
                            <Image className="w-8 h-8 mx-auto mb-1 text-gray-400" />
                          ) : attachment.type === 'audio' ? (
                            <Volume2 className="w-8 h-8 mx-auto mb-1 text-gray-400" />
                          ) : (
                            <FileText className="w-8 h-8 mx-auto mb-1 text-gray-400" />
                          )}
                          <div className="text-xs text-gray-600">{attachment.name}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Linked Entries */}
                {selectedEntry.linkedEntries && selectedEntry.linkedEntries.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-700 mb-2">Linked Entries</h4>
                    <div className="space-y-2">
                      {selectedEntry.linkedEntries.map(linkedId => {
                        const linkedEntry = entries.find(e => e.id === linkedId);
                        return linkedEntry ? (
                          <div key={linkedId} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                            <Link className="w-4 h-4 text-gray-400" />
                            <span className="text-sm">{linkedEntry.title}</span>
                            <span className="text-xs text-gray-500 ml-auto">{linkedEntry.date}</span>
                          </div>
                        ) : null;
                      })}
                    </div>
                  </div>
                )}

                {/* AI Summary */}
                {selectedEntry.aiSummary && (
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Brain className="w-4 h-4 text-purple-600" />
                      <h4 className="font-medium text-purple-800">AI Summary</h4>
                    </div>
                    <p className="text-purple-700">{selectedEntry.aiSummary}</p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex justify-end gap-2 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => setShowEntryDetails(false)}
                  >
                    Close
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      // Edit functionality
                      setNewEntry({
                        title: selectedEntry.title,
                        content: selectedEntry.content,
                        type: selectedEntry.type,
                        mood: selectedEntry.mood,
                        tags: selectedEntry.tags,
                        weather: selectedEntry.weather || '',
                        location: selectedEntry.location || '',
                        mediaAttachments: selectedEntry.mediaAttachments || [],
                        isPrivate: selectedEntry.isPrivate,
                        linkedEntries: selectedEntry.linkedEntries || [],
                        prompt: selectedEntry.prompt || ''
                      });
                      setShowEntryDetails(false);
                      setShowForm(true);
                    }}
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => {
                      deleteEntry(selectedEntry.id);
                      setShowEntryDetails(false);
                    }}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default JournalingHub;
