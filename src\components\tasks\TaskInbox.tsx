
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { X, Plus, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Task } from '../Tasks';

interface TaskInboxProps {
  isOpen: boolean;
  onClose: () => void;
  onAddTask: (task: Partial<Task>) => void;
}

const TaskInbox: React.FC<TaskInboxProps> = ({ isOpen, onClose, onAddTask }) => {
  const [quickTasks, setQuickTasks] = useState<string[]>(['']);

  const addQuickTaskField = () => {
    setQuickTasks([...quickTasks, '']);
  };

  const updateQuickTask = (index: number, value: string) => {
    const updated = [...quickTasks];
    updated[index] = value;
    setQuickTasks(updated);
  };

  const removeQuickTask = (index: number) => {
    const updated = quickTasks.filter((_, i) => i !== index);
    setQuickTasks(updated.length > 0 ? updated : ['']);
  };

  const handleSubmit = () => {
    const validTasks = quickTasks.filter(task => task.trim());
    
    validTasks.forEach(taskTitle => {
      onAddTask({
        title: taskTitle.trim(),
        priority: 'medium',
        status: 'todo'
      });
    });

    setQuickTasks(['']);
    onClose();
  };

  const generateAISuggestions = () => {
    const suggestions = [
      "Review project documentation",
      "Schedule team meeting for next week",
      "Update portfolio website",
      "Prepare presentation slides",
      "Send follow-up emails to clients",
      "Organize workspace and files",
      "Plan weekend activities",
      "Research new technology trends"
    ];
    
    const randomSuggestions = suggestions
      .sort(() => 0.5 - Math.random())
      .slice(0, 3);
    
    setQuickTasks([...quickTasks.filter(t => t.trim()), ...randomSuggestions, '']);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-2xl p-6 w-full max-w-md"
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">Quick Capture</h2>
          <Button variant="ghost" onClick={onClose} className="p-2">
            <X className="w-5 h-5" />
          </Button>
        </div>

        <div className="space-y-3 mb-6">
          {quickTasks.map((task, index) => (
            <div key={index} className="flex items-center space-x-2">
              <Input
                placeholder="What needs to be done?"
                value={task}
                onChange={(e) => updateQuickTask(index, e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    if (index === quickTasks.length - 1) {
                      addQuickTaskField();
                    }
                  }
                }}
                className="flex-1 rounded-xl border-2 border-gray-200 focus:border-purple-500"
                autoFocus={index === 0}
              />
              {quickTasks.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeQuickTask(index)}
                  className="p-1 h-8 w-8 text-red-500 hover:text-red-700"
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
          ))}
        </div>

        <div className="flex space-x-2 mb-4">
          <Button
            variant="outline"
            onClick={addQuickTaskField}
            className="flex-1"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Another
          </Button>
          <Button
            variant="outline"
            onClick={generateAISuggestions}
            className="flex-1"
          >
            <Zap className="w-4 h-4 mr-2" />
            AI Suggest
          </Button>
        </div>

        <div className="flex space-x-2">
          <Button
            onClick={handleSubmit}
            className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
            disabled={!quickTasks.some(task => task.trim())}
          >
            Add {quickTasks.filter(t => t.trim()).length} Task{quickTasks.filter(t => t.trim()).length !== 1 ? 's' : ''}
          </Button>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </motion.div>
    </div>
  );
};

export default TaskInbox;
