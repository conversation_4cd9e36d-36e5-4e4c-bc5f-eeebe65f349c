
import { useLocation, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Home,
  FileText,
  CheckSquare,
  Heart,
  DollarSign,
  Plus,
  RotateCcw,
  Target,
  Clock,
  CalendarDays,
  Activity,
  Settings,
  Zap,
  Brain,
  Sparkles,
  BarChart3,
  Bot,
  Wrench
} from 'lucide-react';

const BottomNav = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const navItems = [
    { path: '/', icon: Home, label: 'Home' },
    { path: '/journal', icon: FileText, label: 'Journal' },
    { path: '/finance', icon: DollarSign, label: 'Finance' },
    { path: '/spiritual', icon: Sparkles, label: 'Spiritual' },
    { path: '/settings', icon: Settings, label: 'Settings' },
  ];

  const quickActions = [
    { icon: FileText, label: 'Quick Note', action: () => navigate('/notes') },
    { icon: CheckSquare, label: 'Add Task', action: () => navigate('/tasks') },
    { icon: Heart, label: 'Log Mood', action: () => navigate('/mood') },
    { icon: RotateCcw, label: 'Routines', action: () => navigate('/routines') },
    { icon: Target, label: 'Goals', action: () => navigate('/goals') },
    { icon: Clock, label: 'Time Tracker', action: () => navigate('/time-tracker') },
    { icon: CalendarDays, label: 'Calendar', action: () => navigate('/calendar') },
    { icon: Activity, label: 'Health', action: () => navigate('/health') },
    { icon: Wrench, label: 'Toolbox', action: () => navigate('/toolbox') },
    { icon: Zap, label: 'Enhanced Kanban', action: () => navigate('/enhanced-kanban') },
    { icon: Brain, label: 'Advanced Goals', action: () => navigate('/advanced-goals') },
    { icon: BarChart3, label: 'Productivity', action: () => navigate('/productivity-dashboard') },
    { icon: Bot, label: 'Automation', action: () => navigate('/automation') },
    { icon: Settings, label: 'Settings', action: () => navigate('/settings') },
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50">
      {/* Floating Action Button - Mobile Responsive */}
      <div className="absolute -top-6 sm:-top-8 right-4 sm:right-6">
        <motion.div className="relative group">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full p-3 sm:p-4 shadow-2xl backdrop-blur-lg border border-white/20 group-hover:shadow-purple-500/50 transition-all duration-300"
            style={{
              boxShadow: '0 20px 40px rgba(139, 92, 246, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)'
            }}
          >
            <Plus className="w-5 h-5 sm:w-6 sm:h-6" />
          </motion.button>

          {/* Quick Actions Menu - Responsive Layout */}
          <motion.div
            className="absolute bottom-14 sm:bottom-16 right-0 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none group-hover:pointer-events-auto"
            style={{ zIndex: 60 }}
          >
            <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 dark:border-gray-700/20 p-3 sm:p-4 w-72 sm:w-80 max-w-[calc(100vw-2rem)] mr-2 sm:mr-0">
              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">Quick Actions</h3>
              <div className="grid grid-cols-3 gap-1.5 sm:gap-2">
                {quickActions.map((action, index) => (
                  <motion.button
                    key={action.label}
                    onClick={action.action}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.03 }}
                    className="flex flex-col items-center p-2 sm:p-3 rounded-xl hover:bg-purple-50 dark:hover:bg-purple-900/30 transition-all duration-200 group/item"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <div className="p-1.5 sm:p-2 bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-800/50 dark:to-blue-800/50 rounded-lg group-hover/item:from-purple-200 group-hover/item:to-blue-200 dark:group-hover/item:from-purple-700/50 dark:group-hover/item:to-blue-700/50 transition-all duration-200 mb-1 sm:mb-2">
                      <action.icon className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-purple-600 dark:text-purple-400" />
                    </div>
                    <span className="font-medium text-gray-700 dark:text-gray-300 text-[10px] sm:text-xs text-center leading-tight">{action.label}</span>
                  </motion.button>
                ))}
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Bottom Navigation - Fully Responsive */}
      <motion.div
        initial={{ y: 100 }}
        animate={{ y: 0 }}
        className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-700/50 px-2 sm:px-4 pt-2 pb-2 sm:pb-4 safe-area-pb"
        style={{
          background: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px) saturate(180%)',
          borderTop: '1px solid rgba(255, 255, 255, 0.2)'
        }}
      >
        <div className="flex justify-around items-center max-w-lg mx-auto">
          {navItems.map((item, index) => {
            const isActive = location.pathname === item.path;
            return (
              <motion.button
                key={item.path}
                onClick={() => navigate(item.path)}
                className="flex flex-col items-center space-y-0.5 sm:space-y-1 py-1.5 sm:py-2 px-2 sm:px-3 rounded-xl transition-all relative min-w-0 flex-1 max-w-[80px]"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className={`relative z-10 ${isActive ? 'text-white' : 'text-gray-500 dark:text-gray-400'}`}>
                  <item.icon className="w-5 h-5 sm:w-6 sm:h-6 mx-auto" />
                  {isActive && (
                    <motion.div
                      layoutId="activeTab"
                      className="absolute -inset-2 sm:-inset-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl -z-10 shadow-lg"
                      initial={false}
                      transition={{
                        type: "spring",
                        stiffness: 500,
                        damping: 30
                      }}
                    />
                  )}
                </div>
                <span className={`text-[10px] sm:text-xs font-medium ${isActive ? 'text-white' : 'text-gray-500 dark:text-gray-400'} truncate w-full text-center`}>
                  {item.label}
                </span>
              </motion.button>
            );
          })}
        </div>
      </motion.div>
    </div>
  );
};

export default BottomNav;
