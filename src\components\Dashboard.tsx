
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Plus, Calendar, Heart, Star, DollarSign, Clock, TrendingUp, Target, CalendarDays, BarChart3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';
import PageHeader from '@/components/ui/PageHeader';
import PageContainer from '@/components/ui/PageContainer';
import ResponsiveGrid from '@/components/ui/ResponsiveGrid';

const Dashboard = ({ userProfile }: { userProfile: any }) => {
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [todayTasks, setTodayTasks] = useState([]);
  const [todayMood, setTodayMood] = useState(null);
  const [weeklySpending, setWeeklySpending] = useState(0);
  const [habitStreaks, setHabitStreaks] = useState([]);
  const [activeRoutines, setActiveRoutines] = useState([]);
  const [goals, setGoals] = useState([]);
  const [todayEvents, setTodayEvents] = useState([]);
  const [timeEntries, setTimeEntries] = useState([]);

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    loadDashboardData();
    return () => clearInterval(timer);
  }, []);

  const loadDashboardData = () => {
    // Load today's tasks
    const tasks = JSON.parse(localStorage.getItem('tasks') || '[]');
    const today = new Date().toDateString();
    const todaysTasks = tasks.filter((task: any) => 
      task.dueDate && new Date(task.dueDate).toDateString() === today
    );
    setTodayTasks(todaysTasks);

    const moods = JSON.parse(localStorage.getItem('moods') || '[]');
    const todayMood = moods.find((mood: any) => 
      new Date(mood.date).toDateString() === today
    );
    setTodayMood(todayMood);

    const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    const weeklyTotal = expenses
      .filter((expense: any) => new Date(expense.date) >= weekStart)
      .reduce((sum: number, expense: any) => sum + expense.amount, 0);
    setWeeklySpending(weeklyTotal);

    const habits = JSON.parse(localStorage.getItem('habits') || '[]');
    setHabitStreaks(habits.slice(0, 3));

    const routines = JSON.parse(localStorage.getItem('routines') || '[]');
    const active = routines.filter((routine: any) => routine.isActive);
    setActiveRoutines(active);

    // Load new data
    const savedGoals = JSON.parse(localStorage.getItem('goals') || '[]');
    setGoals(savedGoals);

    const calendarEvents = JSON.parse(localStorage.getItem('calendarEvents') || '[]');
    const todaysEvents = calendarEvents.filter((event: any) => 
      event.date === new Date().toISOString().split('T')[0]
    );
    setTodayEvents(todaysEvents);

    const savedTimeEntries = JSON.parse(localStorage.getItem('timeEntries') || '[]');
    setTimeEntries(savedTimeEntries);
  };

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  const motivationalQuotes = [
    "Every day is a new beginning. Take a deep breath and start again.",
    "Success is the sum of small efforts repeated day in and day out.",
    "The way to get started is to quit talking and begin doing.",
    "Don't watch the clock; do what it does. Keep going.",
    "Believe you can and you're halfway there.",
    "Your potential is endless. Go do what you were created to do.",
    "Great things never come from comfort zones.",
    "The future depends on what you do today."
  ];

  const todayQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];

  const quickActions = [
    { 
      label: 'Quick Note', 
      icon: Plus, 
      action: () => navigate('/notes'),
      gradient: 'from-purple-500 to-blue-500'
    },
    { 
      label: 'Add Task', 
      icon: Calendar, 
      action: () => navigate('/tasks'),
      gradient: 'from-blue-500 to-cyan-500'
    },
    { 
      label: 'Log Mood', 
      icon: Heart, 
      action: () => navigate('/mood'),
      gradient: 'from-pink-500 to-rose-500'
    },
    { 
      label: 'Start Timer', 
      icon: Clock, 
      action: () => navigate('/time-tracker'),
      gradient: 'from-green-500 to-emerald-500'
    },
    { 
      label: 'Goals', 
      icon: Target, 
      action: () => navigate('/goals'),
      gradient: 'from-orange-500 to-red-500'
    },
    { 
      label: 'Calendar', 
      icon: CalendarDays, 
      action: () => navigate('/calendar'),
      gradient: 'from-indigo-500 to-purple-500'
    }
  ];

  const containerVariants = {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: { staggerChildren: 0.1, delayChildren: 0.2 }
    }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-2 sm:p-4 pb-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-7xl mx-auto"
      >
        {/* Mobile-Optimized Header */}
        <div className="mb-4 sm:mb-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 mb-4">
            <div className="flex-1 min-w-0">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent truncate">
                {getGreeting()}, {userProfile?.name}! 👋
              </h1>
              <p className="text-sm sm:text-base text-gray-600 mt-1">
                {currentTime.toLocaleDateString('en-US', {
                  weekday: 'long',
                  month: 'long',
                  day: 'numeric'
                })}
              </p>
            </div>
            <div className="text-center sm:text-right">
              <div className="text-lg sm:text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                {currentTime.toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: true
                })}
              </div>
              <div className="text-xs text-gray-500">
                {currentTime.toLocaleDateString('en-US', { timeZoneName: 'short' }).split(',')[1]}
              </div>
            </div>
          </div>
        </div>

      {/* Premium Dashboard Preview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="mb-6"
      >
        <Card className="bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 border-0 shadow-xl overflow-hidden">
          <CardContent className="p-6 relative">
            <div className="absolute top-0 right-0 text-6xl opacity-20">✨</div>
            <div className="relative z-10">
              <h3 className="text-white font-bold text-xl mb-2">🎨 Premium Dashboard Available!</h3>
              <p className="text-white/90 text-sm mb-4">
                Experience our new glassmorphism design with mobile-first UI, premium colors, and smooth animations.
              </p>
              <Button
                onClick={() => navigate('/premium-demo')}
                className="bg-white/20 backdrop-blur-sm border border-white/30 text-white hover:bg-white/30 rounded-2xl"
              >
                View Premium Demo ✨
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>

        {/* Mobile-Optimized Quick Actions */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 mb-4 sm:mb-6">
        {quickActions.map((action, index) => (
          <motion.div
            key={action.label}
            variants={itemVariants}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              onClick={action.action}
              className={`w-full h-16 sm:h-20 bg-gradient-to-r ${action.gradient} text-white rounded-xl sm:rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col items-center justify-center space-y-0.5 sm:space-y-1 p-2`}
            >
              <action.icon className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 flex-shrink-0" />
              <span className="text-xs sm:text-sm font-medium text-center leading-tight">{action.label}</span>
            </Button>
          </motion.div>
        ))}
      </motion.div>

      {/* Daily Quote */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="mb-6"
      >
        <Card className="bg-gradient-to-r from-purple-100 via-blue-50 to-indigo-100 border-0 shadow-lg overflow-hidden">
          <CardContent className="p-6 relative">
            <div className="absolute top-0 right-0 text-6xl opacity-10">💫</div>
            <p className="text-center text-gray-700 italic text-lg font-medium relative z-10">
              "{todayQuote}"
            </p>
          </CardContent>
        </Card>
        </div>

      {/* Active Routines */}
      {activeRoutines.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mb-6"
        >
          <Card className="shadow-xl border-0 bg-gradient-to-r from-green-500 to-emerald-500 text-white">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-bold text-lg">🔄 Active Routine</h3>
                  <p className="opacity-90">{activeRoutines[0].name}</p>
                </div>
                <Button
                  onClick={() => navigate('/routines')}
                  variant="outline"
                  className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                >
                  View
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

        {/* Mobile-Optimized Dashboard Widgets */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
        {/* Today's Tasks */}
        <motion.div variants={itemVariants}>
          <Card className="h-full shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-lg">
                <div className="p-2 bg-blue-100 rounded-xl mr-3">
                  <Calendar className="w-5 h-5 text-blue-600" />
                </div>
                Today's Tasks
              </CardTitle>
            </CardHeader>
            <CardContent>
              {todayTasks.length > 0 ? (
                <div className="space-y-3">
                  {todayTasks.slice(0, 3).map((task: any, index) => (
                    <motion.div 
                      key={index} 
                      className="flex items-center space-x-3 p-2 rounded-xl bg-blue-50"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-sm text-gray-700 flex-1">{task.title}</span>
                    </motion.div>
                  ))}
                  {todayTasks.length > 3 && (
                    <p className="text-xs text-gray-500 text-center">+{todayTasks.length - 3} more tasks</p>
                  )}
                </div>
              ) : (
                <div className="text-center py-4">
                  <div className="text-3xl mb-2">🎉</div>
                  <p className="text-gray-500 text-sm">No tasks for today!</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Today's Events */}
        <motion.div variants={itemVariants}>
          <Card className="h-full shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-lg">
                <div className="p-2 bg-rose-100 rounded-xl mr-3">
                  <CalendarDays className="w-5 h-5 text-rose-600" />
                </div>
                Today's Events
              </CardTitle>
            </CardHeader>
            <CardContent>
              {todayEvents.length > 0 ? (
                <div className="space-y-3">
                  {todayEvents.slice(0, 3).map((event: any, index) => (
                    <motion.div 
                      key={index} 
                      className="flex items-center space-x-3 p-2 rounded-xl bg-rose-50"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="w-3 h-3 bg-rose-500 rounded-full"></div>
                      <div className="flex-1">
                        <span className="text-sm text-gray-700 font-medium">{event.title}</span>
                        {event.time && <div className="text-xs text-gray-500">{event.time}</div>}
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4">
                  <div className="text-3xl mb-2">📅</div>
                  <p className="text-gray-500 text-sm">No events today</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Goals Progress */}
        <motion.div variants={itemVariants}>
          <Card className="h-full shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-lg">
                <div className="p-2 bg-orange-100 rounded-xl mr-3">
                  <Target className="w-5 h-5 text-orange-600" />
                </div>
                Goals Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              {goals.length > 0 ? (
                <div className="space-y-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600 mb-1">
                      {goals.filter((g: any) => g.progress === 100).length}/{goals.length}
                    </div>
                    <p className="text-sm text-gray-600">Goals Completed</p>
                  </div>
                  <div className="space-y-2">
                    {goals.slice(0, 2).map((goal: any, index) => (
                      <div key={index} className="bg-orange-50 p-2 rounded-xl">
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-gray-700 font-medium">{goal.title}</span>
                          <span className="text-orange-600 font-bold">{goal.progress}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <div className="text-3xl mb-2">🎯</div>
                  <p className="text-gray-500 text-sm">No goals set yet</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Time Tracking */}
        <motion.div variants={itemVariants}>
          <Card className="h-full shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-lg">
                <div className="p-2 bg-indigo-100 rounded-xl mr-3">
                  <Clock className="w-5 h-5 text-indigo-600" />
                </div>
                Time Tracked
              </CardTitle>
            </CardHeader>
            <CardContent>
              {timeEntries.length > 0 ? (
                <div className="text-center">
                  <div className="text-2xl font-bold text-indigo-600 mb-1">
                    {Math.floor(timeEntries.reduce((total: number, entry: any) => total + (entry.duration || 0), 0) / 3600)}h
                  </div>
                  <p className="text-sm text-gray-600 mb-3">Total Time</p>
                  <div className="text-sm text-gray-500">
                    {timeEntries.length} sessions recorded
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <div className="text-3xl mb-2">⏱️</div>
                  <p className="text-gray-500 text-sm">No time tracked yet</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Mood Check */}
        <motion.div variants={itemVariants}>
          <Card className="h-full shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-lg">
                <div className="p-2 bg-pink-100 rounded-xl mr-3">
                  <Heart className="w-5 h-5 text-pink-600" />
                </div>
                Mood Today
              </CardTitle>
            </CardHeader>
            <CardContent>
              {todayMood ? (
                <div className="text-center">
                  <div className="text-4xl mb-2">{todayMood.emoji}</div>
                  <p className="text-sm text-gray-600 font-medium">{todayMood.mood}</p>
                </div>
              ) : (
                <div className="text-center">
                  <div className="text-3xl mb-3">😊</div>
                  <p className="text-gray-500 text-sm mb-3">How are you feeling?</p>
                  <Button 
                    onClick={() => navigate('/mood')}
                    variant="outline" 
                    size="sm" 
                    className="rounded-full border-2 hover:bg-pink-50"
                  >
                    Log Mood
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Weekly Finance */}
        <motion.div variants={itemVariants}>
          <Card className="h-full shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-lg">
                <div className="p-2 bg-green-100 rounded-xl mr-3">
                  <DollarSign className="w-5 h-5 text-green-600" />
                </div>
                This Week
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  ${weeklySpending.toFixed(2)}
                </div>
                <p className="text-sm text-gray-600 flex items-center justify-center">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  Total Spent
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Habit Streaks */}
        <motion.div variants={itemVariants}>
          <Card className="h-full shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-lg">
                <div className="p-2 bg-yellow-100 rounded-xl mr-3">
                  <Star className="w-5 h-5 text-yellow-600" />
                </div>
                Habit Streaks
              </CardTitle>
            </CardHeader>
            <CardContent>
              {habitStreaks.length > 0 ? (
                <div className="space-y-3">
                  {habitStreaks.map((habit: any, index) => (
                    <motion.div 
                      key={index} 
                      className="flex justify-between items-center p-2 rounded-xl bg-yellow-50"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <span className="text-sm text-gray-700">{habit.name}</span>
                      <span className="text-sm font-bold text-yellow-600 flex items-center">
                        {habit.streak || 0} 
                        <span className="ml-1">🔥</span>
                      </span>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4">
                  <div className="text-3xl mb-2">🎯</div>
                  <p className="text-gray-500 text-sm">No habits tracked yet</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default Dashboard;
