
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { X, Calendar, Tag, MapPin, Clock, Repeat, Link2, Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Task } from '../Tasks';

interface TaskFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (task: Partial<Task>) => void;
  allTasks: Task[];
  existingTags: string[];
  existingProjects: string[];
  editingTask?: Task;
}

const TaskForm: React.FC<TaskFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  allTasks,
  existingTags,
  existingProjects,
  editingTask
}) => {
  const [formData, setFormData] = useState({
    title: editingTask?.title || '',
    description: editingTask?.description || '',
    dueDate: editingTask?.dueDate || '',
    priority: editingTask?.priority || 'medium' as const,
    tags: editingTask?.tags || [],
    category: editingTask?.category || '',
    colorLabel: editingTask?.colorLabel || '#8B5CF6',
    location: editingTask?.location || '',
    project: editingTask?.project || '',
    isRecurring: editingTask?.isRecurring || false,
    recurringPattern: editingTask?.recurringPattern || 'daily',
    estimatedMinutes: editingTask?.estimatedMinutes || 30,
    linkedTasks: editingTask?.linkedTasks || [],
    reminders: editingTask?.reminders || []
  });

  const [newTag, setNewTag] = useState('');
  const [newProject, setNewProject] = useState('');

  const colorOptions = [
    '#8B5CF6', '#3B82F6', '#10B981', '#F59E0B', 
    '#EF4444', '#EC4899', '#6366F1', '#84CC16'
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.trim()) return;

    onSubmit({
      ...formData,
      project: formData.project || newProject || undefined,
      tags: [...formData.tags, ...(newTag ? [newTag] : [])]
    });

    // Reset form
    setFormData({
      title: '',
      description: '',
      dueDate: '',
      priority: 'medium',
      tags: [],
      category: '',
      colorLabel: '#8B5CF6',
      location: '',
      project: '',
      isRecurring: false,
      recurringPattern: 'daily',
      estimatedMinutes: 30,
      linkedTasks: [],
      reminders: []
    });
    setNewTag('');
    setNewProject('');
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  const addReminder = () => {
    const reminderDate = new Date();
    reminderDate.setHours(reminderDate.getHours() + 1);
    
    setFormData(prev => ({
      ...prev,
      reminders: [...prev.reminders, {
        datetime: reminderDate.toISOString().slice(0, 16),
        type: 'notification' as const
      }]
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">
            {editingTask ? 'Edit Task' : 'Create New Task'}
          </h2>
          <Button variant="ghost" onClick={onClose} className="p-2">
            <X className="w-5 h-5" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium mb-2">Task Title *</label>
            <Input
              placeholder="What needs to be done?"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="rounded-xl border-2 border-gray-200 focus:border-purple-500"
              required
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium mb-2">Description</label>
            <textarea
              placeholder="Add more details..."
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:outline-none min-h-[100px] resize-none"
            />
          </div>

          {/* Priority & Due Date */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Priority</label>
              <select
                value={formData.priority}
                onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as any }))}
                className="w-full px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:outline-none"
              >
                <option value="low">🟢 Low</option>
                <option value="medium">🟡 Medium</option>
                <option value="high">🟠 High</option>
                <option value="urgent">🔴 Urgent</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Due Date</label>
              <Input
                type="datetime-local"
                value={formData.dueDate}
                onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                className="rounded-xl border-2 border-gray-200 focus:border-purple-500"
              />
            </div>
          </div>

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium mb-2">Tags</label>
            <div className="flex flex-wrap gap-2 mb-2">
              {formData.tags.map(tag => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="cursor-pointer"
                  onClick={() => removeTag(tag)}
                >
                  <Tag className="w-3 h-3 mr-1" />
                  {tag}
                  <X className="w-3 h-3 ml-1" />
                </Badge>
              ))}
            </div>
            <div className="flex space-x-2">
              <Input
                placeholder="Add new tag..."
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                className="flex-1 rounded-xl border-2 border-gray-200 focus:border-purple-500"
              />
              <Button type="button" onClick={addTag} variant="outline">
                Add
              </Button>
            </div>
            <div className="flex flex-wrap gap-1 mt-2">
              {existingTags.map(tag => (
                <Badge
                  key={tag}
                  variant="outline"
                  className="cursor-pointer text-xs"
                  onClick={() => {
                    if (!formData.tags.includes(tag)) {
                      setFormData(prev => ({ ...prev, tags: [...prev.tags, tag] }));
                    }
                  }}
                >
                  {tag}
                </Badge>
              ))}
            </div>
          </div>

          {/* Project & Location */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Project</label>
              <select
                value={formData.project}
                onChange={(e) => setFormData(prev => ({ ...prev, project: e.target.value }))}
                className="w-full px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:outline-none"
              >
                <option value="">Select project...</option>
                {existingProjects.map(project => (
                  <option key={project} value={project}>{project}</option>
                ))}
              </select>
              <Input
                placeholder="Or create new project..."
                value={newProject}
                onChange={(e) => setNewProject(e.target.value)}
                className="mt-2 rounded-xl border-2 border-gray-200 focus:border-purple-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Location</label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Where?"
                  value={formData.location}
                  onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  className="pl-10 rounded-xl border-2 border-gray-200 focus:border-purple-500"
                />
              </div>
            </div>
          </div>

          {/* Color Label */}
          <div>
            <label className="block text-sm font-medium mb-2">Color Label</label>
            <div className="flex space-x-2">
              {colorOptions.map(color => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, colorLabel: color }))}
                  className={`w-8 h-8 rounded-full border-2 ${
                    formData.colorLabel === color ? 'border-gray-800' : 'border-gray-300'
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>

          {/* Estimated Time */}
          <div>
            <label className="block text-sm font-medium mb-2">Estimated Time (minutes)</label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="number"
                min="5"
                step="5"
                value={formData.estimatedMinutes}
                onChange={(e) => setFormData(prev => ({ ...prev, estimatedMinutes: parseInt(e.target.value) || 30 }))}
                className="pl-10 rounded-xl border-2 border-gray-200 focus:border-purple-500"
              />
            </div>
          </div>

          {/* Recurring Task */}
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.isRecurring}
                onChange={(e) => setFormData(prev => ({ ...prev, isRecurring: e.target.checked }))}
                className="rounded border-gray-300"
              />
              <Repeat className="w-4 h-4" />
              <span className="text-sm font-medium">Recurring Task</span>
            </label>
            
            {formData.isRecurring && (
              <select
                value={formData.recurringPattern}
                onChange={(e) => setFormData(prev => ({ ...prev, recurringPattern: e.target.value }))}
                className="px-3 py-1 rounded-lg border border-gray-300 focus:border-purple-500 focus:outline-none text-sm"
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="custom">Custom</option>
              </select>
            )}
          </div>

          {/* Reminders */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium">Reminders</label>
              <Button type="button" onClick={addReminder} variant="outline" size="sm">
                <Bell className="w-4 h-4 mr-1" />
                Add Reminder
              </Button>
            </div>
            
            {formData.reminders.map((reminder, index) => (
              <div key={index} className="flex items-center space-x-2 mb-2">
                <Input
                  type="datetime-local"
                  value={reminder.datetime}
                  onChange={(e) => {
                    const updatedReminders = [...formData.reminders];
                    updatedReminders[index] = { ...reminder, datetime: e.target.value };
                    setFormData(prev => ({ ...prev, reminders: updatedReminders }));
                  }}
                  className="flex-1 rounded-lg border-2 border-gray-200 focus:border-purple-500"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    const updatedReminders = formData.reminders.filter((_, i) => i !== index);
                    setFormData(prev => ({ ...prev, reminders: updatedReminders }));
                  }}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            ))}
          </div>

          {/* Submit Buttons */}
          <div className="flex space-x-3 pt-4">
            <Button
              type="submit"
              className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
            >
              {editingTask ? 'Update Task' : 'Create Task'}
            </Button>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

export default TaskForm;
