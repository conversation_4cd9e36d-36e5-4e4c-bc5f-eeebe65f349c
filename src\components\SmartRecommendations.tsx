import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain, Lightbulb, Target, TrendingUp, Clock, Star,
  CheckCircle, X, ThumbsUp, ThumbsDown, Zap, Heart,
  Book, Coffee, Moon, Activity, Calendar, Award,
  AlertTriangle, Info, <PERSON>rkles, ArrowRight
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { GlassmorphCard } from '@/components/ui/glassmorphcard';

interface Recommendation {
  id: string;
  type: 'productivity' | 'wellness' | 'habits' | 'goals' | 'learning';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  reasoning: string;
  actionSteps: string[];
  estimatedImpact: number; // 1-10
  timeToImplement: string;
  category: string;
  tags: string[];
  confidence: number; // 0-100
  basedOn: string[];
  dismissed?: boolean;
  implemented?: boolean;
  feedback?: 'helpful' | 'not-helpful';
}

interface UserPattern {
  id: string;
  pattern: string;
  frequency: number;
  impact: 'positive' | 'negative' | 'neutral';
  confidence: number;
}

const SmartRecommendations = () => {
  const [recommendations, setRecommendations] = useState<Recommendation[]>([
    {
      id: '1',
      type: 'productivity',
      priority: 'high',
      title: 'Optimize Your Peak Focus Hours',
      description: 'Schedule your most important tasks between 9-11 AM when your focus is highest.',
      reasoning: 'Analysis shows your Pomodoro sessions have 23% higher productivity ratings during morning hours.',
      actionSteps: [
        'Block 9-11 AM for deep work tasks',
        'Move meetings to afternoon slots',
        'Use this time for complex problem-solving'
      ],
      estimatedImpact: 8,
      timeToImplement: '1 week',
      category: 'Time Management',
      tags: ['focus', 'scheduling', 'productivity'],
      confidence: 87,
      basedOn: ['Pomodoro data', 'Task completion patterns', 'Energy levels']
    },
    {
      id: '2',
      type: 'wellness',
      priority: 'high',
      title: 'Improve Sleep Consistency',
      description: 'Your sleep quality drops 15% when bedtime varies by more than 30 minutes.',
      reasoning: 'Sleep tracking data shows irregular bedtimes correlate with lower next-day productivity.',
      actionSteps: [
        'Set a consistent bedtime alarm',
        'Create a 30-minute wind-down routine',
        'Avoid screens 1 hour before target bedtime'
      ],
      estimatedImpact: 9,
      timeToImplement: '2 weeks',
      category: 'Sleep Optimization',
      tags: ['sleep', 'routine', 'consistency'],
      confidence: 92,
      basedOn: ['Sleep tracking', 'Productivity correlation', 'Mood patterns']
    },
    {
      id: '3',
      type: 'habits',
      priority: 'medium',
      title: 'Stack Your Morning Habits',
      description: 'Combine meditation with your existing coffee routine for better consistency.',
      reasoning: 'Habit stacking increases success rate by 40% according to your completion patterns.',
      actionSteps: [
        'Start 5-minute meditation while coffee brews',
        'Use coffee time as meditation anchor',
        'Gradually increase meditation duration'
      ],
      estimatedImpact: 7,
      timeToImplement: '3 days',
      category: 'Habit Formation',
      tags: ['meditation', 'morning routine', 'habit stacking'],
      confidence: 78,
      basedOn: ['Habit completion data', 'Morning routine analysis']
    },
    {
      id: '4',
      type: 'goals',
      priority: 'medium',
      title: 'Break Down Large Goals',
      description: 'Your goal completion rate is 45% higher for goals with weekly milestones.',
      reasoning: 'Analysis of your goal patterns shows better success with smaller, time-bound targets.',
      actionSteps: [
        'Divide current goals into weekly milestones',
        'Set up weekly review sessions',
        'Celebrate small wins to maintain momentum'
      ],
      estimatedImpact: 8,
      timeToImplement: '1 day',
      category: 'Goal Setting',
      tags: ['goals', 'milestones', 'planning'],
      confidence: 85,
      basedOn: ['Goal completion history', 'Project success patterns']
    },
    {
      id: '5',
      type: 'learning',
      priority: 'low',
      title: 'Optimize Learning Sessions',
      description: 'Your retention is 30% better with 25-minute learning sessions followed by 5-minute breaks.',
      reasoning: 'Learning pattern analysis shows improved comprehension with spaced intervals.',
      actionSteps: [
        'Use Pomodoro technique for study sessions',
        'Take active breaks between learning blocks',
        'Review material within 24 hours'
      ],
      estimatedImpact: 6,
      timeToImplement: '1 week',
      category: 'Learning Efficiency',
      tags: ['learning', 'retention', 'spaced repetition'],
      confidence: 73,
      basedOn: ['Learning session data', 'Retention patterns']
    }
  ]);

  const [userPatterns, setUserPatterns] = useState<UserPattern[]>([
    {
      id: '1',
      pattern: 'Higher productivity on days with morning exercise',
      frequency: 85,
      impact: 'positive',
      confidence: 89
    },
    {
      id: '2',
      pattern: 'Decreased focus after heavy meals',
      frequency: 78,
      impact: 'negative',
      confidence: 82
    },
    {
      id: '3',
      pattern: 'Better sleep quality with consistent bedtime',
      frequency: 92,
      impact: 'positive',
      confidence: 95
    },
    {
      id: '4',
      pattern: 'Lower mood on days without social interaction',
      frequency: 67,
      impact: 'negative',
      confidence: 74
    }
  ]);

  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showImplemented, setShowImplemented] = useState(false);

  const handleRecommendationAction = (id: string, action: 'implement' | 'dismiss' | 'helpful' | 'not-helpful') => {
    setRecommendations(prev => prev.map(rec => {
      if (rec.id === id) {
        switch (action) {
          case 'implement':
            return { ...rec, implemented: true };
          case 'dismiss':
            return { ...rec, dismissed: true };
          case 'helpful':
          case 'not-helpful':
            return { ...rec, feedback: action };
          default:
            return rec;
        }
      }
      return rec;
    }));
  };

  const filteredRecommendations = recommendations.filter(rec => {
    if (rec.dismissed && !showImplemented) return false;
    if (selectedCategory !== 'all' && rec.type !== selectedCategory) return false;
    return true;
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'productivity': return <Zap className="w-5 h-5" />;
      case 'wellness': return <Heart className="w-5 h-5" />;
      case 'habits': return <Activity className="w-5 h-5" />;
      case 'goals': return <Target className="w-5 h-5" />;
      case 'learning': return <Book className="w-5 h-5" />;
      default: return <Lightbulb className="w-5 h-5" />;
    }
  };

  const RecommendationCard = ({ recommendation }: { recommendation: Recommendation }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, x: -100 }}
      layout
    >
      <GlassmorphCard className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            {getTypeIcon(recommendation.type)}
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">
                {recommendation.title}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {recommendation.category}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge className={getPriorityColor(recommendation.priority)}>
              {recommendation.priority}
            </Badge>
            <div className="flex items-center text-sm text-gray-500">
              <Star className="w-4 h-4 mr-1" />
              {recommendation.confidence}%
            </div>
          </div>
        </div>

        <p className="text-gray-700 dark:text-gray-300 mb-4">
          {recommendation.description}
        </p>

        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-4">
          <h4 className="font-medium text-blue-900 dark:text-blue-400 mb-2">
            Why this recommendation?
          </h4>
          <p className="text-sm text-blue-800 dark:text-blue-300">
            {recommendation.reasoning}
          </p>
        </div>

        <div className="mb-4">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">
            Action Steps:
          </h4>
          <ul className="space-y-1">
            {recommendation.actionSteps.map((step, index) => (
              <li key={index} className="flex items-start text-sm text-gray-600 dark:text-gray-400">
                <ArrowRight className="w-4 h-4 mr-2 mt-0.5 text-blue-500" />
                {step}
              </li>
            ))}
          </ul>
        </div>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center">
              <TrendingUp className="w-4 h-4 mr-1" />
              Impact: {recommendation.estimatedImpact}/10
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-1" />
              {recommendation.timeToImplement}
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-2 mb-4">
          {recommendation.tags.map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>

        <div className="text-xs text-gray-500 mb-4">
          Based on: {recommendation.basedOn.join(', ')}
        </div>

        {!recommendation.implemented && !recommendation.dismissed && (
          <div className="flex space-x-2">
            <Button
              size="sm"
              onClick={() => handleRecommendationAction(recommendation.id, 'implement')}
              className="flex items-center"
            >
              <CheckCircle className="w-4 h-4 mr-1" />
              Implement
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleRecommendationAction(recommendation.id, 'dismiss')}
              className="flex items-center"
            >
              <X className="w-4 h-4 mr-1" />
              Dismiss
            </Button>
          </div>
        )}

        {recommendation.implemented && !recommendation.feedback && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Was this helpful?</span>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleRecommendationAction(recommendation.id, 'helpful')}
            >
              <ThumbsUp className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleRecommendationAction(recommendation.id, 'not-helpful')}
            >
              <ThumbsDown className="w-4 h-4" />
            </Button>
          </div>
        )}

        {recommendation.feedback && (
          <div className="flex items-center text-sm text-gray-600">
            {recommendation.feedback === 'helpful' ? (
              <>
                <ThumbsUp className="w-4 h-4 mr-1 text-green-500" />
                Thanks for the feedback!
              </>
            ) : (
              <>
                <ThumbsDown className="w-4 h-4 mr-1 text-red-500" />
                We'll improve our recommendations
              </>
            )}
          </div>
        )}
      </GlassmorphCard>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-purple-900 dark:to-indigo-900 p-6">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            Smart Recommendations
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            AI-powered insights and suggestions based on your personal data patterns
          </p>
        </motion.div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <GlassmorphCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Recommendations</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {recommendations.filter(r => !r.dismissed && !r.implemented).length}
                </p>
              </div>
              <Lightbulb className="w-8 h-8 text-yellow-500" />
            </div>
          </GlassmorphCard>

          <GlassmorphCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Implemented</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {recommendations.filter(r => r.implemented).length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </GlassmorphCard>

          <GlassmorphCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Confidence</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {Math.round(recommendations.reduce((sum, r) => sum + r.confidence, 0) / recommendations.length)}%
                </p>
              </div>
              <Brain className="w-8 h-8 text-purple-500" />
            </div>
          </GlassmorphCard>

          <GlassmorphCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Patterns Found</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {userPatterns.length}
                </p>
              </div>
              <Sparkles className="w-8 h-8 text-blue-500" />
            </div>
          </GlassmorphCard>
        </div>

        <Tabs defaultValue="recommendations" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
            <TabsTrigger value="patterns">Patterns</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="recommendations" className="space-y-6">
            {/* Filters */}
            <div className="flex items-center space-x-4">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800 dark:border-gray-600"
              >
                <option value="all">All Categories</option>
                <option value="productivity">Productivity</option>
                <option value="wellness">Wellness</option>
                <option value="habits">Habits</option>
                <option value="goals">Goals</option>
                <option value="learning">Learning</option>
              </select>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowImplemented(!showImplemented)}
              >
                {showImplemented ? 'Hide' : 'Show'} Implemented
              </Button>
            </div>

            {/* Recommendations List */}
            <div className="space-y-6">
              <AnimatePresence>
                {filteredRecommendations.map((recommendation) => (
                  <RecommendationCard
                    key={recommendation.id}
                    recommendation={recommendation}
                  />
                ))}
              </AnimatePresence>
            </div>
          </TabsContent>

          <TabsContent value="patterns" className="space-y-6">
            <div className="space-y-4">
              {userPatterns.map((pattern) => (
                <motion.div
                  key={pattern.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  <GlassmorphCard className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                          {pattern.pattern}
                        </h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                          <span>Frequency: {pattern.frequency}%</span>
                          <span>Confidence: {pattern.confidence}%</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={
                          pattern.impact === 'positive' ? 'bg-green-100 text-green-800' :
                          pattern.impact === 'negative' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }>
                          {pattern.impact}
                        </Badge>
                      </div>
                    </div>
                    <div className="mt-4">
                      <div className="flex justify-between text-sm mb-2">
                        <span>Pattern Strength</span>
                        <span>{pattern.frequency}%</span>
                      </div>
                      <Progress value={pattern.frequency} />
                    </div>
                  </GlassmorphCard>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="insights" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <GlassmorphCard className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2" />
                    Performance Insights
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <h4 className="font-medium text-green-800 dark:text-green-400">
                        Peak Performance Window
                      </h4>
                      <p className="text-sm text-green-600 dark:text-green-300">
                        Your productivity peaks between 9-11 AM with 23% higher focus scores.
                      </p>
                    </div>
                    
                    <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <h4 className="font-medium text-blue-800 dark:text-blue-400">
                        Optimal Break Timing
                      </h4>
                      <p className="text-sm text-blue-600 dark:text-blue-300">
                        5-minute breaks every 25 minutes maintain 85% focus retention.
                      </p>
                    </div>
                    
                    <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <h4 className="font-medium text-purple-800 dark:text-purple-400">
                        Energy Correlation
                      </h4>
                      <p className="text-sm text-purple-600 dark:text-purple-300">
                        Morning exercise increases afternoon productivity by 18%.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </GlassmorphCard>

              <GlassmorphCard className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center">
                    <Brain className="w-5 h-5 mr-2" />
                    Behavioral Insights
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                      <h4 className="font-medium text-yellow-800 dark:text-yellow-400">
                        Habit Formation
                      </h4>
                      <p className="text-sm text-yellow-600 dark:text-yellow-300">
                        You form habits 40% faster when stacked with existing routines.
                      </p>
                    </div>
                    
                    <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                      <h4 className="font-medium text-red-800 dark:text-red-400">
                        Stress Triggers
                      </h4>
                      <p className="text-sm text-red-600 dark:text-red-300">
                        Irregular sleep patterns increase stress levels by 25%.
                      </p>
                    </div>
                    
                    <div className="p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
                      <h4 className="font-medium text-indigo-800 dark:text-indigo-400">
                        Goal Success Factors
                      </h4>
                      <p className="text-sm text-indigo-600 dark:text-indigo-300">
                        Weekly check-ins improve goal completion by 45%.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </GlassmorphCard>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SmartRecommendations;
