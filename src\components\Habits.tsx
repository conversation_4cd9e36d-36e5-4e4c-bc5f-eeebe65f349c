
import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus, Star, Calendar, Heart, Target, TrendingUp, Award, Flame,
  Clock, Bell, Brain, Zap, Moon, Sun, Coffee, Sunset,
  SkipForward, Settings, BarChart3, Trophy, Gift,
  MessageCircle, Palette, CheckCircle, Circle, Edit,
  Trash2, RotateCcw, Timer, MapPin, Users, Sparkles
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface HabitCompletion {
  date: string;
  completed: boolean;
  skipped: boolean;
  reflection?: string;
  mood?: 'great' | 'good' | 'okay' | 'bad';
  timeSpent?: number;
  location?: string;
}

interface Habit {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  category: 'morning' | 'evening' | 'anytime';
  frequency: 'daily' | 'weekly' | 'custom';
  customFrequency?: number; // days per week for custom
  targetTime?: number; // minutes
  reminders: {
    enabled: boolean;
    times: string[];
    sound: boolean;
  };
  skipRules: {
    graceDays: number;
    autoAdjust: boolean;
    maxSkipsPerWeek: number;
  };
  scoring: {
    basePoints: number;
    streakMultiplier: number;
    perfectWeekBonus: number;
  };
  completions: HabitCompletion[];
  streak: number;
  longestStreak: number;
  totalXP: number;
  level: number;
  createdAt: string;
  isActive: boolean;
  nightOwlMode: boolean; // Allow late check-ins
  aiOptimalTime?: string;
  milestones: {
    days: number;
    achieved: boolean;
    celebratedAt?: string;
  }[];
}

const Habits = () => {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const saved = localStorage.getItem('billionDollarDarkMode');
    return saved ? JSON.parse(saved) : false;
  });

  // Core State
  const [habits, setHabits] = useState<Habit[]>([]);
  const [showNewHabit, setShowNewHabit] = useState(false);
  const [selectedHabit, setSelectedHabit] = useState<Habit | null>(null);
  const [showHabitDetails, setShowHabitDetails] = useState(false);
  const [showReflection, setShowReflection] = useState(false);
  const [reflectionHabitId, setReflectionHabitId] = useState<string>('');

  // View State
  const [viewMode, setViewMode] = useState<'list' | 'calendar' | 'analytics'>('list');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'morning' | 'evening' | 'anytime'>('all');
  const [showCompleted, setShowCompleted] = useState(true);
  const [calendarDate, setCalendarDate] = useState(new Date());

  // Form State
  const [newHabit, setNewHabit] = useState({
    name: '',
    description: '',
    icon: '🎯',
    color: '#8B5CF6',
    category: 'anytime' as Habit['category'],
    frequency: 'daily' as Habit['frequency'],
    customFrequency: 3,
    targetTime: 30,
    reminders: {
      enabled: false,
      times: ['09:00'],
      sound: true
    },
    skipRules: {
      graceDays: 1,
      autoAdjust: true,
      maxSkipsPerWeek: 2
    },
    scoring: {
      basePoints: 10,
      streakMultiplier: 1.5,
      perfectWeekBonus: 50
    },
    nightOwlMode: false
  });

  // Reflection State
  const [reflection, setReflection] = useState({
    mood: 'good' as HabitCompletion['mood'],
    timeSpent: 30,
    location: '',
    notes: ''
  });

  useEffect(() => {
    loadHabits();
    setupNotifications();
    checkMilestones();
  }, []);

  const loadHabits = () => {
    const savedHabits = localStorage.getItem('smartHabits');
    if (savedHabits) {
      setHabits(JSON.parse(savedHabits));
    }
  };

  const saveHabits = (updatedHabits: Habit[]) => {
    setHabits(updatedHabits);
    localStorage.setItem('smartHabits', JSON.stringify(updatedHabits));
  };

  const setupNotifications = () => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  };

  const checkMilestones = () => {
    // Check for milestone achievements and trigger celebrations
    habits.forEach(habit => {
      habit.milestones.forEach(milestone => {
        if (habit.streak >= milestone.days && !milestone.achieved) {
          milestone.achieved = true;
          milestone.celebratedAt = new Date().toISOString();
          triggerCelebration(habit.name, milestone.days);
        }
      });
    });
  };

  const triggerCelebration = (habitName: string, days: number) => {
    // Show celebration animation and notification
    if (Notification.permission === 'granted') {
      new Notification(`🎉 Milestone Achieved!`, {
        body: `${habitName}: ${days} day streak!`,
        icon: '/favicon.ico'
      });
    }
  };

  // AI Pattern Detection
  const analyzeOptimalTime = useCallback((habit: Habit) => {
    const completions = habit.completions.filter(c => c.completed);
    if (completions.length < 7) return null;

    // Analyze completion times to suggest optimal time
    const timeFrequency: { [key: string]: number } = {};
    completions.forEach(completion => {
      const hour = new Date(completion.date).getHours();
      const timeSlot = hour < 12 ? 'morning' : hour < 18 ? 'afternoon' : 'evening';
      timeFrequency[timeSlot] = (timeFrequency[timeSlot] || 0) + 1;
    });

    const optimalTime = Object.keys(timeFrequency).reduce((a, b) =>
      timeFrequency[a] > timeFrequency[b] ? a : b
    );

    return optimalTime;
  }, []);

  // Habit CRUD Operations
  const addHabit = () => {
    if (!newHabit.name.trim()) return;

    const habit: Habit = {
      id: Date.now().toString(),
      name: newHabit.name,
      description: newHabit.description,
      icon: newHabit.icon,
      color: newHabit.color,
      category: newHabit.category,
      frequency: newHabit.frequency,
      customFrequency: newHabit.customFrequency,
      targetTime: newHabit.targetTime,
      reminders: newHabit.reminders,
      skipRules: newHabit.skipRules,
      scoring: newHabit.scoring,
      completions: [],
      streak: 0,
      longestStreak: 0,
      totalXP: 0,
      level: 1,
      createdAt: new Date().toISOString(),
      isActive: true,
      nightOwlMode: newHabit.nightOwlMode,
      milestones: [
        { days: 7, achieved: false },
        { days: 30, achieved: false },
        { days: 100, achieved: false },
        { days: 365, achieved: false }
      ]
    };

    const updatedHabits = [...habits, habit];
    saveHabits(updatedHabits);

    // Reset form
    setNewHabit({
      name: '',
      description: '',
      icon: '🎯',
      color: '#8B5CF6',
      category: 'anytime',
      frequency: 'daily',
      customFrequency: 3,
      targetTime: 30,
      reminders: {
        enabled: false,
        times: ['09:00'],
        sound: true
      },
      skipRules: {
        graceDays: 1,
        autoAdjust: true,
        maxSkipsPerWeek: 2
      },
      scoring: {
        basePoints: 10,
        streakMultiplier: 1.5,
        perfectWeekBonus: 50
      },
      nightOwlMode: false
    });
    setShowNewHabit(false);
  };

  const toggleHabitCompletion = (habitId: string, date: string = new Date().toISOString().split('T')[0]) => {
    const updatedHabits = habits.map(habit => {
      if (habit.id === habitId) {
        const existingCompletion = habit.completions.find(c => c.date === date);

        if (existingCompletion) {
          // Toggle completion
          const updatedCompletions = habit.completions.map(c =>
            c.date === date ? { ...c, completed: !c.completed, skipped: false } : c
          );

          const newStreak = calculateStreak(updatedCompletions);
          const xpGained = existingCompletion.completed ? -habit.scoring.basePoints :
                          habit.scoring.basePoints * (newStreak > 0 ? habit.scoring.streakMultiplier : 1);

          return {
            ...habit,
            completions: updatedCompletions,
            streak: newStreak,
            longestStreak: Math.max(habit.longestStreak, newStreak),
            totalXP: Math.max(0, habit.totalXP + xpGained),
            level: Math.floor((habit.totalXP + xpGained) / 100) + 1
          };
        } else {
          // Add new completion
          const newCompletion: HabitCompletion = {
            date,
            completed: true,
            skipped: false,
            timeSpent: habit.targetTime
          };

          const updatedCompletions = [...habit.completions, newCompletion];
          const newStreak = calculateStreak(updatedCompletions);
          const xpGained = habit.scoring.basePoints * (newStreak > 0 ? habit.scoring.streakMultiplier : 1);

          return {
            ...habit,
            completions: updatedCompletions,
            streak: newStreak,
            longestStreak: Math.max(habit.longestStreak, newStreak),
            totalXP: habit.totalXP + xpGained,
            level: Math.floor((habit.totalXP + xpGained) / 100) + 1
          };
        }
      }
      return habit;
    });

    saveHabits(updatedHabits);

    // Show reflection prompt if enabled
    const habit = habits.find(h => h.id === habitId);
    if (habit && !existingCompletion?.completed) {
      setReflectionHabitId(habitId);
      setShowReflection(true);
    }
  };

  const skipHabit = (habitId: string, date: string = new Date().toISOString().split('T')[0]) => {
    const updatedHabits = habits.map(habit => {
      if (habit.id === habitId) {
        const existingCompletion = habit.completions.find(c => c.date === date);

        if (existingCompletion) {
          const updatedCompletions = habit.completions.map(c =>
            c.date === date ? { ...c, completed: false, skipped: true } : c
          );
          return { ...habit, completions: updatedCompletions };
        } else {
          const newCompletion: HabitCompletion = {
            date,
            completed: false,
            skipped: true
          };
          return { ...habit, completions: [...habit.completions, newCompletion] };
        }
      }
      return habit;
    });

    saveHabits(updatedHabits);
  };

  // Analytics and Helper Functions
  const calculateStreak = (completions: HabitCompletion[]) => {
    if (completions.length === 0) return 0;

    const completedDates = completions
      .filter(c => c.completed)
      .map(c => new Date(c.date))
      .sort((a, b) => b.getTime() - a.getTime());

    if (completedDates.length === 0) return 0;

    let streak = 0;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Check if completed today or yesterday (for night owl mode)
    const latestCompletion = completedDates[0];
    latestCompletion.setHours(0, 0, 0, 0);

    const daysDiff = Math.floor((today.getTime() - latestCompletion.getTime()) / (1000 * 60 * 60 * 24));

    if (daysDiff > 1) return 0; // Streak broken

    // Calculate consecutive days
    let currentDate = new Date(latestCompletion);
    for (const completionDate of completedDates) {
      completionDate.setHours(0, 0, 0, 0);
      if (completionDate.getTime() === currentDate.getTime()) {
        streak++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else if (completionDate.getTime() < currentDate.getTime()) {
        break;
      }
    }

    return streak;
  };

  const getConsistencyPercentage = (habit: Habit, days: number = 30) => {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const completionsInPeriod = habit.completions.filter(c => {
      const date = new Date(c.date);
      return date >= startDate && date <= endDate && c.completed;
    });

    return Math.round((completionsInPeriod.length / days) * 100);
  };

  const getHabitStats = () => {
    const totalHabits = habits.filter(h => h.isActive).length;
    const completedToday = habits.filter(h => isCompletedToday(h)).length;
    const totalXP = habits.reduce((sum, h) => sum + h.totalXP, 0);
    const averageStreak = habits.length > 0 ?
      Math.round(habits.reduce((sum, h) => sum + h.streak, 0) / habits.length) : 0;
    const longestStreak = Math.max(...habits.map(h => h.longestStreak), 0);
    const perfectDays = getPerfectDaysCount();

    return {
      totalHabits,
      completedToday,
      totalXP,
      averageStreak,
      longestStreak,
      perfectDays,
      completionRate: totalHabits > 0 ? Math.round((completedToday / totalHabits) * 100) : 0
    };
  };

  const getPerfectDaysCount = () => {
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    });

    return last30Days.filter(date => {
      const activeHabitsOnDate = habits.filter(h => new Date(h.createdAt) <= new Date(date));
      if (activeHabitsOnDate.length === 0) return false;

      const completedOnDate = activeHabitsOnDate.filter(h =>
        h.completions.some(c => c.date === date && c.completed)
      );

      return completedOnDate.length === activeHabitsOnDate.length;
    }).length;
  };

  const isCompletedToday = (habit: Habit) => {
    const today = new Date().toISOString().split('T')[0];
    return habit.completions.some(c => c.date === today && c.completed);
  };

  const isSkippedToday = (habit: Habit) => {
    const today = new Date().toISOString().split('T')[0];
    return habit.completions.some(c => c.date === today && c.skipped);
  };

  const canCompleteHabit = (habit: Habit) => {
    if (habit.nightOwlMode) return true;

    const now = new Date();
    const currentHour = now.getHours();

    if (habit.category === 'morning' && currentHour > 12) return false;
    if (habit.category === 'evening' && currentHour < 18) return false;

    return true;
  };

  const getFilteredHabits = () => {
    return habits.filter(habit => {
      if (!habit.isActive) return false;
      if (selectedCategory !== 'all' && habit.category !== selectedCategory) return false;
      if (!showCompleted && isCompletedToday(habit)) return false;
      return true;
    });
  };

  const saveReflection = () => {
    const updatedHabits = habits.map(habit => {
      if (habit.id === reflectionHabitId) {
        const today = new Date().toISOString().split('T')[0];
        const updatedCompletions = habit.completions.map(c =>
          c.date === today ? {
            ...c,
            mood: reflection.mood,
            timeSpent: reflection.timeSpent,
            location: reflection.location,
            reflection: reflection.notes
          } : c
        );
        return { ...habit, completions: updatedCompletions };
      }
      return habit;
    });

    saveHabits(updatedHabits);
    setShowReflection(false);
    setReflection({
      mood: 'good',
      timeSpent: 30,
      location: '',
      notes: ''
    });
  };

  // Constants and Data
  const stats = getHabitStats();
  const filteredHabits = getFilteredHabits();

  const habitIcons = [
    '🎯', '💪', '📚', '🧘', '🏃', '💧', '🥗', '😴', '🎨', '🎵',
    '✍️', '🌱', '🧠', '❤️', '🔥', '⭐', '🌟', '💎', '🚀', '🎪'
  ];

  const habitColors = [
    '#8B5CF6', '#EC4899', '#10B981', '#F59E0B', '#EF4444', '#3B82F6',
    '#8B5A00', '#06B6D4', '#84CC16', '#F97316', '#EF4444', '#8B5CF6'
  ];

  const categoryIcons = {
    morning: <Sun className="w-4 h-4" />,
    evening: <Moon className="w-4 h-4" />,
    anytime: <Clock className="w-4 h-4" />
  };

  const moodEmojis = {
    great: '😄',
    good: '😊',
    okay: '😐',
    bad: '😞'
  };

  return (
    <div className={`min-h-screen transition-all duration-700 ${
      isDarkMode
        ? 'bg-gradient-to-br from-slate-900 via-purple-900/30 to-slate-900'
        : 'bg-gradient-to-br from-purple-50 via-white to-pink-50'
    }`}>
      {/* Floating particles */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full opacity-20"
            animate={{
              y: [-20, -100],
              x: [0, Math.random() * 100 - 50],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: '100%',
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-4 pb-32">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-4 sm:mb-6"
        >
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 bg-clip-text text-transparent">
              🧠 Smart Habit Tracker
            </h1>
            <p className={`text-sm sm:text-base ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              AI-powered habit building with streaks, analytics & celebrations
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-2 flex-shrink-0">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={() => setViewMode(viewMode === 'list' ? 'analytics' : 'list')}
                variant="outline"
                className={`w-full sm:w-auto rounded-2xl backdrop-blur-xl border-2 text-sm ${
                  isDarkMode
                    ? 'bg-gray-800/50 border-gray-600 text-gray-300 hover:bg-gray-700/50'
                    : 'bg-white/50 border-gray-200 hover:bg-white/80'
                }`}
                size="sm"
              >
                <TrendingUp className="w-4 h-4 mr-2" />
                {viewMode === 'list' ? 'Analytics' : 'Habits'}
              </Button>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={() => setShowNewHabit(true)}
                className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 rounded-2xl shadow-lg text-sm"
                size="sm"
              >
                <Plus className="w-4 h-4 mr-2" />
                New Habit
              </Button>
            </motion.div>
          </div>
        </motion.div>

        {/* Controls Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex flex-col lg:flex-row gap-4 mb-6"
        >
          {/* Category Filters */}
          <div className="flex gap-2 flex-wrap">
            {(['all', 'morning', 'evening', 'anytime'] as const).map(category => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="rounded-2xl text-xs"
              >
                {category === 'all' ? <Target className="w-3 h-3 mr-1" /> : categoryIcons[category]}
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Button>
            ))}
          </div>

          {/* View Toggle */}
          <div className="flex gap-2 ml-auto">
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-2xl"
            >
              <Target className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'calendar' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('calendar')}
              className="rounded-2xl"
            >
              <Calendar className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'analytics' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('analytics')}
              className="rounded-2xl"
            >
              <BarChart3 className="w-4 h-4" />
            </Button>
          </div>
        </motion.div>

        {/* Enhanced Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-2 lg:grid-cols-6 gap-3 sm:gap-4 mb-6"
        >
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-purple-100 to-indigo-100 border-0 shadow-lg hover:shadow-xl transition-all">
            <Target className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-purple-600" />
            <div className="text-xl sm:text-2xl font-bold text-purple-700">{stats.totalHabits}</div>
            <div className="text-xs sm:text-sm text-purple-600">Active Habits</div>
          </Card>
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-green-100 to-emerald-100 border-0 shadow-lg hover:shadow-xl transition-all">
            <CheckCircle className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-green-600" />
            <div className="text-xl sm:text-2xl font-bold text-green-700">{stats.completedToday}</div>
            <div className="text-xs sm:text-sm text-green-600">Today</div>
          </Card>
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-yellow-100 to-orange-100 border-0 shadow-lg hover:shadow-xl transition-all">
            <Flame className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-orange-600" />
            <div className="text-xl sm:text-2xl font-bold text-orange-700">{stats.longestStreak}</div>
            <div className="text-xs sm:text-sm text-orange-600">Best Streak</div>
          </Card>
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-blue-100 to-cyan-100 border-0 shadow-lg hover:shadow-xl transition-all">
            <Zap className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-blue-600" />
            <div className="text-xl sm:text-2xl font-bold text-blue-700">{stats.totalXP}</div>
            <div className="text-xs sm:text-sm text-blue-600">Total XP</div>
          </Card>
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-pink-100 to-rose-100 border-0 shadow-lg hover:shadow-xl transition-all">
            <Trophy className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-pink-600" />
            <div className="text-xl sm:text-2xl font-bold text-pink-700">{stats.perfectDays}</div>
            <div className="text-xs sm:text-sm text-pink-600">Perfect Days</div>
          </Card>
          <Card className="text-center p-3 sm:p-4 bg-gradient-to-br from-indigo-100 to-purple-100 border-0 shadow-lg hover:shadow-xl transition-all">
            <Award className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-indigo-600" />
            <div className="text-xl sm:text-2xl font-bold text-indigo-700">{stats.completionRate}%</div>
            <div className="text-xs sm:text-sm text-indigo-600">Completion</div>
          </Card>
        </motion.div>

        {/* Enhanced Habit List with Calendar View */}
        {viewMode === 'calendar' ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-2xl shadow-lg p-6"
          >
            <div className="text-center mb-4">
              <h3 className="text-lg font-semibold">Habit Calendar</h3>
              <p className="text-gray-600">Visual streak tracking</p>
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-2 mb-4">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                <div key={day} className="text-center text-sm font-medium text-gray-500 p-2">
                  {day}
                </div>
              ))}

              {/* Generate calendar days for current month */}
              {Array.from({ length: 35 }, (_, i) => {
                const date = new Date(calendarDate.getFullYear(), calendarDate.getMonth(), i - 6);
                const dateStr = date.toISOString().split('T')[0];
                const isCurrentMonth = date.getMonth() === calendarDate.getMonth();
                const isToday = dateStr === new Date().toISOString().split('T')[0];

                // Check if any habits were completed on this date
                const completedHabits = habits.filter(habit =>
                  habit.completions.some(c => c.date === dateStr && c.completed)
                ).length;

                const totalActiveHabits = habits.filter(habit =>
                  new Date(habit.createdAt) <= date && habit.isActive
                ).length;

                const completionRate = totalActiveHabits > 0 ? completedHabits / totalActiveHabits : 0;

                return (
                  <div
                    key={i}
                    className={`aspect-square flex items-center justify-center text-sm rounded-lg transition-all ${
                      isCurrentMonth
                        ? isToday
                          ? 'bg-blue-500 text-white font-bold'
                          : completionRate === 1
                            ? 'bg-green-100 text-green-800 font-medium'
                            : completionRate > 0.5
                              ? 'bg-yellow-100 text-yellow-800'
                              : completionRate > 0
                                ? 'bg-orange-100 text-orange-800'
                                : 'bg-gray-50 text-gray-400'
                        : 'text-gray-300'
                    }`}
                  >
                    {date.getDate()}
                    {completionRate > 0 && isCurrentMonth && (
                      <div className="absolute mt-4 w-1 h-1 rounded-full bg-current opacity-60" />
                    )}
                  </div>
                );
              })}
            </div>

            {/* Legend */}
            <div className="flex justify-center gap-4 text-xs">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-green-100 rounded"></div>
                <span>Perfect Day</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-yellow-100 rounded"></div>
                <span>Good Day</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-orange-100 rounded"></div>
                <span>Some Progress</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-gray-100 rounded"></div>
                <span>No Activity</span>
              </div>
            </div>
          </motion.div>
        ) : viewMode === 'analytics' ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            {/* Analytics Dashboard */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {habits.map(habit => (
                <Card key={habit.id} className="border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <span className="text-lg">{habit.icon}</span>
                      {habit.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Consistency Chart */}
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>30-Day Consistency</span>
                          <span className="font-medium">{getConsistencyPercentage(habit)}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="h-2 rounded-full transition-all duration-300"
                            style={{
                              width: `${getConsistencyPercentage(habit)}%`,
                              backgroundColor: habit.color
                            }}
                          />
                        </div>
                      </div>

                      {/* Stats Grid */}
                      <div className="grid grid-cols-2 gap-4 text-center">
                        <div>
                          <div className="text-2xl font-bold" style={{ color: habit.color }}>
                            {habit.streak}
                          </div>
                          <div className="text-xs text-gray-600">Current Streak</div>
                        </div>
                        <div>
                          <div className="text-2xl font-bold" style={{ color: habit.color }}>
                            {habit.longestStreak}
                          </div>
                          <div className="text-xs text-gray-600">Best Streak</div>
                        </div>
                        <div>
                          <div className="text-2xl font-bold" style={{ color: habit.color }}>
                            {habit.totalXP}
                          </div>
                          <div className="text-xs text-gray-600">Total XP</div>
                        </div>
                        <div>
                          <div className="text-2xl font-bold" style={{ color: habit.color }}>
                            {habit.level}
                          </div>
                          <div className="text-xs text-gray-600">Level</div>
                        </div>
                      </div>

                      {/* AI Insights */}
                      <div className="bg-purple-50 rounded-lg p-3">
                        <div className="flex items-center gap-2 mb-2">
                          <Brain className="w-4 h-4 text-purple-600" />
                          <span className="text-sm font-medium text-purple-800">AI Insights</span>
                        </div>
                        <p className="text-xs text-purple-700">
                          {habit.completions.length > 7
                            ? `Best time: ${analyzeOptimalTime(habit) || 'Coming Soon'} • Keep up the great work!`
                            : 'Complete more entries for personalized insights!'
                          }
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>
        ) : (
          <div className="space-y-3 sm:space-y-4">
            <AnimatePresence>
              {filteredHabits.map(habit => (
                <motion.div
                  key={habit.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="group"
                >
                  <Card className={`border-0 shadow-lg hover:shadow-xl transition-all duration-300 ${
                    isDarkMode ? 'bg-gray-800/80' : 'bg-white/80'
                  } backdrop-blur-sm rounded-2xl overflow-hidden`}
                  style={{ borderLeft: `4px solid ${habit.color}` }}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 flex-1">
                          {/* Habit Icon */}
                          <div
                            className="w-12 h-12 rounded-2xl flex items-center justify-center text-xl shadow-lg cursor-pointer"
                            style={{ backgroundColor: `${habit.color}20` }}
                            onClick={() => {
                              setSelectedHabit(habit);
                              setShowHabitDetails(true);
                            }}
                          >
                            {habit.icon}
                          </div>

                          <div className="flex-1 min-w-0">
                            {/* Habit Header */}
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className={`font-medium text-sm sm:text-base ${
                                isDarkMode ? 'text-white' : 'text-gray-800'
                              }`}>
                                {habit.name}
                              </h3>
                              {categoryIcons[habit.category]}
                              <Badge className="text-xs" style={{ backgroundColor: `${habit.color}20`, color: habit.color }}>
                                Level {habit.level}
                              </Badge>
                              {habit.nightOwlMode && (
                                <Badge variant="outline" className="text-xs">
                                  <Moon className="w-3 h-3 mr-1" />
                                  Night Owl
                                </Badge>
                              )}
                            </div>

                            {/* Habit Description */}
                            {habit.description && (
                              <p className={`text-xs sm:text-sm mb-2 ${
                                isDarkMode ? 'text-gray-400' : 'text-gray-600'
                              }`}>
                                {habit.description}
                              </p>
                            )}

                            {/* Streak and XP */}
                            <div className="flex items-center gap-4 mb-2">
                              <div className="flex items-center gap-1">
                                <Flame className="w-4 h-4 text-orange-500" />
                                <span className="text-sm font-medium">{habit.streak} day streak</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Zap className="w-4 h-4 text-blue-500" />
                                <span className="text-sm">{habit.totalXP} XP</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <BarChart3 className="w-4 h-4 text-green-500" />
                                <span className="text-sm">{getConsistencyPercentage(habit)}%</span>
                              </div>
                            </div>

                            {/* Progress Bar */}
                            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                              <div
                                className="h-2 rounded-full transition-all duration-300"
                                style={{
                                  width: `${getConsistencyPercentage(habit)}%`,
                                  backgroundColor: habit.color
                                }}
                              />
                            </div>

                            {/* Milestones */}
                            <div className="flex gap-1">
                              {habit.milestones.map(milestone => (
                                <motion.div
                                  key={milestone.days}
                                  className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
                                    milestone.achieved
                                      ? 'bg-yellow-100 text-yellow-800'
                                      : 'bg-gray-100 text-gray-400'
                                  }`}
                                  whileHover={{ scale: 1.1 }}
                                  animate={milestone.achieved ? {
                                    scale: [1, 1.2, 1],
                                    rotate: [0, 10, -10, 0]
                                  } : {}}
                                >
                                  {milestone.achieved ? '🏆' : milestone.days}
                                </motion.div>
                              ))}
                            </div>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center space-x-2">
                          {/* Skip Button */}
                          <Button
                            onClick={() => skipHabit(habit.id)}
                            variant="ghost"
                            size="sm"
                            className="text-yellow-600 hover:bg-yellow-50"
                            disabled={isSkippedToday(habit)}
                          >
                            <SkipForward className="w-4 h-4" />
                          </Button>

                          {/* Complete Button */}
                          <Button
                            onClick={() => toggleHabitCompletion(habit.id)}
                            variant="ghost"
                            size="sm"
                            className={`${
                              isCompletedToday(habit)
                                ? 'text-green-600 bg-green-50'
                                : canCompleteHabit(habit)
                                ? 'text-gray-600 hover:bg-gray-50'
                                : 'text-gray-300 cursor-not-allowed'
                            }`}
                            disabled={!canCompleteHabit(habit) && !isCompletedToday(habit)}
                          >
                            {isCompletedToday(habit) ? (
                              <CheckCircle className="w-5 h-5" />
                            ) : (
                              <Circle className="w-5 h-5" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}

      {/* New Habit Form */}
      <AnimatePresence>
        {showNewHabit && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-6"
          >
            <Card className={`shadow-xl border-0 backdrop-blur-xl rounded-2xl ${
              isDarkMode ? 'bg-gray-800/80' : 'bg-white/80'
            }`}>
              <CardHeader>
                <CardTitle className={`${isDarkMode ? 'text-white' : 'text-gray-800'}`}>New Habit</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  placeholder="Habit name..."
                  value={newHabit.name}
                  onChange={(e) => setNewHabit({ ...newHabit, name: e.target.value })}
                  className="rounded-xl border-2 border-gray-200 focus:border-purple-500"
                />

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <select
                    value={newHabit.frequency}
                    onChange={(e) => setNewHabit({ ...newHabit, frequency: e.target.value })}
                    className="px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:outline-none"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                  </select>

                  <div className="flex flex-wrap gap-2 justify-center sm:justify-start">
                    {habitColors.map(color => (
                      <button
                        key={color}
                        onClick={() => setNewHabit({ ...newHabit, color })}
                        className={`w-8 h-8 rounded-full border-2 transition-all ${
                          newHabit.color === color ? 'border-gray-400 scale-110' : 'border-gray-200 hover:scale-105'
                        }`}
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-2 sm:gap-2">
                  <Button
                    onClick={addHabit}
                    className="w-full sm:flex-1 bg-purple-600 hover:bg-purple-700 rounded-xl"
                  >
                    Add Habit
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowNewHabit(false)}
                    className="w-full sm:flex-1 rounded-xl"
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Analytics View */}
      {viewMode === 'analytics' && (
        <div className="space-y-6">
          {/* Habit Performance */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6"
          >
            <Card className={`shadow-xl border-0 backdrop-blur-xl ${
              isDarkMode ? 'bg-purple-900/40 border-purple-500/20' : 'bg-white/80'
            } rounded-2xl`}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Flame className="w-6 h-6 text-orange-600 mr-2" />
                  Streak Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {habits.map(habit => (
                    <div key={habit.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: habit.color }}
                        />
                        <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          {habit.name}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-orange-600 font-bold">{habit.streak || 0}</span>
                        <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>days</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className={`shadow-xl border-0 backdrop-blur-xl ${
              isDarkMode ? 'bg-green-900/40 border-green-500/20' : 'bg-white/80'
            } rounded-2xl`}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="w-6 h-6 text-green-600 mr-2" />
                  Achievements
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { title: "First Week", description: "Complete 7 days in a row", achieved: habits.some(h => h.streak >= 7) },
                    { title: "Consistency King", description: "Complete 30 days in a row", achieved: habits.some(h => h.streak >= 30) },
                    { title: "Habit Master", description: "Maintain 5 active habits", achieved: habits.length >= 5 },
                    { title: "Perfect Day", description: "Complete all habits in one day", achieved: habits.length > 0 && habits.every(h => isCompletedToday(h)) }
                  ].map((achievement, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        achievement.achieved
                          ? 'bg-green-500 text-white'
                          : isDarkMode ? 'bg-gray-700 text-gray-400' : 'bg-gray-200 text-gray-500'
                      }`}>
                        {achievement.achieved ? '✓' : '○'}
                      </div>
                      <div>
                        <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                          {achievement.title}
                        </h4>
                        <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {achievement.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Weekly Overview */}
          <Card className={`shadow-xl border-0 backdrop-blur-xl ${
            isDarkMode ? 'bg-blue-900/40 border-blue-500/20' : 'bg-white/80'
          } rounded-2xl`}>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-6 h-6 text-blue-600 mr-2" />
                Weekly Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {habits.map(habit => {
                  const weekProgress = getWeekProgress(habit);
                  const weeklyCompletion = (weekProgress.filter(Boolean).length / 7) * 100;

                  return (
                    <div key={habit.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: habit.color }}
                          />
                          <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                            {habit.name}
                          </span>
                        </div>
                        <span className={`text-sm font-bold ${
                          weeklyCompletion >= 80 ? 'text-green-600' :
                          weeklyCompletion >= 50 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {Math.round(weeklyCompletion)}%
                        </span>
                      </div>
                      <div className="flex space-x-1">
                        {weekProgress.map((completed, index) => (
                          <div
                            key={index}
                            className={`flex-1 h-3 rounded-full ${
                              completed ? 'bg-green-400' : isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Habits List */}
      {viewMode === 'list' && (
        <div className="space-y-4">
        <AnimatePresence>
          {habits.map((habit) => {
            const weekProgress = getWeekProgress(habit);
            const completedToday = isCompletedToday(habit);
            
            return (
              <motion.div
                key={habit.id}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div 
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: habit.color }}
                        />
                        <h3 className="font-medium text-gray-800">{habit.name}</h3>
                      </div>
                      
                      <Button
                        onClick={() => toggleHabitToday(habit.id)}
                        variant={completedToday ? "default" : "outline"}
                        size="sm"
                        className={`rounded-full ${
                          completedToday 
                            ? 'bg-green-600 hover:bg-green-700 text-white' 
                            : 'hover:bg-green-50 border-green-300'
                        }`}
                      >
                        <Checkbox checked={completedToday} className="hidden" />
                        {completedToday ? '✓' : '+'}
                      </Button>
                    </div>

                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <Star className="w-4 h-4 text-yellow-500" />
                          <span className="text-sm font-medium">{habit.streak || 0} day streak</span>
                        </div>
                      </div>
                    </div>

                    {/* Week Progress */}
                    <div className="flex space-x-1">
                      {weekProgress.map((completed, index) => (
                        <div
                          key={index}
                          className={`flex-1 h-2 rounded-full ${
                            completed ? 'bg-green-400' : 'bg-gray-200'
                          }`}
                        />
                      ))}
                    </div>
                    
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>Sun</span>
                      <span>Mon</span>
                      <span>Tue</span>
                      <span>Wed</span>
                      <span>Thu</span>
                      <span>Fri</span>
                      <span>Sat</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </AnimatePresence>
        </div>
      )}

      {habits.length === 0 && (
        <div className="text-center py-12">
          <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-500 mb-2">No habits yet</h3>
          <p className="text-gray-400 mb-4">Start building positive routines!</p>
          <Button
            onClick={() => setShowNewHabit(true)}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Habit
          </Button>
        </div>
      )}
      </div>
    </div>
  );
};

export default Habits;
