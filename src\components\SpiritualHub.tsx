import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Lotus, 
  BookOpen, Clock, Target, TrendingUp, Calendar,
  Play, Pause, RotateCcw, Volume2, VolumeX, Search,
  Plus, Check, X, ChevronRight, Award, Crown, Zap
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface SpiritualHabit {
  id: string;
  name: string;
  description: string;
  icon: string;
  streak: number;
  completedToday: boolean;
  targetDays: number;
  category: 'meditation' | 'prayer' | 'reading' | 'service' | 'mindfulness';
}

interface GitaVerse {
  id: number;
  verse_number: number;
  chapter_number: number;
  slug: string;
  text: string;
  transliteration: string;
  word_meanings: string;
  translations: Array<{
    id: number;
    description: string;
    author_name: string;
    language: string;
  }>;
}

const SpiritualHub = () => {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const saved = localStorage.getItem('billionDollarDarkMode');
    return saved ? JSON.parse(saved) : false;
  });

  const [habits, setHabits] = useState<SpiritualHabit[]>([]);
  const [currentVerse, setCurrentVerse] = useState<GitaVerse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [meditationTimer, setMeditationTimer] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [selectedDuration, setSelectedDuration] = useState(10);
  const [showAddHabit, setShowAddHabit] = useState(false);
  const [searchChapter, setSearchChapter] = useState('1');
  const [searchVerse, setSearchVerse] = useState('1');
  const [dailyReflection, setDailyReflection] = useState('');
  const [gratitudeList, setGratitudeList] = useState<string[]>([]);
  const [newGratitude, setNewGratitude] = useState('');

  const [newHabit, setNewHabit] = useState({
    name: '',
    description: '',
    category: 'meditation' as SpiritualHabit['category'],
    targetDays: 21
  });

  const spiritualCategories = {
    meditation: { icon: '🧘', color: 'from-purple-500 to-indigo-600', name: 'Meditation' },
    prayer: { icon: '🙏', color: 'from-blue-500 to-cyan-600', name: 'Prayer' },
    reading: { icon: '📖', color: 'from-amber-500 to-orange-600', name: 'Reading' },
    service: { icon: '🤝', color: 'from-green-500 to-emerald-600', name: 'Service' },
    mindfulness: { icon: '🌸', color: 'from-pink-500 to-rose-600', name: 'Mindfulness' }
  };

  useEffect(() => {
    loadSpiritualData();
    fetchDailyVerse();
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTimerRunning && meditationTimer > 0) {
      interval = setInterval(() => {
        setMeditationTimer(prev => {
          if (prev <= 1) {
            setIsTimerRunning(false);
            // Play completion sound or notification
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning, meditationTimer]);

  const loadSpiritualData = () => {
    const savedHabits = localStorage.getItem('spiritualHabits');
    const savedGratitude = localStorage.getItem('gratitudeList');
    const savedReflection = localStorage.getItem('dailyReflection');
    
    if (savedHabits) setHabits(JSON.parse(savedHabits));
    if (savedGratitude) setGratitudeList(JSON.parse(savedGratitude));
    if (savedReflection) setDailyReflection(savedReflection);
  };

  const fetchDailyVerse = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`https://bhagavad-gita3.p.rapidapi.com/v2/chapters/${searchChapter}/verses/${searchVerse}/`, {
        method: 'GET',
        headers: {
          'x-rapidapi-key': '**************************************************',
          'x-rapidapi-host': 'bhagavad-gita3.p.rapidapi.com'
        }
      });
      
      if (response.ok) {
        const verse = await response.json();
        setCurrentVerse(verse);
      }
    } catch (error) {
      console.error('Error fetching verse:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleHabit = (habitId: string) => {
    const updatedHabits = habits.map(habit => {
      if (habit.id === habitId) {
        const newCompleted = !habit.completedToday;
        return {
          ...habit,
          completedToday: newCompleted,
          streak: newCompleted ? habit.streak + 1 : Math.max(0, habit.streak - 1)
        };
      }
      return habit;
    });
    setHabits(updatedHabits);
    localStorage.setItem('spiritualHabits', JSON.stringify(updatedHabits));
  };

  const addHabit = () => {
    if (!newHabit.name.trim()) return;
    
    const habit: SpiritualHabit = {
      id: Date.now().toString(),
      ...newHabit,
      streak: 0,
      completedToday: false,
      icon: spiritualCategories[newHabit.category].icon
    };
    
    const updatedHabits = [...habits, habit];
    setHabits(updatedHabits);
    localStorage.setItem('spiritualHabits', JSON.stringify(updatedHabits));
    setNewHabit({ name: '', description: '', category: 'meditation', targetDays: 21 });
    setShowAddHabit(false);
  };

  const startMeditation = (minutes: number) => {
    setMeditationTimer(minutes * 60);
    setSelectedDuration(minutes);
    setIsTimerRunning(true);
  };

  const addGratitude = () => {
    if (!newGratitude.trim()) return;
    const updated = [...gratitudeList, newGratitude];
    setGratitudeList(updated);
    localStorage.setItem('gratitudeList', JSON.stringify(updated));
    setNewGratitude('');
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`min-h-screen transition-all duration-700 ${
      isDarkMode 
        ? 'bg-gradient-to-br from-slate-900 via-purple-900/30 to-slate-900' 
        : 'bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50'
    }`}>
      {/* Floating spiritual particles */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full opacity-30"
            animate={{
              y: [-20, -100],
              x: [0, Math.random() * 100 - 50],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: '100%',
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-4 pb-32">
        {/* Header - Mobile Responsive */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-4 sm:mb-6"
        >
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-orange-600 via-yellow-600 to-red-600 bg-clip-text text-transparent">
              🕉️ Spiritual Journey
            </h1>
            <p className={`text-sm sm:text-base ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Nurture your soul and find inner peace
            </p>
          </div>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="w-10 h-10 sm:w-12 sm:h-12 rounded-2xl bg-gradient-to-r from-orange-500 to-yellow-500 flex items-center justify-center shadow-lg flex-shrink-0"
          >
            <Sparkles className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </motion.div>
        </motion.div>

        {/* Daily Verse */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-6"
        >
          <Card className={`border-0 shadow-2xl backdrop-blur-xl ${
            isDarkMode 
              ? 'bg-gradient-to-r from-orange-900/40 to-yellow-900/40 border-orange-500/20' 
              : 'bg-gradient-to-r from-orange-100/80 to-yellow-100/80'
          } relative overflow-hidden`}>
            {/* Glowing effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-yellow-500/10 animate-pulse" />
            
            <CardHeader className="relative">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <BookOpen className="w-6 h-6 text-orange-600 mr-2" />
                  <span className={`${isDarkMode ? 'text-orange-300' : 'text-orange-800'}`}>
                    Bhagavad Gita - Daily Verse
                  </span>
                </div>
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-2">
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder="Ch"
                      value={searchChapter}
                      onChange={(e) => setSearchChapter(e.target.value)}
                      className="w-14 sm:w-16 h-8 text-xs rounded-lg"
                      min="1"
                      max="18"
                    />
                    <Input
                      type="number"
                      placeholder="Vs"
                      value={searchVerse}
                      onChange={(e) => setSearchVerse(e.target.value)}
                      className="w-14 sm:w-16 h-8 text-xs rounded-lg"
                      min="1"
                    />
                  </div>
                  <Button
                    onClick={fetchDailyVerse}
                    size="sm"
                    className="h-8 bg-orange-600 hover:bg-orange-700 rounded-lg w-full sm:w-auto"
                    disabled={isLoading}
                  >
                    <Search className="w-3 h-3 mr-1 sm:mr-0" />
                    <span className="sm:hidden">Search Verse</span>
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="relative">
              {isLoading ? (
                <div className="text-center py-8">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full mx-auto"
                  />
                  <p className={`mt-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Loading divine wisdom...
                  </p>
                </div>
              ) : currentVerse ? (
                <div className="space-y-4">
                  <div className={`text-lg font-medium ${isDarkMode ? 'text-orange-200' : 'text-orange-900'}`}>
                    Chapter {currentVerse.chapter_number}, Verse {currentVerse.verse_number}
                  </div>
                  <div className={`text-xl font-sanskrit leading-relaxed ${isDarkMode ? 'text-yellow-200' : 'text-amber-800'}`}>
                    {currentVerse.text}
                  </div>
                  <div className={`italic ${isDarkMode ? 'text-orange-300' : 'text-orange-700'}`}>
                    {currentVerse.transliteration}
                  </div>
                  {currentVerse.translations && currentVerse.translations[0] && (
                    <div className={`${isDarkMode ? 'text-gray-300' : 'text-gray-700'} leading-relaxed`}>
                      {currentVerse.translations[0].description}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Click search to load a verse
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Meditation Timer */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="mb-6"
        >
          <Card className={`border-0 shadow-xl backdrop-blur-xl ${
            isDarkMode
              ? 'bg-purple-900/40 border-purple-500/20'
              : 'bg-purple-100/80'
          } relative overflow-hidden`}>
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-indigo-500/10 animate-pulse" />

            <CardHeader className="relative">
              <CardTitle className="flex items-center">
                <motion.div
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  🧘
                </motion.div>
                <span className={`ml-2 ${isDarkMode ? 'text-purple-300' : 'text-purple-800'}`}>
                  Meditation Timer
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent className="relative">
              <div className="text-center">
                <motion.div
                  className={`text-6xl font-mono mb-4 ${isDarkMode ? 'text-purple-200' : 'text-purple-900'}`}
                  animate={{ scale: isTimerRunning ? [1, 1.02, 1] : 1 }}
                  transition={{ duration: 1, repeat: isTimerRunning ? Infinity : 0 }}
                >
                  {formatTime(meditationTimer)}
                </motion.div>

                <div className="flex flex-wrap justify-center gap-2 mb-4">
                  {[5, 10, 15, 20, 30].map(duration => (
                    <Button
                      key={duration}
                      onClick={() => startMeditation(duration)}
                      variant={selectedDuration === duration ? 'default' : 'outline'}
                      size="sm"
                      className="rounded-2xl text-xs sm:text-sm"
                      disabled={isTimerRunning}
                    >
                      {duration}m
                    </Button>
                  ))}
                </div>

                <div className="flex flex-col sm:flex-row justify-center gap-2 sm:gap-4">
                  <Button
                    onClick={() => setIsTimerRunning(!isTimerRunning)}
                    className="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-2xl w-full sm:w-auto"
                    disabled={meditationTimer === 0}
                  >
                    {isTimerRunning ? <Pause className="w-4 h-4 mr-2" /> : <Play className="w-4 h-4 mr-2" />}
                    {isTimerRunning ? 'Pause' : 'Start'}
                  </Button>
                  <Button
                    onClick={() => {
                      setIsTimerRunning(false);
                      setMeditationTimer(0);
                    }}
                    variant="outline"
                    className="rounded-2xl w-full sm:w-auto"
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Reset
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Spiritual Habits */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="mb-6"
        >
          <Card className={`border-0 shadow-xl backdrop-blur-xl ${
            isDarkMode
              ? 'bg-green-900/40 border-green-500/20'
              : 'bg-green-100/80'
          }`}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Target className="w-6 h-6 text-green-600 mr-2" />
                  <span className={`${isDarkMode ? 'text-green-300' : 'text-green-800'}`}>
                    Spiritual Habits
                  </span>
                </div>
                <Button
                  onClick={() => setShowAddHabit(true)}
                  size="sm"
                  className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4">
                {habits.map(habit => (
                  <motion.div
                    key={habit.id}
                    whileHover={{ scale: 1.02 }}
                    className={`p-4 rounded-2xl border ${
                      habit.completedToday
                        ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-500/30'
                        : isDarkMode
                          ? 'bg-gray-800/50 border-gray-700'
                          : 'bg-white/50 border-gray-200'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <span className="text-2xl mr-2">{habit.icon}</span>
                        <div>
                          <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                            {habit.name}
                          </h4>
                          <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {habit.description}
                          </p>
                        </div>
                      </div>
                      <Button
                        onClick={() => toggleHabit(habit.id)}
                        size="sm"
                        variant={habit.completedToday ? 'default' : 'outline'}
                        className="rounded-full w-8 h-8 p-0"
                      >
                        {habit.completedToday ? <Check className="w-4 h-4" /> : <Plus className="w-4 h-4" />}
                      </Button>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        🔥 {habit.streak} day streak
                      </span>
                      <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        Goal: {habit.targetDays} days
                      </span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Gratitude Journal */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <Card className={`border-0 shadow-xl backdrop-blur-xl ${
            isDarkMode
              ? 'bg-pink-900/40 border-pink-500/20'
              : 'bg-pink-100/80'
          }`}>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Heart className="w-6 h-6 text-pink-600 mr-2" />
                <span className={`${isDarkMode ? 'text-pink-300' : 'text-pink-800'}`}>
                  Gratitude Journal
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-2 mb-4">
                <Input
                  placeholder="What are you grateful for today?"
                  value={newGratitude}
                  onChange={(e) => setNewGratitude(e.target.value)}
                  className="rounded-2xl"
                  onKeyPress={(e) => e.key === 'Enter' && addGratitude()}
                />
                <Button
                  onClick={addGratitude}
                  className="bg-gradient-to-r from-pink-600 to-rose-600 rounded-2xl"
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {gratitudeList.map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className={`p-3 rounded-xl ${
                      isDarkMode ? 'bg-pink-800/30' : 'bg-pink-50'
                    }`}
                  >
                    <div className="flex items-center">
                      <span className="text-pink-500 mr-2">💖</span>
                      <span className={`${isDarkMode ? 'text-pink-200' : 'text-pink-800'}`}>
                        {item}
                      </span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Daily Reflection */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <Card className={`border-0 shadow-xl backdrop-blur-xl ${
            isDarkMode
              ? 'bg-blue-900/40 border-blue-500/20'
              : 'bg-blue-100/80'
          }`}>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Moon className="w-6 h-6 text-blue-600 mr-2" />
                <span className={`${isDarkMode ? 'text-blue-300' : 'text-blue-800'}`}>
                  Daily Reflection
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Reflect on your spiritual journey today..."
                value={dailyReflection}
                onChange={(e) => {
                  setDailyReflection(e.target.value);
                  localStorage.setItem('dailyReflection', e.target.value);
                }}
                className="rounded-2xl min-h-[100px] resize-none"
              />
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Add Habit Modal */}
      <AnimatePresence>
        {showAddHabit && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowAddHabit(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className={`rounded-2xl p-6 w-full max-w-md ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              }`}
            >
              <h3 className={`text-xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                Add Spiritual Habit
              </h3>
              <div className="space-y-4">
                <Input
                  placeholder="Habit name"
                  value={newHabit.name}
                  onChange={(e) => setNewHabit({ ...newHabit, name: e.target.value })}
                  className="rounded-xl"
                />
                <Textarea
                  placeholder="Description"
                  value={newHabit.description}
                  onChange={(e) => setNewHabit({ ...newHabit, description: e.target.value })}
                  className="rounded-xl"
                />
                <select
                  value={newHabit.category}
                  onChange={(e) => setNewHabit({ ...newHabit, category: e.target.value as SpiritualHabit['category'] })}
                  className={`w-full px-3 py-2 rounded-xl border-2 focus:outline-none ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-200'
                  }`}
                >
                  {Object.entries(spiritualCategories).map(([key, cat]) => (
                    <option key={key} value={key}>
                      {cat.icon} {cat.name}
                    </option>
                  ))}
                </select>
                <Input
                  type="number"
                  placeholder="Target days"
                  value={newHabit.targetDays}
                  onChange={(e) => setNewHabit({ ...newHabit, targetDays: parseInt(e.target.value) })}
                  className="rounded-xl"
                />
                <div className="flex space-x-2">
                  <Button onClick={addHabit} className="flex-1 bg-green-600 hover:bg-green-700 rounded-xl">
                    Add Habit
                  </Button>
                  <Button variant="outline" onClick={() => setShowAddHabit(false)} className="rounded-xl">
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SpiritualHub;
