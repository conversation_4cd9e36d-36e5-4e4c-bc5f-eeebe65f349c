
import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import TaskCard from './TaskCard';
import { Task } from '../Tasks';

interface KanbanViewProps {
  tasks: Task[];
  onUpdate: (taskId: string, updates: Partial<Task>) => void;
  onDelete: (taskId: string) => void;
}

const KanbanView: React.FC<KanbanViewProps> = ({ tasks, onUpdate, onDelete }) => {
  const columns = [
    { id: 'todo', title: 'To Do', color: 'bg-gray-100' },
    { id: 'inprogress', title: 'In Progress', color: 'bg-blue-100' },
    { id: 'review', title: 'Review', color: 'bg-purple-100' },
    { id: 'done', title: 'Done', color: 'bg-green-100' }
  ];

  const getTasksByStatus = (status: string) => {
    return tasks.filter(task => task.status === status && !task.parentId);
  };

  const handleDragStart = (e: React.DragEvent, task: Task) => {
    e.dataTransfer.setData('text/plain', task.id);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, status: string) => {
    e.preventDefault();
    const taskId = e.dataTransfer.getData('text/plain');
    onUpdate(taskId, { 
      status: status as any,
      completed: status === 'done'
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {columns.map(column => {
        const columnTasks = getTasksByStatus(column.id);
        
        return (
          <Card key={column.id} className={`shadow-lg border-0 ${column.color}`}>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-semibold flex items-center justify-between">
                {column.title}
                <span className="bg-white/80 text-gray-700 px-2 py-1 rounded-full text-xs">
                  {columnTasks.length}
                </span>
              </CardTitle>
            </CardHeader>
            
            <CardContent 
              className="space-y-3 min-h-[400px]"
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, column.id)}
            >
              {columnTasks.map(task => (
                <div
                  key={task.id}
                  draggable
                  onDragStart={(e) => handleDragStart(e, task)}
                  className="cursor-move"
                >
                  <TaskCard
                    task={task}
                    onUpdate={onUpdate}
                    onDelete={onDelete}
                    allTasks={tasks}
                  />
                </div>
              ))}
              
              {columnTasks.length === 0 && (
                <div className="text-center py-8 text-gray-400">
                  <div className="text-sm">No tasks</div>
                  <div className="text-xs">Drag tasks here</div>
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default KanbanView;
