import { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Smartphone, Monitor, Crown, Star, Zap, Heart, Target, Award } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import BillionDollarDashboard from './BillionDollarDashboard';
import PremiumOnboarding from './PremiumOnboarding';

const PremiumShowcase = () => {
  const [activeDemo, setActiveDemo] = useState<'onboarding' | 'dashboard' | null>(null);
  const [deviceView, setDeviceView] = useState<'mobile' | 'desktop'>('mobile');

  const features = [
    {
      icon: Crown,
      title: 'Premium Onboarding',
      description: 'Stunning first impression with animated gradients, floating particles, and smooth transitions',
      color: 'from-purple-500 to-pink-500',
      demo: 'onboarding'
    },
    {
      icon: Star,
      title: 'Billion Dollar Dashboard',
      description: 'World-class UI with glassmorphism, premium colors, and intuitive interactions',
      color: 'from-blue-500 to-cyan-500',
      demo: 'dashboard'
    },
    {
      icon: Zap,
      title: 'Smooth Animations',
      description: 'Framer Motion powered transitions that feel natural and responsive',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Heart,
      title: 'Emotional Design',
      description: 'Carefully crafted color palette that evokes positive emotions',
      color: 'from-pink-500 to-rose-500'
    },
    {
      icon: Target,
      title: 'Mobile-First',
      description: 'Optimized for mobile with perfect responsive scaling to desktop',
      color: 'from-orange-500 to-red-500'
    },
    {
      icon: Award,
      title: 'Premium Quality',
      description: 'Every detail polished to perfection with attention to micro-interactions',
      color: 'from-indigo-500 to-purple-500'
    }
  ];

  const mockUserProfile = {
    name: 'Alex Chen',
    avatar: '🚀',
    goals: ['productivity', 'health', 'learning'],
    workStyle: 'creative-bursts',
    motto: 'Progress over perfection'
  };

  if (activeDemo) {
    return (
      <div className="relative min-h-screen">
        {/* Demo Controls */}
        <div className="fixed top-4 left-4 z-50 flex items-center space-x-2">
          <Button
            onClick={() => setActiveDemo(null)}
            variant="outline"
            size="sm"
            className="rounded-2xl bg-white/90 backdrop-blur-sm border border-white/30 shadow-lg"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Showcase
          </Button>
          
          {activeDemo === 'dashboard' && (
            <div className="flex items-center space-x-1 bg-white/90 backdrop-blur-sm border border-white/30 rounded-2xl p-1 shadow-lg">
              <Button
                onClick={() => setDeviceView('mobile')}
                variant={deviceView === 'mobile' ? 'default' : 'ghost'}
                size="sm"
                className="rounded-xl"
              >
                <Smartphone className="w-4 h-4" />
              </Button>
              <Button
                onClick={() => setDeviceView('desktop')}
                variant={deviceView === 'desktop' ? 'default' : 'ghost'}
                size="sm"
                className="rounded-xl"
              >
                <Monitor className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Demo Content */}
        <div className="min-h-screen">
          {activeDemo === 'onboarding' && (
            <PremiumOnboarding onComplete={() => setActiveDemo('dashboard')} />
          )}
          
          {activeDemo === 'dashboard' && (
            <div className={`min-h-screen ${
              deviceView === 'mobile' 
                ? 'max-w-sm mx-auto' 
                : 'w-full'
            } transition-all duration-500`}>
              {deviceView === 'mobile' && (
                <div className="bg-black rounded-[3rem] p-2 shadow-2xl mx-4 mt-16">
                  <div className="bg-white rounded-[2.5rem] overflow-hidden">
                    <div className="h-6 bg-black rounded-t-[2.5rem] flex items-center justify-center">
                      <div className="w-16 h-1 bg-gray-800 rounded-full"></div>
                    </div>
                    <div className="h-[700px] overflow-hidden">
                      <BillionDollarDashboard userProfile={mockUserProfile} />
                    </div>
                  </div>
                </div>
              )}
              
              {deviceView === 'desktop' && (
                <div className="bg-gray-800 rounded-2xl p-4 shadow-2xl mx-4 mt-16">
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                  <div className="bg-white rounded-xl overflow-hidden h-[800px]">
                    <BillionDollarDashboard userProfile={mockUserProfile} />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-2000" />
      </div>

      <div className="relative z-10 p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring" }}
            className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-3xl flex items-center justify-center shadow-xl"
          >
            <Crown className="w-10 h-10 text-white" />
          </motion.div>
          
          <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 bg-clip-text text-transparent mb-4">
            Premium FOCOS
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            Experience the billion-dollar app aesthetic with stunning visuals, smooth animations, and premium interactions
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={() => setActiveDemo('onboarding')}
              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-2xl text-lg font-medium shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
            >
              <Crown className="w-5 h-5 mr-2" />
              Try Premium Onboarding
            </Button>
            <Button
              onClick={() => setActiveDemo('dashboard')}
              variant="outline"
              className="border-2 border-purple-300 text-purple-600 px-8 py-4 rounded-2xl text-lg font-medium hover:bg-purple-50 transition-all duration-300 hover:scale-105"
            >
              <Star className="w-5 h-5 mr-2" />
              View Dashboard
            </Button>
          </div>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto mb-12"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
              whileHover={{ scale: 1.05 }}
              className="group cursor-pointer"
              onClick={() => feature.demo && setActiveDemo(feature.demo as any)}
            >
              <Card className="h-full border-0 shadow-xl bg-white/80 backdrop-blur-sm hover:shadow-2xl transition-all duration-300 hover:bg-white/90">
                <CardHeader>
                  <div className={`w-14 h-14 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                    <feature.icon className="w-7 h-7 text-white" />
                  </div>
                  <CardTitle className="text-xl font-bold text-gray-800 group-hover:text-purple-600 transition-colors">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                  {feature.demo && (
                    <div className="mt-4">
                      <span className="text-sm text-purple-600 font-medium">Click to try →</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Premium Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          <Card className="border-0 shadow-xl bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 backdrop-blur-xl">
            <CardContent className="p-8">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
                <div>
                  <div className="text-3xl font-bold text-purple-600 mb-2">100+</div>
                  <div className="text-sm text-gray-600">Premium Components</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-pink-600 mb-2">60fps</div>
                  <div className="text-sm text-gray-600">Smooth Animations</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-blue-600 mb-2">Mobile</div>
                  <div className="text-sm text-gray-600">First Design</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-green-600 mb-2">A11y</div>
                  <div className="text-sm text-gray-600">Accessible</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default PremiumShowcase;
