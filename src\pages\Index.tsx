
import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import FocosOnboarding from '../components/FocosOnboarding';
import PremiumOnboarding from '../components/PremiumOnboarding';
import FocosData from '../components/FocosData';
import Dashboard from '../components/Dashboard';
import PremiumDashboard from '../components/PremiumDashboard';
import BillionDollarDashboard from '../components/BillionDollarDashboard';
import PremiumDashboardDemo from '../components/PremiumDashboardDemo';
import PremiumShowcase from '../components/PremiumShowcase';
import Notes from '../components/Notes';
import Tasks from '../components/Tasks';
import Habits from '../components/Habits';
import Finance from '../components/Finance';
import MoodTracker from '../components/MoodTracker';
import RoutineBuilder from '../components/RoutineBuilder';
import Goals from '../components/Goals';
import TimeTracker from '../components/TimeTracker';
import Calendar from '../components/Calendar';
import HealthTracker from '../components/HealthTracker';
import Settings from '../components/Settings';
import PlannerScheduler from '../components/PlannerScheduler';
import JournalingHub from '../components/JournalingHub';
import WellnessTools from '../components/WellnessTools';
import GoalProjectManagement from '../components/GoalProjectManagement';
import KnowledgeVault from '../components/KnowledgeVault';
import Toolbox from '../components/Toolbox';
import SpiritualHub from '../components/SpiritualHub';
import PomodoroTimer from '../components/PomodoroTimer';
import ProjectBoard from '../components/ProjectBoard';
import QuickCapture from '../components/QuickCapture';
import BottomNav from '../components/BottomNav';
import WidgetDashboard from '../components/WidgetDashboard';
import ThemeCustomizer from '../components/ThemeCustomizer';
import AppLock from '../components/AppLock';
import NotificationService from '../services/NotificationService';
import DataSyncService from '../services/DataSyncService';
import SecurityService from '../services/SecurityService';
import ThemeEngine from '../services/ThemeEngine';

const Index = () => {
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false);
  const [userProfile, setUserProfile] = useState(null);
  const [isAppLocked, setIsAppLocked] = useState(false);
  const [isFakeMode, setIsFakeMode] = useState(false);

  useEffect(() => {
    const focusData = localStorage.getItem('focosOnboardingData');
    const onboardingComplete = localStorage.getItem('onboardingComplete');

    if (focusData && onboardingComplete) {
      setUserProfile(JSON.parse(focusData));
      setHasCompletedOnboarding(true);
    }

    // Check security status
    setIsAppLocked(SecurityService.isSecurityEnabled() && !SecurityService.isAppUnlocked());
    setIsFakeMode(SecurityService.isInFakeMode());

    // Set up notification permission
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }

    // Set up service worker for notifications
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js').catch(() => {
        console.log('Service Worker registration failed');
      });
    }
  }, []);

  const handleOnboardingComplete = (data: any) => {
    setUserProfile(data);
    setHasCompletedOnboarding(true);
    localStorage.setItem('focosOnboardingData', JSON.stringify(data));
    localStorage.setItem('onboardingComplete', 'true');
  };

  const handleAppUnlock = (fakeMode: boolean) => {
    setIsAppLocked(false);
    setIsFakeMode(fakeMode);
    ThemeEngine.playSound('taskComplete');
  };

  // Enhanced real-time notifications and services setup
  useEffect(() => {
    // Initialize services immediately
    const initializeServices = async () => {
      // Request notification permission
      await NotificationService.requestPermission();

      // Start data synchronization
      DataSyncService.startSync();

      // Schedule recurring notifications
      NotificationService.scheduleRecurringNotifications();

      // Show welcome notification if first time
      const isFirstTime = !localStorage.getItem('hasUsedFOCOS');
      if (isFirstTime && NotificationService.getPermissionStatus() === 'granted') {
        await NotificationService.sendNotification({
          title: '🎉 Welcome to FOCOS!',
          body: 'Your premium productivity suite is ready. Let\'s achieve your goals together!',
          tag: 'welcome',
          requireInteraction: true
        });
        localStorage.setItem('hasUsedFOCOS', 'true');
      }
    };

    initializeServices();

    if (!hasCompletedOnboarding) return;

    const checkReminders = () => {
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinutes = now.getMinutes();

      // Enhanced task reminders using new service
      const tasks = DataSyncService.getData('tasks');
      const dueTasks = tasks.filter((task: any) => {
        if (task.completed || !task.dueDate) return false;
        const dueDate = new Date(task.dueDate);
        return dueDate.toDateString() === now.toDateString() &&
               dueDate.getHours() === currentHour &&
               dueDate.getMinutes() === currentMinutes;
      });

      dueTasks.forEach((task: any) => {
        NotificationService.sendTaskReminder(task.title, task.dueTime);
      });

      // Enhanced habit reminders
      if (currentMinutes === 0 && [8, 12, 18, 21].includes(currentHour)) {
        const habits = DataSyncService.getData('habits');
        const pendingHabits = habits.filter((habit: any) => {
          const today = now.toDateString();
          return habit.isActive && !habit.completedDates?.includes(today);
        });

        pendingHabits.forEach((habit: any) => {
          NotificationService.sendHabitReminder(habit.name, habit.streak || 0);
        });
      }

      // Budget alerts
      const budgets = DataSyncService.getData('budgets');
      budgets.forEach((budget: any) => {
        const percentage = (budget.spent / budget.limit) * 100;
        if (percentage >= 90 && !budget.alertSent90) {
          NotificationService.sendBudgetAlert(budget.category, percentage, 'warning');
          budget.alertSent90 = true;
          DataSyncService.saveData('budgets', budgets);
        } else if (percentage >= 100 && !budget.alertSent100) {
          NotificationService.sendBudgetAlert(budget.category, percentage, 'danger');
          budget.alertSent100 = true;
          DataSyncService.saveData('budgets', budgets);
        }
      });
    };

    const interval = setInterval(checkReminders, 60000); // Check every minute
    return () => {
      clearInterval(interval);
      DataSyncService.stopSync();
    };
  }, [hasCompletedOnboarding]);

  // Show app lock if security is enabled and app is locked
  if (isAppLocked) {
    return <AppLock onUnlock={handleAppUnlock} />;
  }

  if (!hasCompletedOnboarding) {
    return <PremiumOnboarding onComplete={handleOnboardingComplete} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Fake Mode Indicator */}
      {isFakeMode && (
        <div className="fixed top-0 left-0 right-0 bg-orange-500 text-white text-center py-1 text-xs z-50">
          🎭 Fake Mode Active - Limited Data Shown
        </div>
      )}

      <Router>
        <div className="pb-20">
          <Routes>
            <Route path="/" element={<BillionDollarDashboard userProfile={userProfile} />} />
            <Route path="/dashboard" element={<BillionDollarDashboard userProfile={userProfile} />} />
            <Route path="/widget-dashboard" element={<WidgetDashboard />} />
            <Route path="/old-dashboard" element={<Dashboard userProfile={userProfile} />} />
            <Route path="/premium-dashboard" element={<PremiumDashboard userProfile={userProfile} />} />
            <Route path="/premium-demo" element={<PremiumDashboardDemo />} />
            <Route path="/showcase" element={<PremiumShowcase />} />
            <Route path="/focos-data" element={<FocosData />} />
            <Route path="/notes" element={<Notes />} />
            <Route path="/tasks" element={<Tasks />} />
            <Route path="/habits" element={<Habits />} />
            <Route path="/finance" element={<Finance />} />
            <Route path="/mood" element={<MoodTracker />} />
            <Route path="/routines" element={<RoutineBuilder />} />
            <Route path="/goals" element={<Goals />} />
            <Route path="/time-tracker" element={<TimeTracker />} />
            <Route path="/calendar" element={<Calendar />} />
            <Route path="/health" element={<HealthTracker />} />
            <Route path="/planner" element={<PlannerScheduler />} />
            <Route path="/journal" element={<JournalingHub />} />
            <Route path="/wellness" element={<WellnessTools />} />
            <Route path="/goal-management" element={<GoalProjectManagement />} />
            <Route path="/knowledge-vault" element={<KnowledgeVault />} />
            <Route path="/toolbox" element={<Toolbox />} />
            <Route path="/spiritual" element={<SpiritualHub />} />
            <Route path="/pomodoro" element={<PomodoroTimer />} />
            <Route path="/projects" element={<ProjectBoard />} />
            <Route path="/quick-capture" element={<QuickCapture />} />
            <Route path="/theme-customizer" element={<ThemeCustomizer />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </div>
        <BottomNav />
      </Router>
    </div>
  );
};

export default Index;
