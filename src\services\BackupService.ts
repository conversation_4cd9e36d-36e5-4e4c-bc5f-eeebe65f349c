// Comprehensive Backup and Restore Service for FOCOS
import CryptoJS from 'crypto-js';
import DataSyncService from './DataSyncService';
import ThemeEngine from './ThemeEngine';
import SecurityService from './SecurityService';

export interface BackupData {
  version: string;
  timestamp: string;
  deviceInfo: {
    userAgent: string;
    platform: string;
    language: string;
  };
  userData: {
    profile: any;
    onboardingData: any;
  };
  appData: {
    tasks: any[];
    habits: any[];
    transactions: any[];
    budgets: any[];
    goals: any[];
    journalEntries: any[];
    moods: any[];
    routines: any[];
    calendarEvents: any[];
    notes: any[];
    healthData: any[];
    timeTracking: any[];
  };
  settings: {
    theme: any;
    customThemes: any[];
    notifications: any;
    preferences: any;
  };
  analytics: any;
  checksum: string;
}

export interface BackupOptions {
  includePersonalData: boolean;
  includeSettings: boolean;
  includeAnalytics: boolean;
  compression: boolean;
  encryption: boolean;
  password?: string;
}

class BackupService {
  private static instance: BackupService;
  private readonly BACKUP_VERSION = '1.0.0';

  constructor() {}

  static getInstance(): BackupService {
    if (!BackupService.instance) {
      BackupService.instance = new BackupService();
    }
    return BackupService.instance;
  }

  // Create comprehensive backup
  async createBackup(options: BackupOptions): Promise<string> {
    try {
      const backupData: Omit<BackupData, 'checksum'> = {
        version: this.BACKUP_VERSION,
        timestamp: new Date().toISOString(),
        deviceInfo: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          language: navigator.language
        },
        userData: this.getUserData(),
        appData: this.getAppData(),
        settings: this.getSettings(),
        analytics: options.includeAnalytics ? this.getAnalytics() : {}
      };

      // Calculate checksum
      const dataString = JSON.stringify(backupData);
      const checksum = CryptoJS.SHA256(dataString).toString();
      
      const finalBackupData: BackupData = {
        ...backupData,
        checksum
      };

      let result = JSON.stringify(finalBackupData, null, 2);

      // Apply compression if requested
      if (options.compression) {
        result = this.compressData(result);
      }

      // Apply encryption if requested
      if (options.encryption && options.password) {
        result = this.encryptData(result, options.password);
      }

      return result;
    } catch (error) {
      throw new Error('Failed to create backup: ' + (error as Error).message);
    }
  }

  // Restore from backup
  async restoreBackup(backupData: string, options: { password?: string; mergeData?: boolean }): Promise<void> {
    try {
      let data = backupData;

      // Decrypt if needed
      if (options.password) {
        data = this.decryptData(data, options.password);
      }

      // Decompress if needed
      if (this.isCompressed(data)) {
        data = this.decompressData(data);
      }

      const backup: BackupData = JSON.parse(data);

      // Verify backup integrity
      if (!this.verifyBackup(backup)) {
        throw new Error('Backup data is corrupted or invalid');
      }

      // Check version compatibility
      if (!this.isVersionCompatible(backup.version)) {
        throw new Error(`Backup version ${backup.version} is not compatible with current app version`);
      }

      // Restore data
      await this.performRestore(backup, options.mergeData || false);

      // Notify success
      ThemeEngine.playSound('taskComplete');
      
    } catch (error) {
      throw new Error('Failed to restore backup: ' + (error as Error).message);
    }
  }

  // Export backup to file
  async exportBackup(options: BackupOptions): Promise<void> {
    try {
      const backupData = await this.createBackup(options);
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `focos-backup-${timestamp}.json`;

      const blob = new Blob([backupData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      a.click();
      
      URL.revokeObjectURL(url);
      
      ThemeEngine.playSound('taskComplete');
    } catch (error) {
      throw new Error('Failed to export backup: ' + (error as Error).message);
    }
  }

  // Import backup from file
  async importBackup(file: File, options: { password?: string; mergeData?: boolean }): Promise<void> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = async (e) => {
        try {
          const backupData = e.target?.result as string;
          await this.restoreBackup(backupData, options);
          resolve();
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read backup file'));
      };
      
      reader.readAsText(file);
    });
  }

  // Get backup info without restoring
  getBackupInfo(backupData: string, password?: string): Partial<BackupData> | null {
    try {
      let data = backupData;

      if (password) {
        data = this.decryptData(data, password);
      }

      if (this.isCompressed(data)) {
        data = this.decompressData(data);
      }

      const backup: BackupData = JSON.parse(data);
      
      return {
        version: backup.version,
        timestamp: backup.timestamp,
        deviceInfo: backup.deviceInfo
      };
    } catch (error) {
      return null;
    }
  }

  // Private helper methods
  private getUserData(): any {
    return {
      profile: this.getStorageItem('focosOnboardingData'),
      onboardingData: {
        completed: this.getStorageItem('onboardingComplete'),
        premiumCompleted: this.getStorageItem('premiumOnboardingComplete')
      }
    };
  }

  private getAppData(): any {
    return {
      tasks: DataSyncService.getData('tasks'),
      habits: DataSyncService.getData('habits'),
      transactions: DataSyncService.getData('transactions'),
      budgets: DataSyncService.getData('budgets'),
      goals: DataSyncService.getData('goals'),
      journalEntries: DataSyncService.getData('journalEntries'),
      moods: DataSyncService.getData('moods'),
      routines: DataSyncService.getData('routines'),
      calendarEvents: DataSyncService.getData('calendarEvents'),
      notes: DataSyncService.getData('notes'),
      healthData: DataSyncService.getData('healthData'),
      timeTracking: DataSyncService.getData('timeTracking')
    };
  }

  private getSettings(): any {
    return {
      theme: ThemeEngine.getCurrentTheme(),
      customThemes: this.getStorageItem('customThemes'),
      notifications: this.getStorageItem('notificationSettings'),
      preferences: this.getStorageItem('userPreferences'),
      dashboardWidgets: DataSyncService.getData('dashboardWidgets')
    };
  }

  private getAnalytics(): any {
    return DataSyncService.getData('analytics');
  }

  private getStorageItem(key: string): any {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch {
      return localStorage.getItem(key);
    }
  }

  private verifyBackup(backup: BackupData): boolean {
    try {
      // Verify required fields
      if (!backup.version || !backup.timestamp || !backup.checksum) {
        return false;
      }

      // Verify checksum
      const { checksum, ...dataWithoutChecksum } = backup;
      const calculatedChecksum = CryptoJS.SHA256(JSON.stringify(dataWithoutChecksum)).toString();
      
      return checksum === calculatedChecksum;
    } catch {
      return false;
    }
  }

  private isVersionCompatible(version: string): boolean {
    // Simple version compatibility check
    const [major] = version.split('.').map(Number);
    const [currentMajor] = this.BACKUP_VERSION.split('.').map(Number);
    
    return major === currentMajor;
  }

  private async performRestore(backup: BackupData, mergeData: boolean): Promise<void> {
    try {
      // Restore user data
      if (backup.userData.profile) {
        localStorage.setItem('focosOnboardingData', JSON.stringify(backup.userData.profile));
      }
      
      if (backup.userData.onboardingData.completed) {
        localStorage.setItem('onboardingComplete', backup.userData.onboardingData.completed);
      }

      // Restore app data
      Object.entries(backup.appData).forEach(([key, data]) => {
        if (data && Array.isArray(data)) {
          if (mergeData) {
            const existingData = DataSyncService.getData(key);
            const mergedData = this.mergeArrayData(existingData, data);
            DataSyncService.saveData(key, mergedData);
          } else {
            DataSyncService.saveData(key, data);
          }
        }
      });

      // Restore settings
      if (backup.settings.customThemes) {
        localStorage.setItem('customThemes', JSON.stringify(backup.settings.customThemes));
      }
      
      if (backup.settings.notifications) {
        localStorage.setItem('notificationSettings', JSON.stringify(backup.settings.notifications));
      }
      
      if (backup.settings.preferences) {
        localStorage.setItem('userPreferences', JSON.stringify(backup.settings.preferences));
      }

      if (backup.settings.dashboardWidgets) {
        DataSyncService.saveData('dashboardWidgets', backup.settings.dashboardWidgets);
      }

      // Restore analytics
      if (backup.analytics) {
        DataSyncService.saveData('analytics', backup.analytics);
      }

      // Apply theme if available
      if (backup.settings.theme) {
        const themeId = backup.settings.theme.id;
        if (ThemeEngine.getTheme(themeId)) {
          ThemeEngine.setTheme(themeId);
        }
      }

    } catch (error) {
      throw new Error('Failed to restore data: ' + (error as Error).message);
    }
  }

  private mergeArrayData(existing: any[], incoming: any[]): any[] {
    const merged = [...existing];
    
    incoming.forEach(item => {
      const existingIndex = merged.findIndex(existing => 
        existing.id === item.id || 
        (existing.title === item.title && existing.date === item.date)
      );
      
      if (existingIndex === -1) {
        merged.push(item);
      } else {
        // Update existing item with newer timestamp
        if (new Date(item.updatedAt || item.createdAt || 0) > 
            new Date(merged[existingIndex].updatedAt || merged[existingIndex].createdAt || 0)) {
          merged[existingIndex] = item;
        }
      }
    });
    
    return merged;
  }

  private encryptData(data: string, password: string): string {
    return CryptoJS.AES.encrypt(data, password).toString();
  }

  private decryptData(encryptedData: string, password: string): string {
    const decrypted = CryptoJS.AES.decrypt(encryptedData, password);
    return decrypted.toString(CryptoJS.enc.Utf8);
  }

  private compressData(data: string): string {
    // Simple compression using base64 encoding
    // In a real implementation, you might use a proper compression library
    return btoa(data);
  }

  private decompressData(compressedData: string): string {
    return atob(compressedData);
  }

  private isCompressed(data: string): boolean {
    // Simple check for base64 encoding
    try {
      return btoa(atob(data)) === data;
    } catch {
      return false;
    }
  }

  // Automatic backup scheduling
  scheduleAutoBackup(intervalHours: number = 24): void {
    const interval = intervalHours * 60 * 60 * 1000;
    
    setInterval(async () => {
      try {
        const options: BackupOptions = {
          includePersonalData: true,
          includeSettings: true,
          includeAnalytics: false,
          compression: true,
          encryption: false
        };
        
        const backup = await this.createBackup(options);
        localStorage.setItem('autoBackup', backup);
        localStorage.setItem('lastAutoBackup', new Date().toISOString());
        
        console.log('Auto backup completed');
      } catch (error) {
        console.error('Auto backup failed:', error);
      }
    }, interval);
  }

  // Get auto backup
  getAutoBackup(): string | null {
    return localStorage.getItem('autoBackup');
  }

  // Clear all data (for testing or reset)
  async clearAllData(): Promise<void> {
    const confirmMessage = 'Are you sure you want to clear all data? This action cannot be undone.';
    
    if (confirm(confirmMessage)) {
      // Clear all localStorage
      const keysToKeep = ['theme', 'language'];
      const allKeys = Object.keys(localStorage);
      
      allKeys.forEach(key => {
        if (!keysToKeep.includes(key)) {
          localStorage.removeItem(key);
        }
      });
      
      // Reset services
      DataSyncService.clearAllData();
      
      ThemeEngine.playSound('taskComplete');
      alert('All data has been cleared successfully.');
      
      // Reload the page
      window.location.reload();
    }
  }
}

export default BackupService.getInstance();
