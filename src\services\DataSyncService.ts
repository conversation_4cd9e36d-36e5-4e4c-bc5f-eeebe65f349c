// Real-time Data Synchronization Service for FOCOS
import NotificationService from './NotificationService';

export interface DataChangeEvent {
  type: 'create' | 'update' | 'delete';
  collection: string;
  data: any;
  timestamp: number;
}

export interface SyncConfig {
  autoSync: boolean;
  syncInterval: number; // in milliseconds
  enableNotifications: boolean;
  enableAnalytics: boolean;
}

class DataSyncService {
  private static instance: DataSyncService;
  private listeners: Map<string, Set<(data: any) => void>> = new Map();
  private syncConfig: SyncConfig = {
    autoSync: true,
    syncInterval: 5000, // 5 seconds
    enableNotifications: true,
    enableAnalytics: true
  };
  private syncInterval: NodeJS.Timeout | null = null;
  private lastSyncTime: number = 0;

  constructor() {
    this.initializeSync();
    this.setupStorageListener();
  }

  static getInstance(): DataSyncService {
    if (!DataSyncService.instance) {
      DataSyncService.instance = new DataSyncService();
    }
    return DataSyncService.instance;
  }

  // Initialize real-time synchronization
  private initializeSync(): void {
    if (this.syncConfig.autoSync) {
      this.startSync();
    }
  }

  // Start automatic synchronization
  startSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      this.performSync();
    }, this.syncConfig.syncInterval);
  }

  // Stop automatic synchronization
  stopSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  // Perform synchronization
  private performSync(): void {
    const now = Date.now();
    this.lastSyncTime = now;

    // Sync all data collections
    this.syncCollection('tasks');
    this.syncCollection('habits');
    this.syncCollection('transactions');
    this.syncCollection('budgets');
    this.syncCollection('goals');
    this.syncCollection('journalEntries');
    this.syncCollection('moods');
    this.syncCollection('routines');

    // Trigger analytics update
    if (this.syncConfig.enableAnalytics) {
      this.updateAnalytics();
    }
  }

  // Sync a specific data collection
  private syncCollection(collection: string): void {
    try {
      const data = this.getData(collection);
      this.notifyListeners(collection, data);
    } catch (error) {
      console.error(`Failed to sync collection ${collection}:`, error);
    }
  }

  // Save data with real-time sync
  saveData(collection: string, data: any): void {
    try {
      localStorage.setItem(collection, JSON.stringify(data));
      
      // Trigger immediate sync for this collection
      this.notifyListeners(collection, data);
      
      // Emit change event
      this.emitChangeEvent('update', collection, data);
      
      // Update analytics
      if (this.syncConfig.enableAnalytics) {
        this.updateCollectionAnalytics(collection, data);
      }
      
    } catch (error) {
      console.error(`Failed to save data for ${collection}:`, error);
    }
  }

  // Get data from storage
  getData(collection: string): any {
    try {
      const data = localStorage.getItem(collection);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error(`Failed to get data for ${collection}:`, error);
      return [];
    }
  }

  // Subscribe to data changes
  subscribe(collection: string, callback: (data: any) => void): () => void {
    if (!this.listeners.has(collection)) {
      this.listeners.set(collection, new Set());
    }
    
    this.listeners.get(collection)!.add(callback);
    
    // Immediately call with current data
    const currentData = this.getData(collection);
    callback(currentData);
    
    // Return unsubscribe function
    return () => {
      this.listeners.get(collection)?.delete(callback);
    };
  }

  // Notify all listeners of data changes
  private notifyListeners(collection: string, data: any): void {
    const listeners = this.listeners.get(collection);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in listener for ${collection}:`, error);
        }
      });
    }
  }

  // Emit change event
  private emitChangeEvent(type: 'create' | 'update' | 'delete', collection: string, data: any): void {
    const event: DataChangeEvent = {
      type,
      collection,
      data,
      timestamp: Date.now()
    };
    
    // Store recent changes for debugging
    this.storeRecentChange(event);
  }

  // Store recent changes for debugging
  private storeRecentChange(event: DataChangeEvent): void {
    const recentChanges = this.getData('recentChanges') || [];
    recentChanges.unshift(event);
    
    // Keep only last 50 changes
    if (recentChanges.length > 50) {
      recentChanges.splice(50);
    }
    
    localStorage.setItem('recentChanges', JSON.stringify(recentChanges));
  }

  // Setup storage event listener for cross-tab sync
  private setupStorageListener(): void {
    window.addEventListener('storage', (event) => {
      if (event.key && event.newValue) {
        try {
          const data = JSON.parse(event.newValue);
          this.notifyListeners(event.key, data);
        } catch (error) {
          console.error('Failed to parse storage event data:', error);
        }
      }
    });
  }

  // Update analytics
  private updateAnalytics(): void {
    const analytics = {
      lastSync: this.lastSyncTime,
      totalTasks: this.getData('tasks').length,
      totalHabits: this.getData('habits').length,
      totalTransactions: this.getData('transactions').length,
      totalBudgets: this.getData('budgets').length,
      totalGoals: this.getData('goals').length,
      totalJournalEntries: this.getData('journalEntries').length,
      totalMoods: this.getData('moods').length,
      totalRoutines: this.getData('routines').length
    };
    
    localStorage.setItem('analytics', JSON.stringify(analytics));
  }

  // Update collection-specific analytics
  private updateCollectionAnalytics(collection: string, data: any): void {
    const analytics = this.getData('analytics') || {};
    
    switch (collection) {
      case 'tasks':
        analytics.completedTasks = data.filter((task: any) => task.completed).length;
        analytics.pendingTasks = data.filter((task: any) => !task.completed).length;
        break;
      case 'habits':
        analytics.activeHabits = data.filter((habit: any) => habit.isActive).length;
        break;
      case 'transactions':
        const thisMonth = new Date().getMonth();
        const thisYear = new Date().getFullYear();
        const monthlyTransactions = data.filter((t: any) => {
          const tDate = new Date(t.date);
          return tDate.getMonth() === thisMonth && tDate.getFullYear() === thisYear;
        });
        analytics.monthlyIncome = monthlyTransactions
          .filter((t: any) => t.type === 'income')
          .reduce((sum: number, t: any) => sum + t.amount, 0);
        analytics.monthlyExpenses = monthlyTransactions
          .filter((t: any) => t.type === 'expense')
          .reduce((sum: number, t: any) => sum + t.amount, 0);
        break;
      case 'budgets':
        analytics.budgetsOverLimit = data.filter((b: any) => b.spent > b.limit).length;
        analytics.budgetsNearLimit = data.filter((b: any) => b.spent / b.limit > 0.8).length;
        break;
    }
    
    this.saveData('analytics', analytics);
  }

  // Get real-time statistics
  getRealTimeStats(): any {
    return {
      lastSync: this.lastSyncTime,
      isOnline: navigator.onLine,
      syncEnabled: this.syncConfig.autoSync,
      collections: {
        tasks: this.getData('tasks').length,
        habits: this.getData('habits').length,
        transactions: this.getData('transactions').length,
        budgets: this.getData('budgets').length,
        goals: this.getData('goals').length,
        journalEntries: this.getData('journalEntries').length,
        moods: this.getData('moods').length,
        routines: this.getData('routines').length
      }
    };
  }

  // Configure sync settings
  configurSync(config: Partial<SyncConfig>): void {
    this.syncConfig = { ...this.syncConfig, ...config };
    
    if (config.autoSync !== undefined) {
      if (config.autoSync) {
        this.startSync();
      } else {
        this.stopSync();
      }
    }
    
    if (config.syncInterval) {
      this.stopSync();
      this.startSync();
    }
  }

  // Export all data
  exportAllData(): any {
    return {
      tasks: this.getData('tasks'),
      habits: this.getData('habits'),
      transactions: this.getData('transactions'),
      budgets: this.getData('budgets'),
      goals: this.getData('goals'),
      journalEntries: this.getData('journalEntries'),
      moods: this.getData('moods'),
      routines: this.getData('routines'),
      analytics: this.getData('analytics'),
      exportedAt: new Date().toISOString()
    };
  }

  // Import data
  importData(data: any): void {
    Object.keys(data).forEach(collection => {
      if (collection !== 'exportedAt') {
        this.saveData(collection, data[collection]);
      }
    });
    
    if (this.syncConfig.enableNotifications) {
      NotificationService.sendNotification({
        title: '📥 Data Import Complete',
        body: 'Your data has been successfully imported and synced.',
        tag: 'data-import'
      });
    }
  }

  // Clear all data
  clearAllData(): void {
    const collections = ['tasks', 'habits', 'transactions', 'budgets', 'goals', 'journalEntries', 'moods', 'routines'];
    collections.forEach(collection => {
      localStorage.removeItem(collection);
      this.notifyListeners(collection, []);
    });
    
    if (this.syncConfig.enableNotifications) {
      NotificationService.sendNotification({
        title: '🗑️ Data Cleared',
        body: 'All data has been cleared from the app.',
        tag: 'data-clear'
      });
    }
  }
}

export default DataSyncService.getInstance();
