/* Billion Dollar App Glassmorphism Effects */

.glass-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.4);
}

.glass-card-premium {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.1),
    0 8px 25px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glass-card-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

.glass-nav {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-button {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Billion Dollar App Color Gradients */
.gradient-frost-blue {
  background: linear-gradient(135deg, #DDF2FD 0%, #E5D9F2 100%);
}

.gradient-lavender-blush {
  background: linear-gradient(135deg, #E5D9F2 0%, #F8C8DC 100%);
}

.gradient-midnight-gold {
  background: linear-gradient(135deg, #1D1A39 0%, #F4E869 100%);
}

.gradient-soft-rainbow {
  background: linear-gradient(135deg, #DDF2FD 0%, #E5D9F2 25%, #F8C8DC 50%, #F4E869 75%, #F1F2F6 100%);
}

.gradient-premium-purple {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-premium-blue {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.gradient-premium-pink {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
}

.gradient-premium-green {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
}

.gradient-premium-orange {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

/* Premium Animated Gradients */
.animated-gradient {
  background: linear-gradient(-45deg, #667eea, #764ba2, #74b9ff, #fd79a8, #00b894);
  background-size: 400% 400%;
  animation: gradientShift 20s ease infinite;
}

.animated-gradient-slow {
  background: linear-gradient(-45deg, #DDF2FD, #E5D9F2, #F8C8DC, #F4E869);
  background-size: 600% 600%;
  animation: gradientShift 30s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 50% 100%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Premium Mesh Gradients */
.mesh-gradient {
  background:
    radial-gradient(at 40% 20%, hsla(228,100%,74%,1) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(189,100%,56%,1) 0px, transparent 50%),
    radial-gradient(at 0% 50%, hsla(355,100%,93%,1) 0px, transparent 50%),
    radial-gradient(at 80% 50%, hsla(340,100%,76%,1) 0px, transparent 50%),
    radial-gradient(at 0% 100%, hsla(22,100%,77%,1) 0px, transparent 50%),
    radial-gradient(at 80% 100%, hsla(242,100%,70%,1) 0px, transparent 50%),
    radial-gradient(at 0% 0%, hsla(343,100%,76%,1) 0px, transparent 50%);
}

/* Floating Elements */
.floating {
  animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
  0% {
    transform: translate(0, 0px);
  }
  50% {
    transform: translate(0, -10px);
  }
  100% {
    transform: translate(0, 0px);
  }
}

/* Premium Glow Effects */
.glow-purple {
  box-shadow:
    0 0 20px rgba(147, 51, 234, 0.4),
    0 0 40px rgba(147, 51, 234, 0.2),
    0 0 80px rgba(147, 51, 234, 0.1);
}

.glow-pink {
  box-shadow:
    0 0 20px rgba(236, 72, 153, 0.4),
    0 0 40px rgba(236, 72, 153, 0.2),
    0 0 80px rgba(236, 72, 153, 0.1);
}

.glow-blue {
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.4),
    0 0 40px rgba(59, 130, 246, 0.2),
    0 0 80px rgba(59, 130, 246, 0.1);
}

.glow-premium {
  box-shadow:
    0 0 30px rgba(147, 51, 234, 0.3),
    0 0 60px rgba(236, 72, 153, 0.2),
    0 0 90px rgba(59, 130, 246, 0.1);
}

/* Premium Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 10px 20px rgba(0, 0, 0, 0.08);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow:
    0 0 30px rgba(147, 51, 234, 0.4),
    0 0 60px rgba(236, 72, 153, 0.3),
    0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Soft Shadows */
.soft-shadow {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.soft-shadow-lg {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* Rounded Corners - iPhone Style */
.rounded-3xl {
  border-radius: 1.5rem;
}

.rounded-4xl {
  border-radius: 2rem;
}

.rounded-5xl {
  border-radius: 2.5rem;
}

/* Progress Ring Animation */
.progress-ring {
  transition: stroke-dashoffset 0.5s ease-in-out;
}

/* Pulse Animation */
.pulse-soft {
  animation: pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulseSoft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Shimmer Effect */
.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Smooth Transitions */
.transition-all-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
  .glass-card {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .glass-nav {
    background: rgba(0, 0, 0, 0.1);
  }
  
  .glass-button {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .glass-button:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .glass-card {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }

  .glass-nav {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  /* Mobile-specific spacing */
  .mobile-padding {
    padding: 1rem;
  }

  .mobile-text {
    font-size: 0.875rem;
  }

  .mobile-title {
    font-size: 1.25rem;
  }

  /* Touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Safe area support */
  .safe-area-inset-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-inset-top {
    padding-top: env(safe-area-inset-top);
  }
}

/* High Performance Mode for Older Devices */
@media (max-width: 480px) {
  .glass-card,
  .glass-nav,
  .glass-button {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background: rgba(255, 255, 255, 0.9);
  }
  
  .animated-gradient {
    animation: none;
    background: linear-gradient(135deg, #DDF2FD 0%, #E5D9F2 100%);
  }
}
