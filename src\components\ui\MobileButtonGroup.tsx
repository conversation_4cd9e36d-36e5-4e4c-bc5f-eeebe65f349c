import React from 'react';
import { Button } from '@/components/ui/button';
import { LucideIcon } from 'lucide-react';

interface ButtonConfig {
  id: string;
  label: string;
  icon?: LucideIcon;
  onClick: () => void;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  className?: string;
  disabled?: boolean;
}

interface MobileButtonGroupProps {
  buttons: ButtonConfig[];
  layout?: 'grid' | 'flex' | 'stack';
  columns?: 2 | 3 | 4;
  size?: 'sm' | 'default' | 'lg';
  spacing?: 'tight' | 'normal' | 'loose';
  className?: string;
}

const MobileButtonGroup: React.FC<MobileButtonGroupProps> = ({
  buttons,
  layout = 'grid',
  columns = 2,
  size = 'sm',
  spacing = 'normal',
  className = ''
}) => {
  const getSpacingClass = () => {
    switch (spacing) {
      case 'tight': return 'gap-1';
      case 'normal': return 'gap-2';
      case 'loose': return 'gap-3';
      default: return 'gap-2';
    }
  };

  const getLayoutClass = () => {
    if (layout === 'grid') {
      return `grid grid-cols-${columns} ${getSpacingClass()}`;
    } else if (layout === 'flex') {
      return `flex flex-wrap ${getSpacingClass()}`;
    } else {
      return `flex flex-col ${getSpacingClass()}`;
    }
  };

  const getButtonClass = (button: ButtonConfig) => {
    const baseClass = layout === 'flex' ? 'flex-1 min-w-0' : 'w-full';
    return `${baseClass} text-xs sm:text-sm ${button.className || ''}`;
  };

  return (
    <div className={`${getLayoutClass()} ${className}`}>
      {buttons.map((button) => {
        const IconComponent = button.icon;
        return (
          <Button
            key={button.id}
            onClick={button.onClick}
            variant={button.variant || 'default'}
            size={size}
            disabled={button.disabled}
            className={getButtonClass(button)}
          >
            {IconComponent && (
              <IconComponent className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
            )}
            <span className="truncate">{button.label}</span>
          </Button>
        );
      })}
    </div>
  );
};

export default MobileButtonGroup;
