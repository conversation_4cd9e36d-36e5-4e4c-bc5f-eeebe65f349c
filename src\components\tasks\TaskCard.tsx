
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Calendar, Tag, MapPin, Clock, AlertCircle, Archive, 
  ChevronRight, ChevronDown, Link2, Repeat, Star, 
  Play, Pause, CheckCircle2, Circle, MoreHorizontal,
  Zap, User, Eye
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Task } from '../Tasks';

interface TaskCardProps {
  task: Task;
  onUpdate: (taskId: string, updates: Partial<Task>) => void;
  onDelete: (taskId: string) => void;
  onClick?: (task: Task) => void;
  allTasks: Task[];
  level?: number;
}

const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onUpdate,
  onDelete,
  onClick,
  allTasks,
  level = 0
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isTracking, setIsTracking] = useState(false);
  const [showActions, setShowActions] = useState(false);

  const subtasks = allTasks.filter(t => t.parentId === task.id);
  const hasSubtasks = subtasks.length > 0;
  const completedSubtasks = subtasks.filter(st => st.completed).length;

  const linkedTasks = allTasks.filter(t => task.linkedTasks?.includes(t.id));
  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && !task.completed;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-700 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-700 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-700 border-green-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'todo': return 'bg-gray-100 text-gray-700';
      case 'inprogress': return 'bg-blue-100 text-blue-700';
      case 'review': return 'bg-purple-100 text-purple-700';
      case 'done': return 'bg-green-100 text-green-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const toggleComplete = () => {
    onUpdate(task.id, { 
      completed: !task.completed,
      status: !task.completed ? 'done' : 'todo'
    });

    // Complete subtasks if parent is completed
    if (!task.completed && hasSubtasks) {
      subtasks.forEach(subtask => {
        if (!subtask.completed) {
          onUpdate(subtask.id, { completed: true, status: 'done' });
        }
      });
    }
  };

  const toggleTracking = () => {
    setIsTracking(!isTracking);
    // Here you would implement actual time tracking logic
  };

  const snoozeTask = (hours: number) => {
    const snoozeUntil = new Date();
    snoozeUntil.setHours(snoozeUntil.getHours() + hours);
    onUpdate(task.id, { snoozeUntil: snoozeUntil.toISOString() });
  };

  const archiveTask = () => {
    onUpdate(task.id, { isArchived: true });
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="group"
      style={{ marginLeft: `${level * 20}px` }}
    >
      <Card 
        className={`shadow-lg border-0 bg-white/90 backdrop-blur-sm transition-all cursor-pointer ${
          task.completed ? 'opacity-60' : ''
        } ${isOverdue ? 'border-l-4 border-l-red-500' : ''} ${
          level > 0 ? 'border-l-4 border-l-purple-300' : ''
        } hover:shadow-xl hover:bg-white/95`}
        style={{ borderLeftColor: task.colorLabel }}
        onClick={() => onClick?.(task)}
      >
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            {/* Expand/Collapse for subtasks */}
            {hasSubtasks && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsExpanded(!isExpanded);
                }}
                className="p-0 h-6 w-6 mt-1"
              >
                {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
              </Button>
            )}

            {/* Checkbox */}
            <div onClick={(e) => e.stopPropagation()} className="mt-1">
              <Checkbox
                checked={task.completed}
                onCheckedChange={toggleComplete}
                className="data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
              />
            </div>

            {/* Task Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  <h3 className={`font-medium text-lg ${
                    task.completed ? 'line-through text-gray-500' : 'text-gray-800'
                  }`}>
                    {task.title}
                    {task.aiScore && task.aiScore > 80 && (
                      <Zap className="inline w-4 h-4 ml-2 text-yellow-500" />
                    )}
                  </h3>
                  
                  {task.description && (
                    <p className={`text-sm mt-1 ${
                      task.completed ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      {task.description}
                    </p>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  {/* AI Score */}
                  {task.aiScore && (
                    <Badge variant="outline" className="text-xs">
                      🤖 {Math.round(task.aiScore)}
                    </Badge>
                  )}

                  {/* More Actions */}
                  <div className="relative">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowActions(!showActions);
                      }}
                      className="opacity-0 group-hover:opacity-100 transition-opacity p-1 h-8 w-8"
                    >
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>

                    {showActions && (
                      <div className="absolute right-0 top-8 bg-white rounded-lg shadow-lg border p-2 z-10 min-w-[150px]">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            snoozeTask(1);
                            setShowActions(false);
                          }}
                          className="w-full justify-start"
                        >
                          <Clock className="w-4 h-4 mr-2" />
                          Snooze 1h
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            archiveTask();
                            setShowActions(false);
                          }}
                          className="w-full justify-start"
                        >
                          <Archive className="w-4 h-4 mr-2" />
                          Archive
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            onDelete(task.id);
                            setShowActions(false);
                          }}
                          className="w-full justify-start text-red-600 hover:text-red-700"
                        >
                          Delete
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Task Meta Information */}
              <div className="flex flex-wrap items-center gap-3 mb-3">
                {/* Status */}
                <Badge variant="outline" className={`text-xs ${getStatusColor(task.status)}`}>
                  {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                </Badge>

                {/* Priority */}
                <Badge variant="outline" className={`text-xs ${getPriorityColor(task.priority)}`}>
                  {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                </Badge>

                {/* Due Date */}
                {task.dueDate && (
                  <div className={`flex items-center text-xs ${
                    isOverdue ? 'text-red-600' : 'text-gray-500'
                  }`}>
                    <Calendar className="w-3 h-3 mr-1" />
                    {new Date(task.dueDate).toLocaleDateString()}
                    {isOverdue && <AlertCircle className="w-3 h-3 ml-1" />}
                  </div>
                )}

                {/* Location */}
                {task.location && (
                  <div className="flex items-center text-xs text-gray-500">
                    <MapPin className="w-3 h-3 mr-1" />
                    {task.location}
                  </div>
                )}

                {/* Estimated Time */}
                {task.estimatedMinutes && (
                  <div className="flex items-center text-xs text-gray-500">
                    <Clock className="w-3 h-3 mr-1" />
                    {task.estimatedMinutes}m
                  </div>
                )}

                {/* Recurring */}
                {task.isRecurring && (
                  <div className="flex items-center text-xs text-purple-600">
                    <Repeat className="w-3 h-3 mr-1" />
                    {task.recurringPattern}
                  </div>
                )}

                {/* Delegated */}
                {task.delegatedTo && (
                  <div className="flex items-center text-xs text-blue-600">
                    <User className="w-3 h-3 mr-1" />
                    {task.delegatedTo}
                  </div>
                )}

                {/* Subtasks count */}
                {hasSubtasks && (
                  <Badge variant="outline" className="text-xs">
                    {completedSubtasks}/{subtasks.length} subtasks
                  </Badge>
                )}

                {/* Linked tasks */}
                {linkedTasks.length > 0 && (
                  <div className="flex items-center text-xs text-gray-500">
                    <Link2 className="w-3 h-3 mr-1" />
                    {linkedTasks.length} linked
                  </div>
                )}
              </div>

              {/* Tags */}
              {task.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-3">
                  {task.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      <Tag className="w-2 h-2 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              {/* Project */}
              {task.project && (
                <div className="text-xs text-gray-500 mb-2">
                  📁 {task.project}
                </div>
              )}

              {/* Action Buttons */}
              <div 
                className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={(e) => e.stopPropagation()}
              >
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleTracking}
                  className={`p-1 h-7 text-xs ${
                    isTracking ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'
                  }`}
                >
                  {isTracking ? (
                    <>
                      <Pause className="w-3 h-3 mr-1" />
                      Stop
                    </>
                  ) : (
                    <>
                      <Play className="w-3 h-3 mr-1" />
                      Start
                    </>
                  )}
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onClick?.(task)}
                  className="p-1 h-7 text-xs text-purple-600 hover:text-purple-700"
                >
                  <Eye className="w-3 h-3 mr-1" />
                  Details
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Subtasks */}
      {isExpanded && hasSubtasks && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-2 space-y-2"
        >
          {subtasks.map(subtask => (
            <TaskCard
              key={subtask.id}
              task={subtask}
              onUpdate={onUpdate}
              onDelete={onDelete}
              onClick={onClick}
              allTasks={allTasks}
              level={level + 1}
            />
          ))}
        </motion.div>
      )}
    </motion.div>
  );
};

export default TaskCard;
