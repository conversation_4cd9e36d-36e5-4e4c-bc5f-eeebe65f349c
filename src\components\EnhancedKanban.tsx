import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>, CardContent, CardHeader, CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue 
} from '@/components/ui/select';
import {
  Plus, Settings, Filter, Search, Calendar, Clock, User,
  Target, TrendingUp, BarChart3, Zap, Bell, Archive,
  ChevronDown, ChevronRight, Flag, Tag, Link, MessageSquare,
  Play, Pause, RotateCcw, CheckCircle, Circle, AlertCircle,
  Users, Briefcase, Layers, Activity, Timer, Star,
  Edit, Trash2, Save, X, Copy, Palette, Layout,
  Columns, Workflow, FileText, Sliders, Eye, EyeOff,
  ArrowUp, ArrowDown, ArrowLeft, ArrowRight, MoreHorizontal
} from 'lucide-react';

// Enhanced interfaces for professional project management
interface ProjectTask {
  id: string;
  title: string;
  description: string;
  status: 'backlog' | 'todo' | 'in-progress' | 'review' | 'testing' | 'done' | 'blocked';
  priority: 'low' | 'medium' | 'high' | 'urgent' | 'critical';
  assignee?: string;
  reporter: string;
  dueDate?: string;
  startDate?: string;
  tags: string[];
  labels: string[];
  project: string;
  epic?: string;
  storyPoints?: number;
  estimatedHours?: number;
  actualHours?: number;
  timeTracking: {
    isActive: boolean;
    startTime?: string;
    totalTime: number;
    sessions: Array<{
      start: string;
      end?: string;
      duration: number;
    }>;
  };
  subtasks: Array<{
    id: string;
    title: string;
    completed: boolean;
    assignee?: string;
  }>;
  dependencies: string[];
  blockers: Array<{
    id: string;
    reason: string;
    createdAt: string;
    resolvedAt?: string;
  }>;
  comments: Array<{
    id: string;
    text: string;
    author: string;
    timestamp: string;
    type: 'comment' | 'status_change' | 'assignment';
  }>;
  attachments: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
    size: number;
  }>;
  customFields: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

interface KanbanColumn {
  id: string;
  title: string;
  color: string;
  wipLimit?: number;
  isCollapsed: boolean;
  position: number;
  type: 'standard' | 'done' | 'blocked';
  customIcon?: string;
  description?: string;
  autoAssignee?: string;
  rules?: ColumnRule[];
}

interface ColumnRule {
  id: string;
  type: 'auto_assign' | 'auto_tag' | 'auto_priority' | 'time_limit' | 'notification';
  condition: string;
  action: string;
  value: any;
  enabled: boolean;
}

interface CardTemplate {
  id: string;
  name: string;
  description: string;
  fields: TemplateField[];
  defaultValues: Record<string, any>;
  category: string;
  isDefault: boolean;
}

interface TemplateField {
  id: string;
  name: string;
  type: 'text' | 'textarea' | 'select' | 'multiselect' | 'date' | 'number' | 'checkbox' | 'user' | 'priority';
  label: string;
  required: boolean;
  options?: string[];
  defaultValue?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

interface WorkflowState {
  id: string;
  name: string;
  color: string;
  type: 'start' | 'intermediate' | 'end' | 'blocked';
  transitions: WorkflowTransition[];
  rules: StateRule[];
  position: { x: number; y: number };
}

interface WorkflowTransition {
  id: string;
  fromState: string;
  toState: string;
  name: string;
  conditions?: TransitionCondition[];
  actions?: TransitionAction[];
  requiredRole?: string;
}

interface TransitionCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
  value: any;
}

interface TransitionAction {
  type: 'assign_user' | 'add_tag' | 'set_priority' | 'send_notification' | 'create_subtask';
  value: any;
}

interface StateRule {
  id: string;
  type: 'time_limit' | 'required_fields' | 'auto_assign' | 'notification';
  value: any;
  enabled: boolean;
}

interface KanbanCustomization {
  id: string;
  name: string;
  description: string;
  columns: KanbanColumn[];
  workflows: WorkflowState[];
  cardTemplates: CardTemplate[];
  globalSettings: {
    theme: 'light' | 'dark' | 'auto';
    compactMode: boolean;
    showAvatars: boolean;
    showDueDates: boolean;
    showProgress: boolean;
    showTags: boolean;
    showPriority: boolean;
    showEstimates: boolean;
    autoSave: boolean;
    notifications: boolean;
    swimlanes: 'none' | 'assignee' | 'priority' | 'project' | 'epic';
    groupBy: 'none' | 'assignee' | 'priority' | 'project' | 'epic' | 'tags';
    sortBy: 'created' | 'updated' | 'priority' | 'due_date' | 'title';
    filterPresets: FilterPreset[];
  };
  permissions: {
    canEditColumns: boolean;
    canEditWorkflow: boolean;
    canEditTemplates: boolean;
    canDeleteTasks: boolean;
    canAssignTasks: boolean;
    canChangeStatus: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

interface FilterPreset {
  id: string;
  name: string;
  filters: {
    assignee?: string[];
    priority?: string[];
    status?: string[];
    tags?: string[];
    project?: string[];
    dateRange?: { start: string; end: string };
  };
  isDefault: boolean;
}

interface Project {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  owner: string;
  members: string[];
  status: 'active' | 'on-hold' | 'completed' | 'archived';
  startDate: string;
  endDate?: string;
  progress: number;
  budget?: number;
  spentBudget?: number;
  tags: string[];
  customFields: Record<string, any>;
}

interface Epic {
  id: string;
  title: string;
  description: string;
  project: string;
  status: 'planning' | 'in-progress' | 'done';
  progress: number;
  startDate?: string;
  endDate?: string;
  color: string;
}

interface TeamMember {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: string;
  department: string;
  capacity: number; // hours per week
  currentWorkload: number;
  skills: string[];
}

const EnhancedKanban: React.FC = () => {
  // State management
  const [tasks, setTasks] = useState<ProjectTask[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [epics, setEpics] = useState<Epic[]>([]);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [columns, setColumns] = useState<KanbanColumn[]>([
    { id: 'backlog', title: 'Backlog', color: 'bg-gray-100', position: 0, isCollapsed: false, type: 'standard' },
    { id: 'todo', title: 'To Do', color: 'bg-blue-100', position: 1, isCollapsed: false, type: 'standard', wipLimit: 5 },
    { id: 'in-progress', title: 'In Progress', color: 'bg-yellow-100', position: 2, isCollapsed: false, type: 'standard', wipLimit: 3 },
    { id: 'review', title: 'Review', color: 'bg-purple-100', position: 3, isCollapsed: false, type: 'standard', wipLimit: 2 },
    { id: 'testing', title: 'Testing', color: 'bg-orange-100', position: 4, isCollapsed: false, type: 'standard' },
    { id: 'done', title: 'Done', color: 'bg-green-100', position: 5, isCollapsed: false, type: 'done' },
    { id: 'blocked', title: 'Blocked', color: 'bg-red-100', position: 6, isCollapsed: false, type: 'blocked' }
  ]);

  // UI State
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [selectedEpic, setSelectedEpic] = useState<string>('all');
  const [selectedAssignee, setSelectedAssignee] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'kanban' | 'list' | 'calendar' | 'timeline'>('kanban');
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [selectedTask, setSelectedTask] = useState<ProjectTask | null>(null);
  const [draggedTask, setDraggedTask] = useState<ProjectTask | null>(null);

  // Customization State
  const [showCustomization, setShowCustomization] = useState(false);
  const [customizationTab, setCustomizationTab] = useState<'columns' | 'workflow' | 'templates' | 'settings'>('columns');
  const [currentCustomization, setCurrentCustomization] = useState<KanbanCustomization>({
    id: 'default',
    name: 'Default Board',
    description: 'Default Kanban board configuration',
    columns: columns,
    workflows: [],
    cardTemplates: [],
    globalSettings: {
      theme: 'light',
      compactMode: false,
      showAvatars: true,
      showDueDates: true,
      showProgress: true,
      showTags: true,
      showPriority: true,
      showEstimates: true,
      autoSave: true,
      notifications: true,
      swimlanes: 'none',
      groupBy: 'none',
      sortBy: 'created',
      filterPresets: []
    },
    permissions: {
      canEditColumns: true,
      canEditWorkflow: true,
      canEditTemplates: true,
      canDeleteTasks: true,
      canAssignTasks: true,
      canChangeStatus: true
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  });
  const [editingColumn, setEditingColumn] = useState<KanbanColumn | null>(null);
  const [editingTemplate, setEditingTemplate] = useState<CardTemplate | null>(null);
  const [showColumnForm, setShowColumnForm] = useState(false);
  const [showTemplateForm, setShowTemplateForm] = useState(false);
  const [savedCustomizations, setSavedCustomizations] = useState<KanbanCustomization[]>([]);

  // Form state for new tasks
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    priority: 'medium' as ProjectTask['priority'],
    assignee: '',
    project: '',
    epic: '',
    dueDate: '',
    estimatedHours: 0,
    storyPoints: 0,
    tags: [] as string[],
    labels: [] as string[]
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const savedTasks = JSON.parse(localStorage.getItem('enhanced-kanban-tasks') || '[]');
    const savedProjects = JSON.parse(localStorage.getItem('enhanced-kanban-projects') || '[]');
    const savedEpics = JSON.parse(localStorage.getItem('enhanced-kanban-epics') || '[]');
    const savedMembers = JSON.parse(localStorage.getItem('enhanced-kanban-members') || '[]');
    
    setTasks(savedTasks);
    setProjects(savedProjects);
    setEpics(savedEpics);
    setTeamMembers(savedMembers);

    // Initialize with sample data if empty
    if (savedProjects.length === 0) {
      initializeSampleData();
    }
  };

  const saveData = () => {
    localStorage.setItem('enhanced-kanban-tasks', JSON.stringify(tasks));
    localStorage.setItem('enhanced-kanban-projects', JSON.stringify(projects));
    localStorage.setItem('enhanced-kanban-epics', JSON.stringify(epics));
    localStorage.setItem('enhanced-kanban-members', JSON.stringify(teamMembers));
  };

  const initializeSampleData = () => {
    // Start with empty arrays - users will create their own projects and team members
    const sampleProjects: Project[] = [];
    const sampleMembers: TeamMember[] = [];

    setProjects(sampleProjects);
    setTeamMembers(sampleMembers);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen p-4 pb-24 bg-gradient-to-br from-blue-50 via-white to-purple-50"
    >
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Enhanced Kanban Board</h1>
          <p className="text-gray-600">Professional project management with advanced features</p>
        </div>
        
        <div className="flex flex-wrap items-center space-x-2">
          <Button 
            onClick={() => setShowTaskForm(true)}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Task
          </Button>
          <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
            <Filter className="w-4 h-4 mr-2" />
            Filters
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowCustomization(true)}
            className="bg-gradient-to-r from-purple-50 to-pink-50 hover:from-purple-100 hover:to-pink-100 border-purple-200"
          >
            <Palette className="w-4 h-4 mr-2" />
            Customize
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* View Mode Tabs */}
      <Tabs value={viewMode} onValueChange={(value: any) => setViewMode(value)} className="mb-6">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-4">
          <TabsTrigger value="kanban" className="flex items-center text-xs md:text-sm">
            <Layers className="w-4 h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Kanban</span>
            <span className="sm:hidden">Board</span>
          </TabsTrigger>
          <TabsTrigger value="list" className="flex items-center text-xs md:text-sm">
            <BarChart3 className="w-4 h-4 mr-1 md:mr-2" />
            List
          </TabsTrigger>
          <TabsTrigger value="calendar" className="flex items-center text-xs md:text-sm">
            <Calendar className="w-4 h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Calendar</span>
            <span className="sm:hidden">Cal</span>
          </TabsTrigger>
          <TabsTrigger value="timeline" className="flex items-center text-xs md:text-sm">
            <Activity className="w-4 h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Timeline</span>
            <span className="sm:hidden">Time</span>
          </TabsTrigger>
        </TabsList>

        {/* Filters Panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 p-4 bg-white rounded-lg border shadow-sm"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="search">Search Tasks</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <Input
                      id="search"
                      placeholder="Search by title, description..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="project-filter">Project</Label>
                  <Select value={selectedProject} onValueChange={setSelectedProject}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Projects" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Projects</SelectItem>
                      {projects.map(project => (
                        <SelectItem key={project.id} value={project.id}>
                          {project.icon} {project.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="assignee-filter">Assignee</Label>
                  <Select value={selectedAssignee} onValueChange={setSelectedAssignee}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Assignees" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Assignees</SelectItem>
                      <SelectItem value="unassigned">Unassigned</SelectItem>
                      {teamMembers.map(member => (
                        <SelectItem key={member.id} value={member.id}>
                          {member.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="epic-filter">Epic</Label>
                  <Select value={selectedEpic} onValueChange={setSelectedEpic}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Epics" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Epics</SelectItem>
                      <SelectItem value="no-epic">No Epic</SelectItem>
                      {epics.map(epic => (
                        <SelectItem key={epic.id} value={epic.id}>
                          {epic.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Kanban Board View */}
        <TabsContent value="kanban" className="space-y-6">
          <div className="flex overflow-x-auto pb-4 space-x-4">
            {columns
              .sort((a, b) => a.position - b.position)
              .map(column => {
                const columnTasks = getFilteredTasksByStatus(column.id as ProjectTask['status']);
                const isOverLimit = column.wipLimit && columnTasks.length > column.wipLimit;

                return (
                  <div
                    key={column.id}
                    className={`flex-shrink-0 w-80 ${column.color} rounded-lg p-4 ${
                      isOverLimit ? 'ring-2 ring-red-400' : ''
                    }`}
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, column.id as ProjectTask['status'])}
                  >
                    {/* Column Header */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleColumnCollapse(column.id)}
                          className="p-1"
                        >
                          {column.isCollapsed ? (
                            <ChevronRight className="w-4 h-4" />
                          ) : (
                            <ChevronDown className="w-4 h-4" />
                          )}
                        </Button>
                        <h3 className="font-semibold text-gray-800">{column.title}</h3>
                        <Badge variant="secondary" className={isOverLimit ? 'bg-red-100 text-red-800' : ''}>
                          {columnTasks.length}
                          {column.wipLimit && ` / ${column.wipLimit}`}
                        </Badge>
                      </div>

                      <div className="flex items-center space-x-1">
                        {column.type === 'blocked' && (
                          <AlertCircle className="w-4 h-4 text-red-500" />
                        )}
                        <Button variant="ghost" size="sm" className="p-1">
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Column Content */}
                    {!column.isCollapsed && (
                      <div className="space-y-3 min-h-[500px]">
                        <AnimatePresence>
                          {columnTasks.map(task => (
                            <TaskCard
                              key={task.id}
                              task={task}
                              onEdit={setSelectedTask}
                              onDragStart={() => setDraggedTask(task)}
                              teamMembers={teamMembers}
                              projects={projects}
                            />
                          ))}
                        </AnimatePresence>

                        {columnTasks.length === 0 && (
                          <div className="text-center py-8 text-gray-400">
                            <div className="text-sm">No tasks</div>
                            <div className="text-xs">Drag tasks here</div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
        </TabsContent>

        {/* List View */}
        <TabsContent value="list" className="space-y-4">
          <div className="bg-white rounded-lg border">
            <div className="p-4 border-b">
              <h3 className="font-semibold">Task List</h3>
            </div>
            <div className="divide-y">
              {getFilteredTasks().map(task => (
                <TaskListItem
                  key={task.id}
                  task={task}
                  onEdit={setSelectedTask}
                  teamMembers={teamMembers}
                  projects={projects}
                />
              ))}
            </div>
          </div>
        </TabsContent>

        {/* Calendar View */}
        <TabsContent value="calendar" className="space-y-4">
          <div className="bg-white rounded-lg border p-6">
            <h3 className="text-lg font-semibold mb-4">Calendar View</h3>
            <p className="text-gray-600">Calendar view coming soon...</p>
          </div>
        </TabsContent>

        {/* Timeline View */}
        <TabsContent value="timeline" className="space-y-4">
          <div className="bg-white rounded-lg border p-6">
            <h3 className="text-lg font-semibold mb-4">Timeline View</h3>
            <p className="text-gray-600">Timeline view coming soon...</p>
          </div>
        </TabsContent>
      </Tabs>

      {/* Customization Modal */}
      <AnimatePresence>
        {showCustomization && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowCustomization(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between p-6 border-b">
                <div>
                  <h2 className="text-2xl font-bold text-gray-800">Customize Kanban Board</h2>
                  <p className="text-gray-600">Personalize your board layout, columns, and workflows</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button onClick={saveCustomization} className="bg-green-600 hover:bg-green-700">
                    <Save className="w-4 h-4 mr-2" />
                    Save
                  </Button>
                  <Button variant="outline" onClick={() => setShowCustomization(false)}>
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <div className="flex h-[calc(90vh-120px)]">
                {/* Customization Sidebar */}
                <div className="w-64 border-r bg-gray-50 p-4">
                  <div className="space-y-2">
                    <Button
                      variant={customizationTab === 'columns' ? 'default' : 'ghost'}
                      className="w-full justify-start"
                      onClick={() => setCustomizationTab('columns')}
                    >
                      <Columns className="w-4 h-4 mr-2" />
                      Columns
                    </Button>
                    <Button
                      variant={customizationTab === 'workflow' ? 'default' : 'ghost'}
                      className="w-full justify-start"
                      onClick={() => setCustomizationTab('workflow')}
                    >
                      <Workflow className="w-4 h-4 mr-2" />
                      Workflow
                    </Button>
                    <Button
                      variant={customizationTab === 'templates' ? 'default' : 'ghost'}
                      className="w-full justify-start"
                      onClick={() => setCustomizationTab('templates')}
                    >
                      <FileText className="w-4 h-4 mr-2" />
                      Templates
                    </Button>
                    <Button
                      variant={customizationTab === 'settings' ? 'default' : 'ghost'}
                      className="w-full justify-start"
                      onClick={() => setCustomizationTab('settings')}
                    >
                      <Sliders className="w-4 h-4 mr-2" />
                      Settings
                    </Button>
                  </div>

                  <div className="mt-6 pt-6 border-t">
                    <h4 className="font-semibold text-gray-700 mb-3">Saved Configurations</h4>
                    <div className="space-y-2">
                      {savedCustomizations.map((config) => (
                        <div key={config.id} className="flex items-center justify-between">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="flex-1 justify-start text-left"
                            onClick={() => loadCustomization(config)}
                          >
                            {config.name}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="p-1"
                            onClick={() => {
                              setSavedCustomizations(savedCustomizations.filter(c => c.id !== config.id));
                            }}
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full mt-2"
                      onClick={createNewCustomization}
                    >
                      <Plus className="w-3 h-3 mr-1" />
                      New Config
                    </Button>
                  </div>
                </div>

                {/* Customization Content */}
                <div className="flex-1 p-6 overflow-y-auto">
                  {customizationTab === 'columns' && (
                    <div className="space-y-6">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold">Column Configuration</h3>
                        <Button onClick={() => setShowColumnForm(true)}>
                          <Plus className="w-4 h-4 mr-2" />
                          Add Column
                        </Button>
                      </div>

                      <div className="grid gap-4">
                        {columns.map((column, index) => (
                          <div key={column.id} className="border rounded-lg p-4 bg-white">
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center space-x-3">
                                <div className={`w-4 h-4 rounded ${column.color}`} />
                                <h4 className="font-medium">{column.title}</h4>
                                <Badge variant="outline">{column.type}</Badge>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setEditingColumn(column)}
                                >
                                  <Edit className="w-3 h-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => deleteColumn(column.id)}
                                  disabled={columns.length <= 1}
                                >
                                  <Trash2 className="w-3 h-3" />
                                </Button>
                                <div className="flex">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => reorderColumns(index, Math.max(0, index - 1))}
                                    disabled={index === 0}
                                  >
                                    <ArrowLeft className="w-3 h-3" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => reorderColumns(index, Math.min(columns.length - 1, index + 1))}
                                    disabled={index === columns.length - 1}
                                  >
                                    <ArrowRight className="w-3 h-3" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                              <div>Position: {column.position + 1}</div>
                              <div>WIP Limit: {column.wipLimit || 'None'}</div>
                              <div>Tasks: {getFilteredTasksByStatus(column.id as any).length}</div>
                              <div>Collapsed: {column.isCollapsed ? 'Yes' : 'No'}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {customizationTab === 'settings' && (
                    <div className="space-y-6">
                      <h3 className="text-lg font-semibold">Global Settings</h3>

                      <div className="grid grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <h4 className="font-medium">Display Options</h4>
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <Label>Compact Mode</Label>
                              <input
                                type="checkbox"
                                checked={currentCustomization.globalSettings.compactMode}
                                onChange={(e) => updateGlobalSettings({ compactMode: e.target.checked })}
                                className="rounded"
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label>Show Avatars</Label>
                              <input
                                type="checkbox"
                                checked={currentCustomization.globalSettings.showAvatars}
                                onChange={(e) => updateGlobalSettings({ showAvatars: e.target.checked })}
                                className="rounded"
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label>Show Due Dates</Label>
                              <input
                                type="checkbox"
                                checked={currentCustomization.globalSettings.showDueDates}
                                onChange={(e) => updateGlobalSettings({ showDueDates: e.target.checked })}
                                className="rounded"
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label>Show Progress</Label>
                              <input
                                type="checkbox"
                                checked={currentCustomization.globalSettings.showProgress}
                                onChange={(e) => updateGlobalSettings({ showProgress: e.target.checked })}
                                className="rounded"
                              />
                            </div>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <h4 className="font-medium">Grouping & Sorting</h4>
                          <div className="space-y-3">
                            <div>
                              <Label>Group By</Label>
                              <Select
                                value={currentCustomization.globalSettings.groupBy}
                                onValueChange={(value) => updateGlobalSettings({ groupBy: value as any })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="none">None</SelectItem>
                                  <SelectItem value="assignee">Assignee</SelectItem>
                                  <SelectItem value="priority">Priority</SelectItem>
                                  <SelectItem value="project">Project</SelectItem>
                                  <SelectItem value="epic">Epic</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label>Sort By</Label>
                              <Select
                                value={currentCustomization.globalSettings.sortBy}
                                onValueChange={(value) => updateGlobalSettings({ sortBy: value as any })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="created">Created Date</SelectItem>
                                  <SelectItem value="updated">Updated Date</SelectItem>
                                  <SelectItem value="priority">Priority</SelectItem>
                                  <SelectItem value="due_date">Due Date</SelectItem>
                                  <SelectItem value="title">Title</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );

  // Customization Functions
  const saveCustomization = () => {
    const updatedCustomization = {
      ...currentCustomization,
      columns: columns,
      updatedAt: new Date().toISOString()
    };

    const existingIndex = savedCustomizations.findIndex(c => c.id === updatedCustomization.id);
    if (existingIndex >= 0) {
      const updated = [...savedCustomizations];
      updated[existingIndex] = updatedCustomization;
      setSavedCustomizations(updated);
    } else {
      setSavedCustomizations([...savedCustomizations, updatedCustomization]);
    }

    setCurrentCustomization(updatedCustomization);
    localStorage.setItem('kanban-customizations', JSON.stringify(savedCustomizations));
    localStorage.setItem('current-kanban-customization', JSON.stringify(updatedCustomization));
  };

  const loadCustomization = (customization: KanbanCustomization) => {
    setCurrentCustomization(customization);
    setColumns(customization.columns);
    localStorage.setItem('current-kanban-customization', JSON.stringify(customization));
  };

  const createNewCustomization = () => {
    const newCustomization: KanbanCustomization = {
      id: `custom-${Date.now()}`,
      name: `Custom Board ${savedCustomizations.length + 1}`,
      description: 'Custom Kanban board configuration',
      columns: [...columns],
      workflows: [],
      cardTemplates: [],
      globalSettings: { ...currentCustomization.globalSettings },
      permissions: { ...currentCustomization.permissions },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setSavedCustomizations([...savedCustomizations, newCustomization]);
    setCurrentCustomization(newCustomization);
  };

  const addColumn = (column: Omit<KanbanColumn, 'id' | 'position'>) => {
    const newColumn: KanbanColumn = {
      ...column,
      id: `column-${Date.now()}`,
      position: columns.length
    };
    setColumns([...columns, newColumn]);
  };

  const updateColumn = (columnId: string, updates: Partial<KanbanColumn>) => {
    setColumns(columns.map(col =>
      col.id === columnId ? { ...col, ...updates } : col
    ));
  };

  const deleteColumn = (columnId: string) => {
    if (columns.length <= 1) return; // Prevent deleting all columns

    // Move tasks from deleted column to first available column
    const remainingColumns = columns.filter(col => col.id !== columnId);
    const targetColumn = remainingColumns[0];

    setTasks(tasks.map(task =>
      task.status === columnId ? { ...task, status: targetColumn.id as any } : task
    ));

    setColumns(remainingColumns.map((col, index) => ({ ...col, position: index })));
  };

  const reorderColumns = (startIndex: number, endIndex: number) => {
    const result = Array.from(columns);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);

    const reorderedColumns = result.map((col, index) => ({ ...col, position: index }));
    setColumns(reorderedColumns);
  };

  const createCardTemplate = (template: Omit<CardTemplate, 'id'>) => {
    const newTemplate: CardTemplate = {
      ...template,
      id: `template-${Date.now()}`
    };

    const updatedCustomization = {
      ...currentCustomization,
      cardTemplates: [...currentCustomization.cardTemplates, newTemplate]
    };
    setCurrentCustomization(updatedCustomization);
  };

  const updateGlobalSettings = (settings: Partial<KanbanCustomization['globalSettings']>) => {
    const updatedCustomization = {
      ...currentCustomization,
      globalSettings: { ...currentCustomization.globalSettings, ...settings }
    };
    setCurrentCustomization(updatedCustomization);
  };

  // Helper functions
  function getFilteredTasks(): ProjectTask[] {
    return tasks.filter(task => {
      const matchesSearch = !searchQuery ||
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.description.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesProject = selectedProject === 'all' || task.project === selectedProject;
      const matchesAssignee = selectedAssignee === 'all' ||
        (selectedAssignee === 'unassigned' && !task.assignee) ||
        task.assignee === selectedAssignee;
      const matchesEpic = selectedEpic === 'all' ||
        (selectedEpic === 'no-epic' && !task.epic) ||
        task.epic === selectedEpic;

      return matchesSearch && matchesProject && matchesAssignee && matchesEpic;
    });
  }

  function getFilteredTasksByStatus(status: ProjectTask['status']): ProjectTask[] {
    return getFilteredTasks().filter(task => task.status === status);
  }

  function handleDragOver(e: React.DragEvent) {
    e.preventDefault();
  }

  function handleDrop(e: React.DragEvent, status: ProjectTask['status']) {
    e.preventDefault();
    if (!draggedTask) return;

    const updatedTasks = tasks.map(task =>
      task.id === draggedTask.id
        ? { ...task, status, updatedAt: new Date().toISOString() }
        : task
    );

    setTasks(updatedTasks);
    setDraggedTask(null);
    saveData();
  }

  function toggleColumnCollapse(columnId: string) {
    setColumns(columns.map(col =>
      col.id === columnId ? { ...col, isCollapsed: !col.isCollapsed } : col
    ));
  }
};

// Task Card Component
interface TaskCardProps {
  task: ProjectTask;
  onEdit: (task: ProjectTask) => void;
  onDragStart: () => void;
  teamMembers: TeamMember[];
  projects: Project[];
}

const TaskCard: React.FC<TaskCardProps> = ({ task, onEdit, onDragStart, teamMembers, projects }) => {
  const [isTracking, setIsTracking] = useState(task.timeTracking.isActive);

  const assignee = teamMembers.find(member => member.id === task.assignee);
  const project = projects.find(proj => proj.id === task.project);

  const priorityColors = {
    low: 'bg-gray-100 text-gray-800',
    medium: 'bg-blue-100 text-blue-800',
    high: 'bg-orange-100 text-orange-800',
    urgent: 'bg-red-100 text-red-800',
    critical: 'bg-red-200 text-red-900'
  };

  const completedSubtasks = task.subtasks.filter(st => st.completed).length;
  const progressPercentage = task.subtasks.length > 0
    ? (completedSubtasks / task.subtasks.length) * 100
    : 0;

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      draggable
      onDragStart={onDragStart}
      onClick={() => onEdit(task)}
      className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-all cursor-pointer border border-gray-200"
    >
      {/* Task Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-sm text-gray-900 truncate">{task.title}</h4>
          {task.description && (
            <p className="text-xs text-gray-600 mt-1 line-clamp-2">{task.description}</p>
          )}
        </div>
        <Badge className={`ml-2 text-xs ${priorityColors[task.priority]}`}>
          {task.priority}
        </Badge>
      </div>

      {/* Task Meta */}
      <div className="space-y-2">
        {/* Project and Epic */}
        <div className="flex items-center space-x-2">
          {project && (
            <Badge variant="outline" className="text-xs">
              {project.icon} {project.name}
            </Badge>
          )}
          {task.epic && (
            <Badge variant="secondary" className="text-xs">
              Epic: {task.epic}
            </Badge>
          )}
        </div>

        {/* Tags */}
        {task.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {task.tags.slice(0, 3).map(tag => (
              <Badge key={tag} variant="outline" className="text-xs">
                <Tag className="w-3 h-3 mr-1" />
                {tag}
              </Badge>
            ))}
            {task.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{task.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Progress Bar */}
        {task.subtasks.length > 0 && (
          <div>
            <div className="flex justify-between text-xs text-gray-600 mb-1">
              <span>Subtasks</span>
              <span>{completedSubtasks}/{task.subtasks.length}</span>
            </div>
            <Progress value={progressPercentage} className="h-1" />
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center space-x-2">
            {/* Assignee */}
            {assignee && (
              <div className="flex items-center space-x-1">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                  {assignee.name.charAt(0)}
                </div>
              </div>
            )}

            {/* Story Points */}
            {task.storyPoints && (
              <Badge variant="outline" className="text-xs">
                {task.storyPoints} SP
              </Badge>
            )}
          </div>

          <div className="flex items-center space-x-1 text-xs text-gray-500">
            {/* Time Tracking */}
            {task.timeTracking.totalTime > 0 && (
              <div className="flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                {Math.round(task.timeTracking.totalTime / 60)}h
              </div>
            )}

            {/* Due Date */}
            {task.dueDate && (
              <div className="flex items-center">
                <Calendar className="w-3 h-3 mr-1" />
                {new Date(task.dueDate).toLocaleDateString()}
              </div>
            )}

            {/* Comments */}
            {task.comments.length > 0 && (
              <div className="flex items-center">
                <MessageSquare className="w-3 h-3 mr-1" />
                {task.comments.length}
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Task List Item Component
interface TaskListItemProps {
  task: ProjectTask;
  onEdit: (task: ProjectTask) => void;
  teamMembers: TeamMember[];
  projects: Project[];
}

const TaskListItem: React.FC<TaskListItemProps> = ({ task, onEdit, teamMembers, projects }) => {
  const assignee = teamMembers.find(member => member.id === task.assignee);
  const project = projects.find(proj => proj.id === task.project);

  const priorityColors = {
    low: 'text-gray-600',
    medium: 'text-blue-600',
    high: 'text-orange-600',
    urgent: 'text-red-600',
    critical: 'text-red-800'
  };

  return (
    <div
      onClick={() => onEdit(task)}
      className="p-4 hover:bg-gray-50 cursor-pointer transition-colors"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 flex-1 min-w-0">
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-gray-900 truncate">{task.title}</h4>
            <p className="text-sm text-gray-600 truncate">{task.description}</p>
          </div>

          <div className="flex items-center space-x-3">
            <Badge className={priorityColors[task.priority]}>
              {task.priority}
            </Badge>

            {project && (
              <span className="text-sm text-gray-600">
                {project.icon} {project.name}
              </span>
            )}

            {assignee && (
              <div className="flex items-center space-x-1">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                  {assignee.name.charAt(0)}
                </div>
                <span className="text-sm text-gray-600">{assignee.name}</span>
              </div>
            )}

            {task.dueDate && (
              <span className="text-sm text-gray-500">
                {new Date(task.dueDate).toLocaleDateString()}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedKanban;
