import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>, CardContent, CardHeader, CardTitle 
} from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import {
  TrendingUp, TrendingDown, Activity, Clock, Target,
  CheckCircle, AlertCircle, Calendar, BarChart3,
  PieChart, LineChart, Users, Briefcase, Zap,
  Award, Star, Trophy, Crown, Diamond, Flame,
  ArrowUp, ArrowDown, Minus, Eye, Timer,
  Brain, Heart, Dumbbell, Book, Coffee, Lightbulb,
  Settings, Plus, Edit, Trash2, Refresh<PERSON><PERSON>, Wifi,
  WifiOff, Play, Pause, RotateCcw, Maximize2,
  Minimize2, Grid, Layout, Sliders, Bell, BellOff
} from 'lucide-react';

// Productivity Analytics Interfaces
interface ProductivityMetric {
  id: string;
  name: string;
  value: number;
  target: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  changePercentage: number;
  category: 'time' | 'tasks' | 'goals' | 'health' | 'learning';
  period: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  history: Array<{
    date: string;
    value: number;
  }>;
}

interface KPI {
  id: string;
  name: string;
  description: string;
  value: number;
  target: number;
  unit: string;
  status: 'excellent' | 'good' | 'warning' | 'critical';
  category: string;
  weight: number; // Importance weight for overall score
  formula?: string;
  benchmarks: {
    excellent: number;
    good: number;
    warning: number;
  };
}

interface ProductivityScore {
  overall: number;
  categories: {
    timeManagement: number;
    taskCompletion: number;
    goalAchievement: number;
    healthWellness: number;
    learningGrowth: number;
  };
  trend: 'improving' | 'declining' | 'stable';
  insights: string[];
  recommendations: string[];
}

interface TimeBlock {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  duration: number; // minutes
  category: 'deep-work' | 'meetings' | 'admin' | 'break' | 'learning' | 'exercise';
  productivity: number; // 1-10 scale
  energy: number; // 1-10 scale
  focus: number; // 1-10 scale
  description: string;
  tags: string[];
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: string;
  points: number;
  unlockedAt: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  requirements: string[];
}

// Real-time Dashboard Interfaces
interface DashboardWidget {
  id: string;
  type: 'metric' | 'chart' | 'kpi' | 'progress' | 'list' | 'calendar' | 'timer' | 'custom';
  title: string;
  size: 'small' | 'medium' | 'large' | 'full';
  position: { x: number; y: number; w: number; h: number };
  config: {
    dataSource: string;
    refreshInterval: number; // seconds
    showHeader: boolean;
    showBorder: boolean;
    theme: 'light' | 'dark' | 'auto';
    customSettings: Record<string, any>;
  };
  isVisible: boolean;
  isLocked: boolean;
  lastUpdated: string;
}

interface RealTimeData {
  timestamp: string;
  metrics: Record<string, number>;
  events: DashboardEvent[];
  alerts: DashboardAlert[];
  status: 'connected' | 'disconnected' | 'error';
}

interface DashboardEvent {
  id: string;
  type: 'task_completed' | 'goal_achieved' | 'milestone_reached' | 'streak_broken' | 'new_record';
  title: string;
  description: string;
  timestamp: string;
  category: string;
  importance: 'low' | 'medium' | 'high' | 'critical';
  data: Record<string, any>;
}

interface DashboardAlert {
  id: string;
  type: 'warning' | 'error' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  actions?: Array<{
    label: string;
    action: string;
    variant: 'primary' | 'secondary' | 'danger';
  }>;
}

interface DashboardLayout {
  id: string;
  name: string;
  description: string;
  widgets: DashboardWidget[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

interface LiveMetric {
  id: string;
  name: string;
  value: number;
  previousValue: number;
  change: number;
  changePercent: number;
  trend: 'up' | 'down' | 'stable';
  unit: string;
  format: 'number' | 'percentage' | 'currency' | 'time' | 'bytes';
  color: string;
  icon: string;
  isRealTime: boolean;
  lastUpdated: string;
  history: Array<{ timestamp: string; value: number }>;
}

const ProductivityDashboard: React.FC = () => {
  // State Management
  const [metrics, setMetrics] = useState<ProductivityMetric[]>([]);
  const [kpis, setKPIs] = useState<KPI[]>([]);
  const [productivityScore, setProductivityScore] = useState<ProductivityScore | null>(null);
  const [timeBlocks, setTimeBlocks] = useState<TimeBlock[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  
  // UI State
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month' | 'quarter'>('week');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [activeTab, setActiveTab] = useState<'overview' | 'time' | 'performance' | 'insights'>('overview');

  // Real-time State
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [realTimeData, setRealTimeData] = useState<RealTimeData>({
    timestamp: new Date().toISOString(),
    metrics: {},
    events: [],
    alerts: [],
    status: 'connected'
  });
  const [liveMetrics, setLiveMetrics] = useState<LiveMetric[]>([]);
  const [dashboardWidgets, setDashboardWidgets] = useState<DashboardWidget[]>([]);
  const [currentLayout, setCurrentLayout] = useState<DashboardLayout | null>(null);
  const [savedLayouts, setSavedLayouts] = useState<DashboardLayout[]>([]);
  const [isCustomizing, setIsCustomizing] = useState(false);
  const [selectedWidget, setSelectedWidget] = useState<DashboardWidget | null>(null);
  const [refreshInterval, setRefreshInterval] = useState(5); // seconds
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'error'>('connected');
  const [notifications, setNotifications] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    loadData();
    calculateProductivityScore();
    initializeRealTime();
  }, []);

  // Real-time data refresh effect
  useEffect(() => {
    if (!isRealTimeEnabled || !autoRefresh) return;

    const interval = setInterval(() => {
      refreshRealTimeData();
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [isRealTimeEnabled, autoRefresh, refreshInterval]);

  // Real-time Functions
  const initializeRealTime = () => {
    // Initialize default widgets
    const defaultWidgets: DashboardWidget[] = [
      {
        id: 'live-metrics',
        type: 'metric',
        title: 'Live Metrics',
        size: 'large',
        position: { x: 0, y: 0, w: 6, h: 4 },
        config: {
          dataSource: 'live-metrics',
          refreshInterval: 5,
          showHeader: true,
          showBorder: true,
          theme: 'auto',
          customSettings: {}
        },
        isVisible: true,
        isLocked: false,
        lastUpdated: new Date().toISOString()
      },
      {
        id: 'recent-events',
        type: 'list',
        title: 'Recent Events',
        size: 'medium',
        position: { x: 6, y: 0, w: 6, h: 4 },
        config: {
          dataSource: 'events',
          refreshInterval: 3,
          showHeader: true,
          showBorder: true,
          theme: 'auto',
          customSettings: { maxItems: 5 }
        },
        isVisible: true,
        isLocked: false,
        lastUpdated: new Date().toISOString()
      }
    ];

    setDashboardWidgets(defaultWidgets);
    initializeLiveMetrics();
    loadSavedLayouts();
  };

  const initializeLiveMetrics = () => {
    const initialMetrics: LiveMetric[] = [
      {
        id: 'active-time',
        name: 'Active Time Today',
        value: 0,
        previousValue: 0,
        change: 0,
        changePercent: 0,
        trend: 'stable',
        unit: 'hours',
        format: 'time',
        color: '#3b82f6',
        icon: 'Clock',
        isRealTime: true,
        lastUpdated: new Date().toISOString(),
        history: []
      },
      {
        id: 'tasks-today',
        name: 'Tasks Completed Today',
        value: 0,
        previousValue: 0,
        change: 0,
        changePercent: 0,
        trend: 'stable',
        unit: 'tasks',
        format: 'number',
        color: '#10b981',
        icon: 'CheckCircle',
        isRealTime: true,
        lastUpdated: new Date().toISOString(),
        history: []
      },
      {
        id: 'focus-score',
        name: 'Focus Score',
        value: 85,
        previousValue: 82,
        change: 3,
        changePercent: 3.7,
        trend: 'up',
        unit: '%',
        format: 'percentage',
        color: '#8b5cf6',
        icon: 'Brain',
        isRealTime: true,
        lastUpdated: new Date().toISOString(),
        history: []
      },
      {
        id: 'energy-level',
        name: 'Energy Level',
        value: 78,
        previousValue: 75,
        change: 3,
        changePercent: 4.0,
        trend: 'up',
        unit: '%',
        format: 'percentage',
        color: '#f59e0b',
        icon: 'Zap',
        isRealTime: true,
        lastUpdated: new Date().toISOString(),
        history: []
      }
    ];

    setLiveMetrics(initialMetrics);
  };

  const refreshRealTimeData = () => {
    const now = new Date();
    setLastRefresh(now);

    // Simulate real-time data updates
    setLiveMetrics(prev => prev.map(metric => {
      const randomChange = (Math.random() - 0.5) * 10; // -5 to +5
      const newValue = Math.max(0, Math.min(100, metric.value + randomChange));
      const change = newValue - metric.value;
      const changePercent = metric.value > 0 ? (change / metric.value) * 100 : 0;

      return {
        ...metric,
        previousValue: metric.value,
        value: newValue,
        change,
        changePercent,
        trend: change > 0 ? 'up' : change < 0 ? 'down' : 'stable',
        lastUpdated: now.toISOString(),
        history: [
          ...metric.history.slice(-20), // Keep last 20 points
          { timestamp: now.toISOString(), value: newValue }
        ]
      };
    }));

    // Update real-time data
    setRealTimeData(prev => ({
      ...prev,
      timestamp: now.toISOString(),
      metrics: liveMetrics.reduce((acc, metric) => {
        acc[metric.id] = metric.value;
        return acc;
      }, {} as Record<string, number>)
    }));

    // Simulate random events
    if (Math.random() < 0.1) { // 10% chance of new event
      const events: DashboardEvent[] = [
        {
          id: `event-${Date.now()}`,
          type: 'task_completed',
          title: 'Task Completed',
          description: 'You completed a high-priority task!',
          timestamp: now.toISOString(),
          category: 'productivity',
          importance: 'medium',
          data: {}
        }
      ];

      setRealTimeData(prev => ({
        ...prev,
        events: [...events, ...prev.events.slice(0, 9)] // Keep last 10 events
      }));
    }
  };

  const toggleRealTime = () => {
    setIsRealTimeEnabled(!isRealTimeEnabled);
    if (!isRealTimeEnabled) {
      refreshRealTimeData();
    }
  };

  const manualRefresh = () => {
    refreshRealTimeData();
    calculateProductivityScore();
  };

  const addWidget = (type: DashboardWidget['type']) => {
    const newWidget: DashboardWidget = {
      id: `widget-${Date.now()}`,
      type,
      title: `New ${type.charAt(0).toUpperCase() + type.slice(1)}`,
      size: 'medium',
      position: { x: 0, y: 0, w: 4, h: 3 },
      config: {
        dataSource: type,
        refreshInterval: 5,
        showHeader: true,
        showBorder: true,
        theme: 'auto',
        customSettings: {}
      },
      isVisible: true,
      isLocked: false,
      lastUpdated: new Date().toISOString()
    };

    setDashboardWidgets([...dashboardWidgets, newWidget]);
  };

  const removeWidget = (widgetId: string) => {
    setDashboardWidgets(dashboardWidgets.filter(w => w.id !== widgetId));
  };

  const updateWidget = (widgetId: string, updates: Partial<DashboardWidget>) => {
    setDashboardWidgets(dashboardWidgets.map(w =>
      w.id === widgetId ? { ...w, ...updates, lastUpdated: new Date().toISOString() } : w
    ));
  };

  const saveLayout = (name: string) => {
    const newLayout: DashboardLayout = {
      id: `layout-${Date.now()}`,
      name,
      description: `Custom layout created on ${new Date().toLocaleDateString()}`,
      widgets: [...dashboardWidgets],
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setSavedLayouts([...savedLayouts, newLayout]);
    setCurrentLayout(newLayout);
    localStorage.setItem('dashboard-layouts', JSON.stringify([...savedLayouts, newLayout]));
  };

  const loadLayout = (layout: DashboardLayout) => {
    setDashboardWidgets(layout.widgets);
    setCurrentLayout(layout);
  };

  const loadSavedLayouts = () => {
    const saved = localStorage.getItem('dashboard-layouts');
    if (saved) {
      setSavedLayouts(JSON.parse(saved));
    }
  };

  const loadData = () => {
    // Load saved data or initialize with sample data
    initializeSampleData();
  };

  const initializeSampleData = () => {
    const sampleMetrics: ProductivityMetric[] = [
      {
        id: 'focus-time',
        name: 'Deep Focus Time',
        value: 0,
        target: 8,
        unit: 'hours',
        trend: 'stable',
        changePercentage: 0,
        category: 'time',
        period: 'daily',
        history: []
      },
      {
        id: 'tasks-completed',
        name: 'Tasks Completed',
        value: 0,
        target: 25,
        unit: 'tasks',
        trend: 'stable',
        changePercentage: 0,
        category: 'tasks',
        period: 'weekly',
        history: []
      },
      {
        id: 'goals-progress',
        name: 'Goal Progress',
        value: 0,
        target: 85,
        unit: '%',
        trend: 'stable',
        changePercentage: 0,
        category: 'goals',
        period: 'monthly',
        history: []
      },
      {
        id: 'energy-level',
        name: 'Average Energy',
        value: 0,
        target: 8,
        unit: '/10',
        trend: 'stable',
        changePercentage: 0,
        category: 'health',
        period: 'weekly',
        history: []
      }
    ];

    const sampleKPIs: KPI[] = [
      {
        id: 'productivity-index',
        name: 'Productivity Index',
        description: 'Overall productivity score based on multiple factors',
        value: 0,
        target: 85,
        unit: '%',
        status: 'warning',
        category: 'Overall',
        weight: 0.3,
        benchmarks: { excellent: 90, good: 75, warning: 60 }
      },
      {
        id: 'time-efficiency',
        name: 'Time Efficiency',
        description: 'Ratio of productive time to total time',
        value: 0,
        target: 80,
        unit: '%',
        status: 'warning',
        category: 'Time Management',
        weight: 0.25,
        benchmarks: { excellent: 85, good: 70, warning: 55 }
      },
      {
        id: 'goal-completion-rate',
        name: 'Goal Completion Rate',
        description: 'Percentage of goals completed on time',
        value: 0,
        target: 90,
        unit: '%',
        status: 'warning',
        category: 'Goal Achievement',
        weight: 0.2,
        benchmarks: { excellent: 85, good: 70, warning: 55 }
      },
      {
        id: 'work-life-balance',
        name: 'Work-Life Balance',
        description: 'Balance between work and personal time',
        value: 0,
        target: 80,
        unit: '/100',
        status: 'warning',
        category: 'Wellness',
        weight: 0.15,
        benchmarks: { excellent: 85, good: 70, warning: 55 }
      },
      {
        id: 'learning-velocity',
        name: 'Learning Velocity',
        description: 'Rate of skill acquisition and knowledge growth',
        value: 0,
        target: 80,
        unit: 'points',
        status: 'warning',
        category: 'Growth',
        weight: 0.1,
        benchmarks: { excellent: 80, good: 65, warning: 50 }
      }
    ];

    const sampleAchievements: Achievement[] = [
      // Start with no achievements - users will unlock them through progression
    ];

    setMetrics(sampleMetrics);
    setKPIs(sampleKPIs);
    setAchievements(sampleAchievements);
  };

  const calculateProductivityScore = () => {
    // Calculate overall productivity score based on KPIs
    const weightedScore = kpis.reduce((total, kpi) => {
      const normalizedValue = Math.min(kpi.value / kpi.target, 1.2); // Cap at 120%
      return total + (normalizedValue * kpi.weight * 100);
    }, 0);

    const score: ProductivityScore = {
      overall: Math.round(weightedScore),
      categories: {
        timeManagement: 78,
        taskCompletion: 85,
        goalAchievement: 88,
        healthWellness: 72,
        learningGrowth: 85
      },
      trend: 'improving',
      insights: [
        'Your focus time has improved by 12% this week',
        'Goal completion rate is above target',
        'Work-life balance needs attention',
        'Learning velocity is excellent'
      ],
      recommendations: [
        'Schedule more breaks to improve work-life balance',
        'Consider time-blocking for better focus',
        'Set up automated reminders for goal check-ins',
        'Maintain current learning momentum'
      ]
    };

    setProductivityScore(score);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600 bg-green-100';
      case 'good': return 'text-blue-600 bg-blue-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTrendIcon = (trend: string, changePercentage: number) => {
    if (trend === 'up') return <ArrowUp className="w-4 h-4 text-green-500" />;
    if (trend === 'down') return <ArrowDown className="w-4 h-4 text-red-500" />;
    return <Minus className="w-4 h-4 text-gray-500" />;
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'border-gray-300 bg-gray-50';
      case 'rare': return 'border-blue-300 bg-blue-50';
      case 'epic': return 'border-purple-300 bg-purple-50';
      case 'legendary': return 'border-yellow-300 bg-yellow-50';
      default: return 'border-gray-300 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 p-2 sm:p-4 pb-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-7xl mx-auto"
      >
        {/* Mobile-Optimized Header */}
        <div className="mb-4 sm:mb-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 mb-4">
            <div className="flex-1 min-w-0">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 truncate">📊 Productivity Dashboard</h1>
              <p className="text-sm sm:text-base text-gray-600 mt-1">Real-time productivity tracking and analytics</p>
            </div>
            <div className="flex items-center space-x-2">
              {connectionStatus === 'connected' ? (
                <div className="flex items-center space-x-1 text-green-600">
                  <Wifi className="w-4 h-4" />
                  <span className="text-sm font-medium">Live</span>
                </div>
              ) : (
                <div className="flex items-center space-x-1 text-red-600">
                  <WifiOff className="w-4 h-4" />
                  <span className="text-sm font-medium">Offline</span>
                </div>
              )}
              {isRealTimeEnabled && (
                <div className="flex items-center space-x-1 text-blue-600">
                  <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse" />
                  <span className="text-sm">Real-time</span>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-4 text-gray-600">
            <p>Professional analytics and performance insights</p>
            <span className="text-sm">
              Last updated: {lastRefresh.toLocaleTimeString()}
            </span>
          </div>
        </div>

        <div className="flex flex-wrap items-center space-x-2">
          <Button
            variant={isRealTimeEnabled ? "default" : "outline"}
            onClick={toggleRealTime}
            className={isRealTimeEnabled ? "bg-green-600 hover:bg-green-700" : ""}
          >
            {isRealTimeEnabled ? <Pause className="w-4 h-4 mr-2" /> : <Play className="w-4 h-4 mr-2" />}
            {isRealTimeEnabled ? 'Pause' : 'Start'} Live
          </Button>
          <Button variant="outline" onClick={manualRefresh}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button
            variant="outline"
            onClick={() => setIsCustomizing(!isCustomizing)}
            className={isCustomizing ? "bg-purple-50 border-purple-200" : ""}
          >
            <Layout className="w-4 h-4 mr-2" />
            Customize
          </Button>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className="px-3 py-2 border rounded-lg bg-white"
          >
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
          </select>
          <Button variant="outline">
            <Eye className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Real-time Alerts */}
      <AnimatePresence>
        {realTimeData.alerts.filter(alert => !alert.isRead).length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mb-4"
          >
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Bell className="w-5 h-5 text-orange-600" />
                    <span className="font-medium text-orange-800">
                      {realTimeData.alerts.filter(alert => !alert.isRead).length} new alerts
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setRealTimeData(prev => ({
                      ...prev,
                      alerts: prev.alerts.map(alert => ({ ...alert, isRead: true }))
                    }))}
                  >
                    Mark all as read
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Customization Panel */}
      <AnimatePresence>
        {isCustomizing && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-6"
          >
            <Card className="border-purple-200 bg-purple-50">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Sliders className="w-5 h-5" />
                  <span>Dashboard Customization</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Add Widgets</h4>
                    <div className="space-y-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start"
                        onClick={() => addWidget('metric')}
                      >
                        <BarChart3 className="w-4 h-4 mr-2" />
                        Metric Widget
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start"
                        onClick={() => addWidget('chart')}
                      >
                        <LineChart className="w-4 h-4 mr-2" />
                        Chart Widget
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start"
                        onClick={() => addWidget('timer')}
                      >
                        <Timer className="w-4 h-4 mr-2" />
                        Timer Widget
                      </Button>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Settings</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label>Auto Refresh</Label>
                        <input
                          type="checkbox"
                          checked={autoRefresh}
                          onChange={(e) => setAutoRefresh(e.target.checked)}
                          className="rounded"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label>Notifications</Label>
                        <input
                          type="checkbox"
                          checked={notifications}
                          onChange={(e) => setNotifications(e.target.checked)}
                          className="rounded"
                        />
                      </div>
                      <div>
                        <Label>Refresh Interval (seconds)</Label>
                        <input
                          type="number"
                          min="1"
                          max="60"
                          value={refreshInterval}
                          onChange={(e) => setRefreshInterval(Number(e.target.value))}
                          className="w-full mt-1 px-2 py-1 border rounded"
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Layouts</h4>
                    <div className="space-y-2">
                      {savedLayouts.map((layout) => (
                        <div key={layout.id} className="flex items-center justify-between">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="flex-1 justify-start"
                            onClick={() => loadLayout(layout)}
                          >
                            {layout.name}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSavedLayouts(savedLayouts.filter(l => l.id !== layout.id))}
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => {
                          const name = prompt('Layout name:');
                          if (name) saveLayout(name);
                        }}
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        Save Layout
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Live Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {liveMetrics.map((metric) => (
          <motion.div
            key={metric.id}
            layout
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="relative"
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: metric.color }}
                    />
                    <span className="text-sm font-medium text-gray-600">{metric.name}</span>
                  </div>
                  {metric.isRealTime && (
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  )}
                </div>

                <div className="flex items-end justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-800">
                      {metric.format === 'percentage' ? `${metric.value.toFixed(1)}%` :
                       metric.format === 'time' ? `${metric.value.toFixed(1)}h` :
                       metric.value.toFixed(0)}
                    </div>
                    <div className={`flex items-center space-x-1 text-sm ${
                      metric.trend === 'up' ? 'text-green-600' :
                      metric.trend === 'down' ? 'text-red-600' : 'text-gray-500'
                    }`}>
                      {metric.trend === 'up' ? <ArrowUp className="w-3 h-3" /> :
                       metric.trend === 'down' ? <ArrowDown className="w-3 h-3" /> :
                       <Minus className="w-3 h-3" />}
                      <span>{Math.abs(metric.changePercent).toFixed(1)}%</span>
                    </div>
                  </div>

                  <div className="text-xs text-gray-400">
                    {new Date(metric.lastUpdated).toLocaleTimeString()}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center text-xs md:text-sm">
            <BarChart3 className="w-4 h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Overview</span>
            <span className="sm:hidden">Stats</span>
          </TabsTrigger>
          <TabsTrigger value="time" className="flex items-center text-xs md:text-sm">
            <Clock className="w-4 h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Time Analysis</span>
            <span className="sm:hidden">Time</span>
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center text-xs md:text-sm">
            <Target className="w-4 h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Performance</span>
            <span className="sm:hidden">Perf</span>
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center text-xs md:text-sm">
            <Brain className="w-4 h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Insights</span>
            <span className="sm:hidden">Tips</span>
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab Content */}
        <TabsContent value="overview" className="space-y-6">
          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {kpis.map((kpi, index) => (
              <motion.div
                key={kpi.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    {kpi.name}
                  </h3>
                  <Badge className={getStatusColor(kpi.status)}>
                    {kpi.status}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-gray-800 dark:text-white">
                    {kpi.value} {kpi.unit}
                  </div>
                  <div className="flex items-center text-sm">
                    {kpi.trend === 'up' ? (
                      <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    ) : kpi.trend === 'down' ? (
                      <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
                    ) : (
                      <Minus className="w-4 h-4 text-gray-500 mr-1" />
                    )}
                    <span className={kpi.trend === 'up' ? 'text-green-600' :
                                   kpi.trend === 'down' ? 'text-red-600' : 'text-gray-600'}>
                      {kpi.change}% vs last period
                    </span>
                  </div>
                  <div className="text-xs text-gray-500">
                    Target: {kpi.target} {kpi.unit}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Productivity Score */}
          {productivityScore && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
            >
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                Productivity Score
              </h3>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-1">
                  <div className="text-center">
                    <div className="text-4xl font-bold text-blue-600 mb-2">
                      {productivityScore.overall}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      Overall Score
                    </div>
                    <Badge className={getStatusColor(productivityScore.status)} size="sm">
                      {productivityScore.status}
                    </Badge>
                  </div>
                </div>
                <div className="lg:col-span-2">
                  <div className="grid grid-cols-2 gap-4">
                    {Object.entries(productivityScore.breakdown || {}).map(([key, value]) => (
                      <div key={key} className="text-center">
                        <div className="text-xl font-semibold text-gray-700 dark:text-gray-300">
                          {value}
                        </div>
                        <div className="text-xs text-gray-500 capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Recent Achievements */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
          >
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
              Recent Achievements
            </h3>
            <div className="grid gap-3">
              {achievements.slice(0, 5).map((achievement, index) => (
                <div key={achievement.id} className="flex items-center gap-3 p-3 bg-white/10 rounded-lg">
                  <div className="text-2xl">{achievement.icon}</div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-800 dark:text-white">
                      {achievement.title}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      {achievement.description}
                    </div>
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date(achievement.unlockedAt).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </TabsContent>

        {/* Analytics Tab Content */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="text-center py-12">
            <BarChart3 className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
              Advanced Analytics
            </h3>
            <p className="text-gray-500 mb-4">
              Detailed charts and trend analysis coming soon
            </p>
          </div>
        </TabsContent>

        {/* Performance Tab Content */}
        <TabsContent value="performance" className="space-y-6">
          <div className="text-center py-12">
            <Target className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
              Performance Metrics
            </h3>
            <p className="text-gray-500 mb-4">
              Comprehensive performance tracking and analysis
            </p>
          </div>
        </TabsContent>

        {/* Insights Tab Content */}
        <TabsContent value="insights" className="space-y-6">
          {productivityScore && (
            <div className="grid gap-6">
              {/* Insights */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                  AI Insights
                </h3>
                <div className="space-y-3">
                  {productivityScore.insights.map((insight, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-white/10 rounded-lg">
                      <Brain className="w-5 h-5 text-blue-500 mt-0.5" />
                      <p className="text-gray-700 dark:text-gray-300">{insight}</p>
                    </div>
                  ))}
                </div>
              </motion.div>

              {/* Recommendations */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                  Recommendations
                </h3>
                <div className="space-y-3">
                  {productivityScore.recommendations.map((recommendation, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-white/10 rounded-lg">
                      <Lightbulb className="w-5 h-5 text-yellow-500 mt-0.5" />
                      <p className="text-gray-700 dark:text-gray-300">{recommendation}</p>
                    </div>
                  ))}
                </div>
              </motion.div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </motion.div>
  );
};

export default ProductivityDashboard;
