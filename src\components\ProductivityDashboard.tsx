import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>, CardContent, CardHeader, CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  TrendingUp, TrendingDown, Activity, Clock, Target,
  CheckCircle, AlertCircle, Calendar, BarChart3,
  PieChart, LineChart, Users, Briefcase, Zap,
  Award, Star, Trophy, Crown, Diamond, Flame,
  ArrowUp, ArrowDown, Minus, Eye, Timer,
  Brain, Heart, Dumbbell, Book, Coffee, Lightbulb
} from 'lucide-react';

// Productivity Analytics Interfaces
interface ProductivityMetric {
  id: string;
  name: string;
  value: number;
  target: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  changePercentage: number;
  category: 'time' | 'tasks' | 'goals' | 'health' | 'learning';
  period: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  history: Array<{
    date: string;
    value: number;
  }>;
}

interface KPI {
  id: string;
  name: string;
  description: string;
  value: number;
  target: number;
  unit: string;
  status: 'excellent' | 'good' | 'warning' | 'critical';
  category: string;
  weight: number; // Importance weight for overall score
  formula?: string;
  benchmarks: {
    excellent: number;
    good: number;
    warning: number;
  };
}

interface ProductivityScore {
  overall: number;
  categories: {
    timeManagement: number;
    taskCompletion: number;
    goalAchievement: number;
    healthWellness: number;
    learningGrowth: number;
  };
  trend: 'improving' | 'declining' | 'stable';
  insights: string[];
  recommendations: string[];
}

interface TimeBlock {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  duration: number; // minutes
  category: 'deep-work' | 'meetings' | 'admin' | 'break' | 'learning' | 'exercise';
  productivity: number; // 1-10 scale
  energy: number; // 1-10 scale
  focus: number; // 1-10 scale
  description: string;
  tags: string[];
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: string;
  points: number;
  unlockedAt: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  requirements: string[];
}

const ProductivityDashboard: React.FC = () => {
  // State Management
  const [metrics, setMetrics] = useState<ProductivityMetric[]>([]);
  const [kpis, setKPIs] = useState<KPI[]>([]);
  const [productivityScore, setProductivityScore] = useState<ProductivityScore | null>(null);
  const [timeBlocks, setTimeBlocks] = useState<TimeBlock[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  
  // UI State
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month' | 'quarter'>('week');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [activeTab, setActiveTab] = useState<'overview' | 'time' | 'performance' | 'insights'>('overview');

  useEffect(() => {
    loadData();
    calculateProductivityScore();
  }, []);

  const loadData = () => {
    // Load saved data or initialize with sample data
    initializeSampleData();
  };

  const initializeSampleData = () => {
    const sampleMetrics: ProductivityMetric[] = [
      {
        id: 'focus-time',
        name: 'Deep Focus Time',
        value: 6.5,
        target: 8,
        unit: 'hours',
        trend: 'up',
        changePercentage: 12,
        category: 'time',
        period: 'daily',
        history: []
      },
      {
        id: 'tasks-completed',
        name: 'Tasks Completed',
        value: 23,
        target: 25,
        unit: 'tasks',
        trend: 'up',
        changePercentage: 8,
        category: 'tasks',
        period: 'weekly',
        history: []
      },
      {
        id: 'goals-progress',
        name: 'Goal Progress',
        value: 78,
        target: 85,
        unit: '%',
        trend: 'stable',
        changePercentage: 2,
        category: 'goals',
        period: 'monthly',
        history: []
      },
      {
        id: 'energy-level',
        name: 'Average Energy',
        value: 7.2,
        target: 8,
        unit: '/10',
        trend: 'up',
        changePercentage: 5,
        category: 'health',
        period: 'weekly',
        history: []
      }
    ];

    const sampleKPIs: KPI[] = [
      {
        id: 'productivity-index',
        name: 'Productivity Index',
        description: 'Overall productivity score based on multiple factors',
        value: 82,
        target: 85,
        unit: '%',
        status: 'good',
        category: 'Overall',
        weight: 0.3,
        benchmarks: { excellent: 90, good: 75, warning: 60 }
      },
      {
        id: 'time-efficiency',
        name: 'Time Efficiency',
        description: 'Ratio of productive time to total time',
        value: 76,
        target: 80,
        unit: '%',
        status: 'good',
        category: 'Time Management',
        weight: 0.25,
        benchmarks: { excellent: 85, good: 70, warning: 55 }
      },
      {
        id: 'goal-completion-rate',
        name: 'Goal Completion Rate',
        description: 'Percentage of goals completed on time',
        value: 88,
        target: 90,
        unit: '%',
        status: 'excellent',
        category: 'Goal Achievement',
        weight: 0.2,
        benchmarks: { excellent: 85, good: 70, warning: 55 }
      },
      {
        id: 'work-life-balance',
        name: 'Work-Life Balance',
        description: 'Balance between work and personal time',
        value: 72,
        target: 80,
        unit: '/100',
        status: 'warning',
        category: 'Wellness',
        weight: 0.15,
        benchmarks: { excellent: 85, good: 70, warning: 55 }
      },
      {
        id: 'learning-velocity',
        name: 'Learning Velocity',
        description: 'Rate of skill acquisition and knowledge growth',
        value: 85,
        target: 80,
        unit: 'points',
        status: 'excellent',
        category: 'Growth',
        weight: 0.1,
        benchmarks: { excellent: 80, good: 65, warning: 50 }
      }
    ];

    const sampleAchievements: Achievement[] = [
      {
        id: 'early-bird',
        title: 'Early Bird',
        description: 'Started work before 7 AM for 7 consecutive days',
        icon: '🌅',
        category: 'Time Management',
        points: 100,
        unlockedAt: new Date().toISOString(),
        rarity: 'rare',
        requirements: ['Start work before 7 AM', '7 consecutive days']
      },
      {
        id: 'focus-master',
        title: 'Focus Master',
        description: 'Maintained deep focus for 4+ hours in a single session',
        icon: '🎯',
        category: 'Productivity',
        points: 150,
        unlockedAt: new Date().toISOString(),
        rarity: 'epic',
        requirements: ['4+ hours continuous focus', 'No interruptions']
      },
      {
        id: 'goal-crusher',
        title: 'Goal Crusher',
        description: 'Completed 10 goals ahead of schedule',
        icon: '🏆',
        category: 'Achievement',
        points: 200,
        unlockedAt: new Date().toISOString(),
        rarity: 'legendary',
        requirements: ['Complete 10 goals', 'All ahead of schedule']
      }
    ];

    setMetrics(sampleMetrics);
    setKPIs(sampleKPIs);
    setAchievements(sampleAchievements);
  };

  const calculateProductivityScore = () => {
    // Calculate overall productivity score based on KPIs
    const weightedScore = kpis.reduce((total, kpi) => {
      const normalizedValue = Math.min(kpi.value / kpi.target, 1.2); // Cap at 120%
      return total + (normalizedValue * kpi.weight * 100);
    }, 0);

    const score: ProductivityScore = {
      overall: Math.round(weightedScore),
      categories: {
        timeManagement: 78,
        taskCompletion: 85,
        goalAchievement: 88,
        healthWellness: 72,
        learningGrowth: 85
      },
      trend: 'improving',
      insights: [
        'Your focus time has improved by 12% this week',
        'Goal completion rate is above target',
        'Work-life balance needs attention',
        'Learning velocity is excellent'
      ],
      recommendations: [
        'Schedule more breaks to improve work-life balance',
        'Consider time-blocking for better focus',
        'Set up automated reminders for goal check-ins',
        'Maintain current learning momentum'
      ]
    };

    setProductivityScore(score);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600 bg-green-100';
      case 'good': return 'text-blue-600 bg-blue-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTrendIcon = (trend: string, changePercentage: number) => {
    if (trend === 'up') return <ArrowUp className="w-4 h-4 text-green-500" />;
    if (trend === 'down') return <ArrowDown className="w-4 h-4 text-red-500" />;
    return <Minus className="w-4 h-4 text-gray-500" />;
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'border-gray-300 bg-gray-50';
      case 'rare': return 'border-blue-300 bg-blue-50';
      case 'epic': return 'border-purple-300 bg-purple-50';
      case 'legendary': return 'border-yellow-300 bg-yellow-50';
      default: return 'border-gray-300 bg-gray-50';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen p-4 pb-24 bg-gradient-to-br from-indigo-50 via-white to-cyan-50"
    >
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Productivity Dashboard</h1>
          <p className="text-gray-600">Professional analytics and performance insights</p>
        </div>
        
        <div className="flex flex-wrap items-center space-x-2">
          <select 
            value={selectedPeriod} 
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className="px-3 py-2 border rounded-lg bg-white"
          >
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
          </select>
          <Button variant="outline">
            <Eye className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Productivity Score Card */}
      {productivityScore && (
        <Card className="mb-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">Productivity Score</h2>
                <div className="flex items-center space-x-4">
                  <span className="text-4xl font-bold">{productivityScore.overall}</span>
                  <div className="flex items-center space-x-1">
                    {productivityScore.trend === 'improving' && <TrendingUp className="w-5 h-5" />}
                    {productivityScore.trend === 'declining' && <TrendingDown className="w-5 h-5" />}
                    {productivityScore.trend === 'stable' && <Minus className="w-5 h-5" />}
                    <span className="capitalize">{productivityScore.trend}</span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <Trophy className="w-16 h-16 opacity-80" />
              </div>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mt-6">
              {Object.entries(productivityScore.categories).map(([key, value]) => (
                <div key={key} className="text-center">
                  <div className="text-lg font-semibold">{value}</div>
                  <div className="text-sm opacity-80 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center">
            <BarChart3 className="w-4 h-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="time" className="flex items-center">
            <Clock className="w-4 h-4 mr-2" />
            Time Analysis
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center">
            <Target className="w-4 h-4 mr-2" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center">
            <Brain className="w-4 h-4 mr-2" />
            Insights
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab Content */}
        <TabsContent value="overview" className="space-y-6">
          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {kpis.map((kpi, index) => (
              <motion.div
                key={kpi.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    {kpi.name}
                  </h3>
                  <Badge className={getStatusColor(kpi.status)}>
                    {kpi.status}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-gray-800 dark:text-white">
                    {kpi.value} {kpi.unit}
                  </div>
                  <div className="flex items-center text-sm">
                    {kpi.trend === 'up' ? (
                      <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    ) : kpi.trend === 'down' ? (
                      <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
                    ) : (
                      <Minus className="w-4 h-4 text-gray-500 mr-1" />
                    )}
                    <span className={kpi.trend === 'up' ? 'text-green-600' :
                                   kpi.trend === 'down' ? 'text-red-600' : 'text-gray-600'}>
                      {kpi.change}% vs last period
                    </span>
                  </div>
                  <div className="text-xs text-gray-500">
                    Target: {kpi.target} {kpi.unit}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Productivity Score */}
          {productivityScore && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
            >
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                Productivity Score
              </h3>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-1">
                  <div className="text-center">
                    <div className="text-4xl font-bold text-blue-600 mb-2">
                      {productivityScore.overall}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      Overall Score
                    </div>
                    <Badge className={getStatusColor(productivityScore.status)} size="sm">
                      {productivityScore.status}
                    </Badge>
                  </div>
                </div>
                <div className="lg:col-span-2">
                  <div className="grid grid-cols-2 gap-4">
                    {Object.entries(productivityScore.breakdown).map(([key, value]) => (
                      <div key={key} className="text-center">
                        <div className="text-xl font-semibold text-gray-700 dark:text-gray-300">
                          {value}
                        </div>
                        <div className="text-xs text-gray-500 capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Recent Achievements */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
          >
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
              Recent Achievements
            </h3>
            <div className="grid gap-3">
              {achievements.slice(0, 5).map((achievement, index) => (
                <div key={achievement.id} className="flex items-center gap-3 p-3 bg-white/10 rounded-lg">
                  <div className="text-2xl">{achievement.icon}</div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-800 dark:text-white">
                      {achievement.title}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      {achievement.description}
                    </div>
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date(achievement.unlockedAt).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </TabsContent>

        {/* Analytics Tab Content */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="text-center py-12">
            <BarChart3 className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
              Advanced Analytics
            </h3>
            <p className="text-gray-500 mb-4">
              Detailed charts and trend analysis coming soon
            </p>
          </div>
        </TabsContent>

        {/* Performance Tab Content */}
        <TabsContent value="performance" className="space-y-6">
          <div className="text-center py-12">
            <Target className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
              Performance Metrics
            </h3>
            <p className="text-gray-500 mb-4">
              Comprehensive performance tracking and analysis
            </p>
          </div>
        </TabsContent>

        {/* Insights Tab Content */}
        <TabsContent value="insights" className="space-y-6">
          {productivityScore && (
            <div className="grid gap-6">
              {/* Insights */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                  AI Insights
                </h3>
                <div className="space-y-3">
                  {productivityScore.insights.map((insight, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-white/10 rounded-lg">
                      <Brain className="w-5 h-5 text-blue-500 mt-0.5" />
                      <p className="text-gray-700 dark:text-gray-300">{insight}</p>
                    </div>
                  ))}
                </div>
              </motion.div>

              {/* Recommendations */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                  Recommendations
                </h3>
                <div className="space-y-3">
                  {productivityScore.recommendations.map((recommendation, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-white/10 rounded-lg">
                      <Lightbulb className="w-5 h-5 text-yellow-500 mt-0.5" />
                      <p className="text-gray-700 dark:text-gray-300">{recommendation}</p>
                    </div>
                  ))}
                </div>
              </motion.div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </motion.div>
  );
};

export default ProductivityDashboard;
