
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Heart, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';

interface MoodEntry {
  id: number;
  emoji: string;
  mood: string;
  color: string;
  note: string;
  date: string;
  dateString: string;
}

const MoodTracker = () => {
  const [moods, setMoods] = useState<MoodEntry[]>([]);
  const [showNewMood, setShowNewMood] = useState(false);
  const [selectedMood, setSelectedMood] = useState('');
  const [moodNote, setMoodNote] = useState('');

  const moodOptions = [
    { emoji: '😊', mood: 'Happy', color: '#10B981' },
    { emoji: '😌', mood: 'Calm', color: '#3B82F6' },
    { emoji: '😴', mood: 'Tired', color: '#6B7280' },
    { emoji: '😰', mood: 'Anxious', color: '#F59E0B' },
    { emoji: '😢', mood: 'Sad', color: '#3B82F6' },
    { emoji: '😡', mood: 'Angry', color: '#EF4444' },
    { emoji: '🤩', mood: 'Excited', color: '#8B5CF6' },
    { emoji: '😑', mood: 'Neutral', color: '#6B7280' },
  ];

  useEffect(() => {
    loadMoods();
  }, []);

  const loadMoods = () => {
    const saved = JSON.parse(localStorage.getItem('moods') || '[]') as MoodEntry[];
    setMoods(saved);
  };

  const addMood = () => {
    if (!selectedMood) return;

    const moodData = moodOptions.find(m => m.mood === selectedMood);
    if (!moodData) return;

    const mood: MoodEntry = {
      id: Date.now(),
      ...moodData,
      note: moodNote,
      date: new Date().toISOString(),
      dateString: new Date().toDateString(),
    };

    // Remove any existing mood for today
    const today = new Date().toDateString();
    const filtered = moods.filter(m => new Date(m.date).toDateString() !== today);
    
    const updated = [...filtered, mood];
    setMoods(updated);
    localStorage.setItem('moods', JSON.stringify(updated));
    
    setSelectedMood('');
    setMoodNote('');
    setShowNewMood(false);
  };

  const getTodayMood = () => {
    const today = new Date().toDateString();
    return moods.find(m => new Date(m.date).toDateString() === today);
  };

  const getWeekMoods = () => {
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    
    const weekMoods = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(weekStart);
      day.setDate(weekStart.getDate() + i);
      const dayString = day.toDateString();
      const mood = moods.find(m => new Date(m.date).toDateString() === dayString);
      weekMoods.push({ date: day, mood });
    }
    
    return weekMoods;
  };

  const getMoodStats = () => {
    const lastWeek = moods.filter(m => {
      const moodDate = new Date(m.date);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return moodDate >= weekAgo;
    });

    const moodCounts: Record<string, number> = {};
    lastWeek.forEach(m => {
      moodCounts[m.mood] = (moodCounts[m.mood] || 0) + 1;
    });

    const mostCommon = Object.entries(moodCounts)
      .sort(([,a], [,b]) => (b as number) - (a as number))[0];

    return { 
      totalEntries: lastWeek.length,
      mostCommonMood: mostCommon ? mostCommon[0] : null
    };
  };

  const todayMood = getTodayMood();
  const weekMoods = getWeekMoods();
  const stats = getMoodStats();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen p-4 pb-24"
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Mood Tracker</h1>
        {!todayMood && (
          <Button 
            onClick={() => setShowNewMood(true)}
            className="bg-purple-600 hover:bg-purple-700 rounded-full p-3"
          >
            <Plus className="w-5 h-5" />
          </Button>
        )}
      </div>

      {/* Today's Mood */}
      <Card className="mb-6 shadow-lg border-0 bg-white/70 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Heart className="w-5 h-5 mr-2 text-pink-600" />
            How are you feeling today?
          </CardTitle>
        </CardHeader>
        <CardContent>
          {todayMood ? (
            <div className="text-center">
              <div className="text-6xl mb-4">{todayMood.emoji}</div>
              <h3 className="text-2xl font-semibold text-gray-800 mb-2">{todayMood.mood}</h3>
              {todayMood.note && (
                <p className="text-gray-600 italic">"{todayMood.note}"</p>
              )}
              <Button
                variant="outline"
                onClick={() => setShowNewMood(true)}
                className="mt-4 rounded-full"
              >
                Update Mood
              </Button>
            </div>
          ) : (
            <div className="text-center">
              <p className="text-gray-500 mb-4">Tap to log your mood for today</p>
              <Button 
                onClick={() => setShowNewMood(true)}
                className="bg-purple-600 hover:bg-purple-700 rounded-full"
              >
                Log Mood
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Week Overview */}
      <Card className="mb-6 shadow-lg border-0 bg-white/70 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-lg">This Week</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-7 gap-2">
            {weekMoods.map((day, index) => (
              <div key={index} className="text-center">
                <div className="text-xs text-gray-500 mb-2">
                  {day.date.toLocaleDateString('en-US', { weekday: 'short' })}
                </div>
                <div className="h-12 flex items-center justify-center">
                  {day.mood ? (
                    <span className="text-2xl" title={day.mood.mood}>
                      {day.mood.emoji}
                    </span>
                  ) : (
                    <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <Card className="text-center p-4 shadow-lg border-0 bg-white/70 backdrop-blur-sm">
          <div className="text-2xl font-bold text-purple-600">{stats.totalEntries}</div>
          <div className="text-sm text-gray-600">Entries This Week</div>
        </Card>
        <Card className="text-center p-4 shadow-lg border-0 bg-white/70 backdrop-blur-sm">
          <div className="text-2xl font-bold text-blue-600">
            {stats.mostCommonMood || '-'}
          </div>
          <div className="text-sm text-gray-600">Most Common</div>
        </Card>
      </div>

      {/* New Mood Form */}
      <AnimatePresence>
        {showNewMood && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
          >
            <Card className="w-full max-w-md shadow-2xl border-0 bg-white">
              <CardHeader>
                <CardTitle>How are you feeling?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-4 gap-3">
                  {moodOptions.map((mood) => (
                    <button
                      key={mood.mood}
                      onClick={() => setSelectedMood(mood.mood)}
                      className={`p-4 rounded-xl border-2 transition-all ${
                        selectedMood === mood.mood
                          ? 'border-purple-500 bg-purple-50'
                          : 'border-gray-200 hover:border-purple-300'
                      }`}
                    >
                      <div className="text-3xl mb-2">{mood.emoji}</div>
                      <div className="text-xs font-medium">{mood.mood}</div>
                    </button>
                  ))}
                </div>

                <Textarea
                  placeholder="How are you feeling? (optional)"
                  value={moodNote}
                  onChange={(e) => setMoodNote(e.target.value)}
                  className="rounded-xl border-2 border-gray-200 focus:border-purple-500 resize-none"
                  rows={3}
                />

                <div className="flex space-x-2">
                  <Button 
                    onClick={addMood} 
                    disabled={!selectedMood}
                    className="flex-1 bg-purple-600 hover:bg-purple-700"
                  >
                    Save Mood
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setShowNewMood(false)}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Recent Moods */}
      <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-lg">Mood History</CardTitle>
        </CardHeader>
        <CardContent>
          {moods.length > 0 ? (
            <div className="space-y-3">
              {moods
                .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                .slice(0, 10)
                .map((mood) => (
                  <motion.div
                    key={mood.id}
                    layout
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="text-2xl">{mood.emoji}</div>
                    <div className="flex-1">
                      <div className="font-medium">{mood.mood}</div>
                      {mood.note && (
                        <p className="text-sm text-gray-600 mt-1">"{mood.note}"</p>
                      )}
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <Calendar className="w-3 h-3 mr-1" />
                        {new Date(mood.date).toLocaleDateString()}
                      </div>
                    </div>
                  </motion.div>
                ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-500 mb-2">No moods tracked yet</h3>
              <p className="text-gray-400 mb-4">Start tracking your emotional wellbeing!</p>
              <Button 
                onClick={() => setShowNewMood(true)}
                className="bg-purple-600 hover:bg-purple-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Log First Mood
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default MoodTracker;
