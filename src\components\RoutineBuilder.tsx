
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Clock, Star, Calendar, Play, Pause, Settings, Trash2, Layout, Copy } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';

interface RoutineStep {
  id: number;
  title: string;
  description?: string;
  duration: number;
  isCompleted: boolean;
  type: 'task' | 'break' | 'focus';
}

interface Routine {
  id: number;
  name: string;
  description: string;
  steps: RoutineStep[];
  totalDuration: number;
  isActive: boolean;
  createdAt: string;
  completedSessions: number;
  category: 'morning' | 'work' | 'evening' | 'custom';
}

const RoutineBuilder = () => {
  const [routines, setRoutines] = useState<Routine[]>([]);
  const [showBuilder, setShowBuilder] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [activeRoutine, setActiveRoutine] = useState<Routine | null>(null);
  const [currentRoutine, setCurrentRoutine] = useState<Partial<Routine>>({
    name: '',
    description: '',
    steps: [],
    category: 'custom'
  });
  const [newStep, setNewStep] = useState<Partial<RoutineStep>>({
    title: '',
    description: '',
    duration: 5,
    type: 'task'
  });

  useEffect(() => {
    loadRoutines();
  }, []);

  const loadRoutines = () => {
    const savedRoutines = JSON.parse(localStorage.getItem('routines') || '[]') as Routine[];
    setRoutines(savedRoutines);
  };

  const saveRoutine = () => {
    const routine: Routine = {
      id: currentRoutine.id || Date.now(),
      name: currentRoutine.name || '',
      description: currentRoutine.description || '',
      steps: currentRoutine.steps || [],
      totalDuration: (currentRoutine.steps || []).reduce((sum, step) => sum + step.duration, 0),
      isActive: false,
      createdAt: currentRoutine.createdAt || new Date().toISOString(),
      completedSessions: currentRoutine.completedSessions || 0,
      category: currentRoutine.category || 'custom'
    };

    const updatedRoutines = currentRoutine.id 
      ? routines.map(r => r.id === currentRoutine.id ? routine : r)
      : [...routines, routine];

    setRoutines(updatedRoutines);
    localStorage.setItem('routines', JSON.stringify(updatedRoutines));
    setShowBuilder(false);
    setCurrentRoutine({ name: '', description: '', steps: [], category: 'custom' });
  };

  const addStep = () => {
    if (!newStep.title) return;
    
    const step: RoutineStep = {
      id: Date.now(),
      title: newStep.title,
      description: newStep.description,
      duration: newStep.duration || 5,
      isCompleted: false,
      type: newStep.type || 'task'
    };

    setCurrentRoutine({
      ...currentRoutine,
      steps: [...(currentRoutine.steps || []), step]
    });

    setNewStep({ title: '', description: '', duration: 5, type: 'task' });
  };

  const removeStep = (stepId: number) => {
    setCurrentRoutine({
      ...currentRoutine,
      steps: (currentRoutine.steps || []).filter(step => step.id !== stepId)
    });
  };

  const startRoutine = (routine: Routine) => {
    setActiveRoutine(routine);
    const updatedRoutines = routines.map(r => 
      r.id === routine.id ? { ...r, isActive: true } : { ...r, isActive: false }
    );
    setRoutines(updatedRoutines);
    localStorage.setItem('routines', JSON.stringify(updatedRoutines));
  };

  const stopRoutine = () => {
    if (activeRoutine) {
      const updatedRoutines = routines.map(r => 
        r.id === activeRoutine.id 
          ? { ...r, isActive: false, completedSessions: r.completedSessions + 1 }
          : r
      );
      setRoutines(updatedRoutines);
      localStorage.setItem('routines', JSON.stringify(updatedRoutines));
    }
    setActiveRoutine(null);
  };

  const categoryEmojis = {
    morning: '🌅',
    work: '💼',
    evening: '🌙',
    custom: '⚙️'
  };

  const typeColors = {
    task: 'from-blue-500 to-purple-600',
    break: 'from-green-500 to-teal-500',
    focus: 'from-orange-500 to-red-500'
  };

  // Template Routines
  const templateRoutines: Omit<Routine, 'id' | 'isActive' | 'createdAt' | 'completedSessions'>[] = [
    {
      name: "Energizing Morning",
      description: "Start your day with energy and focus",
      category: "morning",
      totalDuration: 45,
      steps: [
        { id: 1, title: "Wake Up & Stretch", description: "Gentle stretching to wake up your body", duration: 5, isCompleted: false, type: "task" },
        { id: 2, title: "Hydration", description: "Drink a large glass of water", duration: 2, isCompleted: false, type: "task" },
        { id: 3, title: "Meditation", description: "Mindfulness meditation", duration: 10, isCompleted: false, type: "focus" },
        { id: 4, title: "Exercise", description: "Light cardio or yoga", duration: 20, isCompleted: false, type: "task" },
        { id: 5, title: "Healthy Breakfast", description: "Nutritious meal to fuel your day", duration: 8, isCompleted: false, type: "task" }
      ]
    },
    {
      name: "Productive Work Block",
      description: "Deep work session with strategic breaks",
      category: "work",
      totalDuration: 90,
      steps: [
        { id: 1, title: "Review Goals", description: "Check daily priorities", duration: 5, isCompleted: false, type: "task" },
        { id: 2, title: "Deep Work Session 1", description: "Focus on most important task", duration: 25, isCompleted: false, type: "focus" },
        { id: 3, title: "Short Break", description: "Rest and recharge", duration: 5, isCompleted: false, type: "break" },
        { id: 4, title: "Deep Work Session 2", description: "Continue focused work", duration: 25, isCompleted: false, type: "focus" },
        { id: 5, title: "Long Break", description: "Walk, snack, or chat", duration: 15, isCompleted: false, type: "break" },
        { id: 6, title: "Deep Work Session 3", description: "Final focused session", duration: 25, isCompleted: false, type: "focus" }
      ]
    },
    {
      name: "Relaxing Evening",
      description: "Wind down and prepare for restful sleep",
      category: "evening",
      totalDuration: 60,
      steps: [
        { id: 1, title: "Digital Sunset", description: "Turn off screens and devices", duration: 5, isCompleted: false, type: "task" },
        { id: 2, title: "Tidy Up", description: "Clean and organize living space", duration: 15, isCompleted: false, type: "task" },
        { id: 3, title: "Relaxing Bath/Shower", description: "Warm water to relax muscles", duration: 15, isCompleted: false, type: "task" },
        { id: 4, title: "Gratitude Journal", description: "Write 3 things you're grateful for", duration: 10, isCompleted: false, type: "focus" },
        { id: 5, title: "Reading", description: "Read a book or listen to audiobook", duration: 15, isCompleted: false, type: "task" }
      ]
    },
    {
      name: "Quick Energy Boost",
      description: "15-minute routine to recharge during the day",
      category: "custom",
      totalDuration: 15,
      steps: [
        { id: 1, title: "Deep Breathing", description: "4-7-8 breathing technique", duration: 3, isCompleted: false, type: "focus" },
        { id: 2, title: "Desk Stretches", description: "Stretch neck, shoulders, and back", duration: 5, isCompleted: false, type: "task" },
        { id: 3, title: "Hydrate", description: "Drink water and have a healthy snack", duration: 3, isCompleted: false, type: "task" },
        { id: 4, title: "Power Walk", description: "Quick walk around the block or office", duration: 4, isCompleted: false, type: "task" }
      ]
    },
    {
      name: "Study Session",
      description: "Optimized learning routine with spaced breaks",
      category: "work",
      totalDuration: 120,
      steps: [
        { id: 1, title: "Review Previous Material", description: "Quick recap of last session", duration: 10, isCompleted: false, type: "task" },
        { id: 2, title: "Study Block 1", description: "Active learning and note-taking", duration: 25, isCompleted: false, type: "focus" },
        { id: 3, title: "Break", description: "Rest and move around", duration: 5, isCompleted: false, type: "break" },
        { id: 4, title: "Study Block 2", description: "Continue with new material", duration: 25, isCompleted: false, type: "focus" },
        { id: 5, title: "Break", description: "Hydrate and stretch", duration: 5, isCompleted: false, type: "break" },
        { id: 6, title: "Study Block 3", description: "Practice and application", duration: 25, isCompleted: false, type: "focus" },
        { id: 7, title: "Long Break", description: "Meal or longer rest", duration: 15, isCompleted: false, type: "break" },
        { id: 8, title: "Review & Summary", description: "Consolidate learning", duration: 10, isCompleted: false, type: "task" }
      ]
    },
    {
      name: "Creative Flow",
      description: "Routine designed to enhance creativity",
      category: "custom",
      totalDuration: 75,
      steps: [
        { id: 1, title: "Brain Dump", description: "Write down all thoughts and ideas", duration: 10, isCompleted: false, type: "task" },
        { id: 2, title: "Inspiration Gathering", description: "Browse creative content for ideas", duration: 15, isCompleted: false, type: "task" },
        { id: 3, title: "Creative Work", description: "Focus on creative project", duration: 30, isCompleted: false, type: "focus" },
        { id: 4, title: "Movement Break", description: "Dance, walk, or light exercise", duration: 10, isCompleted: false, type: "break" },
        { id: 5, title: "Refine & Polish", description: "Edit and improve your work", duration: 10, isCompleted: false, type: "focus" }
      ]
    }
  ];

  const useTemplate = (template: typeof templateRoutines[0]) => {
    const routine: Routine = {
      ...template,
      id: Date.now(),
      isActive: false,
      createdAt: new Date().toISOString(),
      completedSessions: 0
    };

    setCurrentRoutine(routine);
    setShowTemplates(false);
    setShowBuilder(true);
  };

  if (showBuilder) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="min-h-screen p-4 pb-24 bg-gradient-to-br from-blue-50 via-white to-purple-50"
      >
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-800">
            {currentRoutine.id ? 'Edit Routine' : 'Create Routine'}
          </h1>
          <div className="space-x-2">
            <Button variant="outline" onClick={() => setShowBuilder(false)}>
              Cancel
            </Button>
            <Button onClick={saveRoutine} className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
              Save Routine
            </Button>
          </div>
        </div>

        <div className="space-y-6 max-w-2xl mx-auto">
          {/* Basic Info */}
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-xl">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                placeholder="Routine name..."
                value={currentRoutine.name || ''}
                onChange={(e) => setCurrentRoutine({ ...currentRoutine, name: e.target.value })}
                className="text-lg py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500"
              />
              
              <Textarea
                placeholder="Describe your routine..."
                value={currentRoutine.description || ''}
                onChange={(e) => setCurrentRoutine({ ...currentRoutine, description: e.target.value })}
                className="rounded-xl border-2 border-gray-200 focus:border-purple-500 resize-none"
              />

              <select
                value={currentRoutine.category || 'custom'}
                onChange={(e) => setCurrentRoutine({ ...currentRoutine, category: e.target.value as any })}
                className="w-full p-3 rounded-xl border-2 border-gray-200 focus:border-purple-500"
              >
                <option value="morning">🌅 Morning Routine</option>
                <option value="work">💼 Work Routine</option>
                <option value="evening">🌙 Evening Routine</option>
                <option value="custom">⚙️ Custom Routine</option>
              </select>
            </CardContent>
          </Card>

          {/* Add Steps */}
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-xl">Add Steps</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  placeholder="Step title..."
                  value={newStep.title || ''}
                  onChange={(e) => setNewStep({ ...newStep, title: e.target.value })}
                  className="rounded-xl border-2 border-gray-200 focus:border-purple-500"
                />
                
                <div className="flex space-x-2">
                  <Input
                    type="number"
                    placeholder="Duration (min)"
                    value={newStep.duration || ''}
                    onChange={(e) => setNewStep({ ...newStep, duration: parseInt(e.target.value) || 0 })}
                    className="rounded-xl border-2 border-gray-200 focus:border-purple-500"
                  />
                  
                  <select
                    value={newStep.type || 'task'}
                    onChange={(e) => setNewStep({ ...newStep, type: e.target.value as any })}
                    className="p-2 rounded-xl border-2 border-gray-200 focus:border-purple-500"
                  >
                    <option value="task">Task</option>
                    <option value="break">Break</option>
                    <option value="focus">Focus</option>
                  </select>
                </div>
              </div>
              
              <Input
                placeholder="Description (optional)..."
                value={newStep.description || ''}
                onChange={(e) => setNewStep({ ...newStep, description: e.target.value })}
                className="rounded-xl border-2 border-gray-200 focus:border-purple-500"
              />
              
              <Button onClick={addStep} className="w-full bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600">
                <Plus className="w-4 h-4 mr-2" />
                Add Step
              </Button>
            </CardContent>
          </Card>

          {/* Steps List */}
          {(currentRoutine.steps || []).length > 0 && (
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-xl">Routine Steps</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {(currentRoutine.steps || []).map((step, index) => (
                    <motion.div
                      key={step.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className={`p-4 rounded-xl bg-gradient-to-r ${typeColors[step.type]} text-white flex items-center justify-between`}
                    >
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-bold">#{index + 1}</span>
                          <span className="font-medium">{step.title}</span>
                          <Clock className="w-4 h-4" />
                          <span className="text-sm">{step.duration}min</span>
                        </div>
                        {step.description && (
                          <p className="text-sm opacity-90">{step.description}</p>
                        )}
                      </div>
                      <Button
                        onClick={() => removeStep(step.id)}
                        variant="ghost"
                        size="sm"
                        className="text-white hover:bg-white/20"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </motion.div>
                  ))}
                </div>
                
                <div className="mt-4 p-3 bg-gray-100 rounded-xl">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Total Duration:</span>
                    <span className="font-bold">
                      {(currentRoutine.steps || []).reduce((sum, step) => sum + step.duration, 0)} minutes
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </motion.div>
    );
  }

  if (showTemplates) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="min-h-screen p-4 pb-24 bg-gradient-to-br from-blue-50 via-white to-purple-50"
      >
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-800">Choose a Template</h1>
          <div className="space-x-2">
            <Button variant="outline" onClick={() => setShowTemplates(false)}>
              Back
            </Button>
            <Button
              onClick={() => {
                setShowTemplates(false);
                setShowBuilder(true);
              }}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
            >
              Create Custom
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
          {templateRoutines.map((template, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
              className="group"
            >
              <Card className="h-full shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all cursor-pointer">
                <CardHeader className="pb-3">
                  <div className="flex items-center space-x-3 mb-2">
                    <span className="text-3xl">{categoryEmojis[template.category]}</span>
                    <div>
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <p className="text-sm text-gray-600">{template.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {template.totalDuration}min
                    </span>
                    <span className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {template.steps.length} steps
                    </span>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 mb-4">
                    {template.steps.slice(0, 4).map((step, stepIndex) => (
                      <div key={stepIndex} className="flex items-center text-sm">
                        <div className={`w-3 h-3 rounded-full mr-3 bg-gradient-to-r ${typeColors[step.type]}`}></div>
                        <span className="flex-1">{step.title}</span>
                        <span className="text-gray-500">{step.duration}min</span>
                      </div>
                    ))}
                    {template.steps.length > 4 && (
                      <div className="text-xs text-gray-400 text-center">
                        +{template.steps.length - 4} more steps
                      </div>
                    )}
                  </div>

                  <Button
                    onClick={() => useTemplate(template)}
                    className="w-full bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600"
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    Use This Template
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="text-center mt-8">
          <p className="text-gray-500 mb-4">Don't see what you're looking for?</p>
          <Button
            onClick={() => {
              setShowTemplates(false);
              setShowBuilder(true);
            }}
            variant="outline"
            className="rounded-2xl"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Custom Routine
          </Button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen p-4 pb-24 bg-gradient-to-br from-blue-50 via-white to-purple-50"
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Routine Builder</h1>
          <p className="text-gray-600">Create and manage your daily routines</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => setShowTemplates(true)}
            variant="outline"
            className="rounded-2xl"
          >
            <Layout className="w-4 h-4 mr-2" />
            Templates
          </Button>
          <Button
            onClick={() => setShowBuilder(true)}
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-2xl"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create
          </Button>
        </div>
      </div>

      {/* Active Routine */}
      {activeRoutine && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-6"
        >
          <Card className="shadow-xl border-0 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-xl font-bold mb-1">Active: {activeRoutine.name}</h3>
                  <p className="opacity-90">{activeRoutine.description}</p>
                  <div className="flex items-center space-x-4 mt-2 text-sm">
                    <span className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {activeRoutine.totalDuration}min
                    </span>
                    <span className="flex items-center">
                      <Star className="w-4 h-4 mr-1" />
                      {activeRoutine.completedSessions} sessions
                    </span>
                  </div>
                </div>
                <Button
                  onClick={stopRoutine}
                  variant="outline"
                  className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                >
                  <Pause className="w-4 h-4 mr-2" />
                  Stop
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Routines Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {routines.map((routine) => (
          <motion.div
            key={routine.id}
            layout
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="group"
          >
            <Card className="h-full shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl">{categoryEmojis[routine.category]}</span>
                    <div>
                      <CardTitle className="text-lg">{routine.name}</CardTitle>
                      <p className="text-sm text-gray-600">{routine.description}</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setCurrentRoutine(routine);
                      setShowBuilder(true);
                    }}
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Settings className="w-4 h-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {routine.totalDuration}min
                      </span>
                      <span className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        {routine.steps.length} steps
                      </span>
                    </div>
                    <span className="flex items-center">
                      <Star className="w-4 h-4 mr-1" />
                      {routine.completedSessions}
                    </span>
                  </div>
                  
                  <div className="space-y-1">
                    {routine.steps.slice(0, 3).map((step, index) => (
                      <div key={step.id} className="text-xs text-gray-500 flex items-center">
                        <div className="w-2 h-2 rounded-full bg-purple-400 mr-2"></div>
                        {step.title} ({step.duration}min)
                      </div>
                    ))}
                    {routine.steps.length > 3 && (
                      <div className="text-xs text-gray-400">+{routine.steps.length - 3} more steps</div>
                    )}
                  </div>
                  
                  <Button
                    onClick={() => startRoutine(routine)}
                    disabled={routine.isActive}
                    className="w-full bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    {routine.isActive ? 'Running...' : 'Start Routine'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {routines.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔄</div>
          <h3 className="text-xl font-medium text-gray-500 mb-2">No routines yet</h3>
          <p className="text-gray-400 mb-4">Create your first routine to get started!</p>
          <Button 
            onClick={() => setShowBuilder(true)}
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Routine
          </Button>
        </div>
      )}
    </motion.div>
  );
};

export default RoutineBuilder;
